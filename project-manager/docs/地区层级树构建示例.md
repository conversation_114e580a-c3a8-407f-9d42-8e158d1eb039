# 地区层级树构建示例

## 概述

商机统计接口返回的数据中包含 `parentAreaId` 字段，可以用来构建完整的地区层级树结构。

## 示例数据

假设接口返回以下数据：

```json
[
  {
    "day": "20250320",
    "areaId": "root",
    "areaName": "全国",
    "parentAreaId": null,
    "allNum": 100,
    "projectAddNum": 5
  },
  {
    "day": "20250320",
    "areaId": "north",
    "areaName": "华北地区",
    "parentAreaId": "root",
    "allNum": 60,
    "projectAddNum": 3
  },
  {
    "day": "20250320",
    "areaId": "south",
    "areaName": "华南地区",
    "parentAreaId": "root",
    "allNum": 40,
    "projectAddNum": 2
  },
  {
    "day": "20250320",
    "areaId": "beijing",
    "areaName": "北京",
    "parentAreaId": "north",
    "allNum": 35,
    "projectAddNum": 2
  },
  {
    "day": "20250320",
    "areaId": "tianjin",
    "areaName": "天津",
    "parentAreaId": "north",
    "allNum": 25,
    "projectAddNum": 1
  },
  {
    "day": "20250320",
    "areaId": "guangzhou",
    "areaName": "广州",
    "parentAreaId": "south",
    "allNum": 25,
    "projectAddNum": 1
  },
  {
    "day": "20250320",
    "areaId": "shenzhen",
    "areaName": "深圳",
    "parentAreaId": "south",
    "allNum": 15,
    "projectAddNum": 1
  }
]
```

## 构建地区树的算法

### JavaScript 示例

```javascript
function buildAreaTree(statisticsData) {
  // 按地区ID分组，每个地区只保留一条记录（用于构建树结构）
  const areaMap = new Map();
  statisticsData.forEach(item => {
    if (!areaMap.has(item.areaId)) {
      areaMap.set(item.areaId, {
        areaId: item.areaId,
        areaName: item.areaName,
        parentAreaId: item.parentAreaId,
        children: [],
        data: item // 保存完整的统计数据
      });
    }
  });

  // 构建父子关系
  const rootNodes = [];
  areaMap.forEach(area => {
    if (area.parentAreaId === null || area.parentAreaId === 'root') {
      rootNodes.push(area);
    } else {
      const parent = areaMap.get(area.parentAreaId);
      if (parent) {
        parent.children.push(area);
      }
    }
  });

  return rootNodes;
}

// 使用示例
const tree = buildAreaTree(statisticsData);
console.log(JSON.stringify(tree, null, 2));
```

### Java 示例

```java
public class AreaTreeBuilder {
    
    public static class AreaNode {
        private String areaId;
        private String areaName;
        private String parentAreaId;
        private List<AreaNode> children = new ArrayList<>();
        private BusinessStatisticsVo data;
        
        // 构造函数、getter、setter 省略
    }
    
    public static List<AreaNode> buildAreaTree(List<BusinessStatisticsVo> statisticsData) {
        // 按地区ID分组
        Map<String, AreaNode> areaMap = new HashMap<>();
        
        for (BusinessStatisticsVo item : statisticsData) {
            if (!areaMap.containsKey(item.getAreaId())) {
                AreaNode node = new AreaNode();
                node.setAreaId(item.getAreaId());
                node.setAreaName(item.getAreaName());
                node.setParentAreaId(item.getParentAreaId());
                node.setData(item);
                areaMap.put(item.getAreaId(), node);
            }
        }
        
        // 构建父子关系
        List<AreaNode> rootNodes = new ArrayList<>();
        
        for (AreaNode area : areaMap.values()) {
            if (area.getParentAreaId() == null || "root".equals(area.getParentAreaId())) {
                rootNodes.add(area);
            } else {
                AreaNode parent = areaMap.get(area.getParentAreaId());
                if (parent != null) {
                    parent.getChildren().add(area);
                }
            }
        }
        
        return rootNodes;
    }
}
```

### Python 示例

```python
def build_area_tree(statistics_data):
    # 按地区ID分组
    area_map = {}
    
    for item in statistics_data:
        if item['areaId'] not in area_map:
            area_map[item['areaId']] = {
                'areaId': item['areaId'],
                'areaName': item['areaName'],
                'parentAreaId': item['parentAreaId'],
                'children': [],
                'data': item
            }
    
    # 构建父子关系
    root_nodes = []
    
    for area in area_map.values():
        if area['parentAreaId'] is None or area['parentAreaId'] == 'root':
            root_nodes.append(area)
        else:
            parent = area_map.get(area['parentAreaId'])
            if parent:
                parent['children'].append(area)
    
    return root_nodes

# 使用示例
tree = build_area_tree(statistics_data)
import json
print(json.dumps(tree, indent=2, ensure_ascii=False))
```

## 构建结果

上述算法会生成如下的树形结构：

```
全国 (root)
├── 华北地区 (north)
│   ├── 北京 (beijing)
│   └── 天津 (tianjin)
└── 华南地区 (south)
    ├── 广州 (guangzhou)
    └── 深圳 (shenzhen)
```

## 应用场景

1. **前端树形组件**: 直接用于 Tree、TreeSelect 等组件的数据源
2. **报表展示**: 按层级展示地区统计数据
3. **数据验证**: 验证父子地区的数据汇聚关系
4. **权限控制**: 根据用户权限显示对应的地区层级

## 注意事项

1. **数据一致性**: 确保 parentAreaId 引用的父地区在数据中存在
2. **循环引用**: 避免地区层级中出现循环引用
3. **性能优化**: 对于大量地区数据，考虑使用索引优化查找性能
4. **空值处理**: 正确处理 parentAreaId 为 null 或 "root" 的情况
