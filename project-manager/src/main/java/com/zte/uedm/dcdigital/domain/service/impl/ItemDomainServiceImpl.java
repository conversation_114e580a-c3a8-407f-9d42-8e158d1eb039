package com.zte.uedm.dcdigital.domain.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.system.ResourceDto;
import com.zte.uedm.dcdigital.common.bean.system.ResourceEntityDto;
import com.zte.uedm.dcdigital.common.bean.system.UserRoleDto;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.aggregate.model.*;
import com.zte.uedm.dcdigital.domain.aggregate.repository.*;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.domain.aggregate.model.ItemInfoEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectAreaObj;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectHandoverEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ItemInfoRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectAreaRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectHandoverRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectRepository;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.ItemStageEnums;
import com.zte.uedm.dcdigital.domain.common.enums.OverallProjectStatusEnums;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import com.zte.uedm.dcdigital.domain.service.DeepenDesignDomainService;
import com.zte.uedm.dcdigital.domain.service.ItemDomainService;
import com.zte.uedm.dcdigital.domain.service.ProjectAreaDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProjectConvert;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.project.vo.ItemInfoInnerVo;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ItemDomainServiceImpl implements ItemDomainService {

    @Autowired
    private AuthService authService;

    @Autowired
    private SystemService systemService;

    @Autowired
    private ProjectAreaRepository projectAreaRepository;

    @Autowired
    private ItemInfoRepository itemInfoRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectDeliveryHistoryRepository deliveryHistoryRepository;

    @Autowired
    private ProjectDeliveryRepository deliveryRepository;

    @Autowired
    private ProjectLegacyIssuesRepository legacyIssuesRepository;

    @Autowired
    private ProjectAreaDomainService projectAreaDomainService;

    @Autowired
    private ProjectHandoverRepository projectHandoverRepository;

    @Autowired
    private DeepenDesignDomainService deepenDesignDomainService;

    @Override
    public List<AreaTreeVo> selectAreaItemTree(String areaName) {
        //没有名称查询所有
        List<ProjectAreaObj> areaObjs = projectAreaRepository.selectAreaListByName(areaName);
        log.debug("projectAreaDomainService selectAreaListBy areaObjs:{}",areaObjs);
        List<AreaTreeVo> areaTreeVos = ProjectConvert.INSTANCE.objListToVo(areaObjs);
        for (AreaTreeVo treeVo : areaTreeVos) {
            treeVo.setPathName(treeVo.getPathName().replace(GlobalConstants.DEFAULT_PATH,""));
            treeVo.setAddCheck(treeVo.getAreaLevel().equals(GlobalConstants.TWO));
        }
        return buildTree(areaTreeVos);
    }

    @Override
    public PageVO<ProjectDeliveryHistoryVo> queryDeliveryHistory(PageQueryDto queryDto) {
        Page<ProjectDeliveryHistoryEntity> page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        List<ProjectDeliveryHistoryEntity> entityList = deliveryHistoryRepository.queryDeliveryHistory(queryDto.getId());
        List<ProjectDeliveryHistoryVo> historyVoList = new ArrayList<>();
        entityList.forEach(p->{
            ProjectDeliveryHistoryVo historyVo = new ProjectDeliveryHistoryVo();
            BeanUtils.copyProperties(p, historyVo);
            String allStatus = OverallProjectStatusEnums.getNameByCode(p.getOverallState());
            historyVo.setOverallStateName(I18nUtil.getI18nFromString(allStatus));
            historyVoList.add(historyVo);
        });
        return new PageVO<>(page.getTotal(), historyVoList);
    }

    @Override
    public ProjectDeliveryVo queryDeliveryById(String id) {
        ProjectDeliveryVo deliveryVo = new ProjectDeliveryVo();
        //未编辑时默认为0
        deliveryVo.setId(id);
        deliveryVo.setDeliveryProgress(GlobalConstants.ZERO);
        ProjectDeliveryEntity deliveryEntity = deliveryRepository.queryById(id);
        if (deliveryEntity != null) {
            BeanUtils.copyProperties(deliveryEntity, deliveryVo);
            String projectPd = deliveryEntity.getProjectPd();
            UserVo userinfoById = systemService.getUserinfoById(projectPd);
            if (userinfoById != null) {
                deliveryVo.setProjectPdName(userinfoById.getName());
            }
            //枚举转换
            String nameByCode = ItemStageEnums.getNameByCode(deliveryEntity.getCurrentProjectStage());
            deliveryVo.setCurrentProjectStageName(I18nUtil.getI18nFromString(nameByCode));
            String allStatus = OverallProjectStatusEnums.getNameByCode(deliveryEntity.getOverallState());
            deliveryVo.setOverallStateName(I18nUtil.getI18nFromString(allStatus));
        }
        Optional.of(itemInfoRepository.getItemInfoDetailById(id)).ifPresent(itemInfoEntity -> {
            deliveryVo.setResourceId(itemInfoEntity.getProjectId());
        });

        return deliveryVo;
    }

    @Override
    public PageVO<ProjectLegacyIssuesVo> queryLegacyIssues(PageQueryDto queryDto) {
        Page<ProjectLegacyIssuesEntity> page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        List<ProjectLegacyIssuesEntity> entityList = legacyIssuesRepository.queryLegacyIssues(queryDto.getId());
        if (CollectionUtils.isEmpty(entityList)) {
            return new PageVO<>(0, new ArrayList<>());
        }
        List<ProjectLegacyIssuesVo> historyVoList = new ArrayList<>();
        List<String> userIds = entityList.stream().map(ProjectLegacyIssuesEntity::getResponsiblePerson).distinct().collect(Collectors.toList());
        Map<String, String> userMap = systemService.getUserinfoByIds(userIds).stream().collect(Collectors.toMap(UserVo::getId, UserVo::getName));
        entityList.forEach(p->{
            ProjectLegacyIssuesVo historyVo = new ProjectLegacyIssuesVo();
            BeanUtils.copyProperties(p, historyVo);
            String name = userMap.get(p.getResponsiblePerson());
            if (StringUtils.isNotBlank(name)) {
                historyVo.setResponsiblePersonName(name);
            }
            historyVoList.add(historyVo);
        });
        return new PageVO<>(page.getTotal(), historyVoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editDelivery(ProjectDeliveryDto deliveryDto) {
        //查询项目表
        ItemInfoEntity detailById = itemInfoRepository.getItemInfoDetailById(deliveryDto.getId());
        if (detailById == null) {
            log.error("The project does not exist");
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        //查询项目对应的商机表
        ProjectEntity projectEntity = projectRepository.selectProjectById(detailById.getProjectId());
        if (projectEntity == null) {
            log.error("The business opportunity for the project does not exist");
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        ProjectDeliveryEntity deliveryEntity = deliveryRepository.queryById(deliveryDto.getId());
        //名称+工号展示及保存
        String currentUser = getUserNameAndId();
        String currentDateTime = DateTimeUtils.getCurrentTime();
        boolean isUpdate = true;
        if (deliveryEntity == null) {
            //编辑
            deliveryEntity = new ProjectDeliveryEntity();
            deliveryEntity.setId(deliveryDto.getId());
            deliveryEntity.setCreateTime(currentDateTime);
            deliveryEntity.setCreateBy(currentUser);
            isUpdate = false;
        }
        deliveryEntity.setUpdateBy(currentUser);
        deliveryEntity.setUpdateTime(currentDateTime);
        //更新内容
        UserDto projectPd = deliveryDto.getProjectPd();
        deliveryEntity.setProjectPd(projectPd.getId());
        deliveryEntity.setCurrentProjectStage(deliveryDto.getCurrentProjectStage());
        deliveryEntity.setOverallState(deliveryDto.getOverallState());
        deliveryEntity.setDeliveryProgress(deliveryDto.getDeliveryProgress());
        deliveryEntity.setPacTime(deliveryDto.getPacTime());
        deliveryEntity.setFacTime(deliveryDto.getFacTime());
        deliveryEntity.setProjectDeliveryMilepostProgram(deliveryDto.getProjectDeliveryMilepostProgram());
        deliveryEntity.setWeeklyProjectProgress(deliveryDto.getWeeklyProjectProgress());
        log.info("start updateDelivery isUpdate:{}",isUpdate);
        deliveryRepository.updateDelivery(deliveryEntity);
        addDeliveryHistory(deliveryDto,currentUser,currentDateTime);
        updateResource(projectPd,isUpdate,projectEntity.getId());
        addUser(projectPd);
    }

    private void updateResource(UserDto userDto,boolean isUpdate,String resourceId) {
        ResourceDto resourceDto = new ResourceDto();
        List<UserRoleDto> userRoleDtos = buildUserRoleDto(RoleCodeEnum.PROJECT_PD.getCode(), Collections.singletonList(userDto));
        resourceDto.setUserRoleDtoList(userRoleDtos);
        ResourceEntityDto resourceEntityDto = new ResourceEntityDto();
        resourceEntityDto.setEntityId(resourceId);
        resourceEntityDto.setType(GlobalConstants.PROJECT_TYPE);
        resourceDto.setResourceEntityDto(resourceEntityDto);
        if (isUpdate) {
            systemService.updateResource(resourceDto);
        } else {
            systemService.createResource(resourceDto);
        }
    }
    private void addDeliveryHistory(ProjectDeliveryDto deliveryDto,String currentUser, String currentDateTime) {
        ProjectDeliveryHistoryEntity historyEntity = new ProjectDeliveryHistoryEntity();
        historyEntity.setId(UUID.randomUUID().toString());
        historyEntity.setWeeklyProjectProgress(deliveryDto.getWeeklyProjectProgress());
        historyEntity.setOverallState(deliveryDto.getOverallState());
        historyEntity.setDeliveryProgress(deliveryDto.getDeliveryProgress());
        historyEntity.setProjectId(deliveryDto.getId());
        historyEntity.setUpdateBy(currentUser);
        historyEntity.setUpdateTime(currentDateTime);
        historyEntity.setCreateTime(currentDateTime);
        historyEntity.setCreateBy(currentUser);
        //新增历史记录
        deliveryHistoryRepository.add(historyEntity);
    }

    private void addUser(UserDto userDto) {
        UserVo userVo = new UserVo();
        userVo.setId(userDto.getId());
        userVo.setName(userDto.getName());
        userVo.setEmployeeId(userDto.getId());
        userVo.setEmail(userDto.getEmail());
        userVo.setPhoneNumber(userDto.getPhoneNumber());
        systemService.addUser(userVo);
    }
    private List<UserRoleDto> buildUserRoleDto(String code, List<UserDto> managers) {
        List<UserRoleDto> userRoleDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(managers)) {
            UserRoleDto userRoleDto = new UserRoleDto();
            userRoleDto.setRoleCode(code);
            userRoleDtoList.add(userRoleDto);
        }else {
            for (UserDto manager : managers) {
                UserRoleDto userRoleDto = new UserRoleDto();
                userRoleDto.setId(manager.getId());
                userRoleDto.setRoleCode(code);
                userRoleDto.setName(manager.getName());
                userRoleDto.setPhoneNumber(manager.getPhoneNumber());
                userRoleDto.setEmail(manager.getEmail());
                userRoleDto.setEmployeeId(manager.getId());
                userRoleDtoList.add(userRoleDto);
            }
        }
        return userRoleDtoList;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addLegacyIssues(LegacyIssuesAddDto issuesAddDto) {
        ProjectLegacyIssuesEntity legacyIssuesEntity = new ProjectLegacyIssuesEntity();
        legacyIssuesEntity.setProjectId(issuesAddDto.getProjectId());
        legacyIssuesEntity.setLegacyIssues(issuesAddDto.getLegacyIssues());
        legacyIssuesEntity.setState(issuesAddDto.getState());
        legacyIssuesEntity.setCloseTime(issuesAddDto.getCloseTime());
        legacyIssuesEntity.setId(UUID.randomUUID().toString());
        String currentUserId = getUserNameAndId();
        String currentDateTime = DateTimeUtils.getCurrentTime();
        UserDto responsiblePerson = issuesAddDto.getResponsiblePerson();
        legacyIssuesEntity.setResponsiblePerson(responsiblePerson.getId());
        legacyIssuesEntity.setUpdateBy(currentUserId);
        legacyIssuesEntity.setUpdateTime(currentDateTime);
        legacyIssuesEntity.setCreateTime(currentDateTime);
        legacyIssuesEntity.setCreateBy(currentUserId);
        legacyIssuesRepository.add(legacyIssuesEntity);
        addUser(responsiblePerson);
    }

    /***
     * 获取当前用户的 名称+工号
     * @return 拼接好的名称 + 工号
     */
    private String getUserNameAndId() {
        String userId = authService.getUserId();
        UserVo userinfo = systemService.getUserinfoById(userId);
        //名称+工号展示及保存
        return userinfo.getName() + userId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editLegacyIssues(LegacyIssuesEditDto issuesEditDto) {
        ProjectLegacyIssuesEntity legacyIssuesEntity = legacyIssuesRepository.queryById(issuesEditDto.getId());
        if (legacyIssuesEntity == null) {
           log.error("legacyIssuesEntity is null");
           throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        legacyIssuesEntity.setLegacyIssues(issuesEditDto.getLegacyIssues());
        legacyIssuesEntity.setState(issuesEditDto.getState());
        legacyIssuesEntity.setCloseTime(issuesEditDto.getCloseTime());
        UserDto responsiblePerson = issuesEditDto.getResponsiblePerson();
        legacyIssuesEntity.setResponsiblePerson(responsiblePerson.getId());
        String currentUserId = getUserNameAndId();
        String currentDateTime = DateTimeUtils.getCurrentTime();
        legacyIssuesEntity.setUpdateBy(currentUserId);
        legacyIssuesEntity.setUpdateTime(currentDateTime);
        legacyIssuesRepository.update(legacyIssuesEntity);
        addUser(responsiblePerson);
    }

    @Override
    public void deleteLegacyIssues(String id) {
        legacyIssuesRepository.deleteById(id);
    }

    @Override
    public ItemInfoVo getItemInfoDetailById(String id) {
        ItemInfoVo itemInfoVo=new ItemInfoVo();
        ItemInfoEntity itemEntity = itemInfoRepository.getItemInfoDetailById(id);
        if (itemEntity!=null&&itemEntity.getId()!=null){
            BeanUtils.copyProperties(itemEntity,itemInfoVo);
        }
        return itemInfoVo;
    }

    /* Started by AICoder, pid:00a2eqb3e90ae6f14cec0af7a0e4717aff27e2e0 */
    @Override
    public PageVO<ItemInfoVo> matchingQueryList(ItemQueryDto queryDto) {
        String userId = authService.getUserId();
        List<String> areaIds = systemService.getAreaIdsByUserId(userId, null);
        log.info("areaIds = {}", areaIds);
        List<String> allProjectIds = systemService.getEntityIdsByUserId(userId, GlobalConstants.PROJECT_TYPE);

        if (CollectionUtils.isEmpty(allProjectIds) && CollectionUtils.isEmpty(areaIds)) {
            return new PageVO<>();
        }

        // 筛选出已经转化成项目的商机
        List<ItemInfoInnerVo> allItemInfoIds = getItemInfoDetailListByProjectIds(allProjectIds);
        if (CollectionUtils.isEmpty(allItemInfoIds)) {
            return new PageVO<>();
        }

        allProjectIds = allItemInfoIds.stream().map(ItemInfoInnerVo::getProjectId).collect(Collectors.toList());

        if (StringUtils.isNotEmpty(queryDto.getAreaId())) {
            List<String> allAreaIds = projectAreaDomainService.getAllAreaIds(queryDto.getAreaId());
            log.info("allAreaIds = {}", allAreaIds);
            List<String> stringList = areaIds.stream().filter(allAreaIds::contains).collect(Collectors.toList());
            queryDto.setAreaIds(stringList);

            // 查询当前地区下的商机id
            List<String> projectIds = projectRepository.selectProjectByAreaIds(allAreaIds);
            if (CollectionUtils.isEmpty(projectIds)) {
                return new PageVO<>();
            }
            // 根据商机id筛选出已转化的项目
            List<ItemInfoInnerVo> itemInfoIds = getItemInfoDetailListByProjectIds(projectIds);
            if (CollectionUtils.isEmpty(itemInfoIds)) {
                return new PageVO<>();
            }

            projectIds = itemInfoIds.stream().map(ItemInfoInnerVo::getProjectId).collect(Collectors.toList());
            List<String> haveProjectIds = allProjectIds.stream().filter(projectIds::contains).collect(Collectors.toList());
            queryDto.setProjectIds(haveProjectIds);
        } else {
            queryDto.setAreaIds(areaIds);
            queryDto.setProjectIds(allProjectIds);
        }

        log.info("matchingQueryList queryDto:{}", queryDto);
        return itemInfoRepository.matchingQueryList(queryDto);
    }

    @Override
    public List<ItemInfoInnerVo> getItemInfoDetailListByIds(List<String> ids) {
        List<ItemInfoEntity> itemInfoEntities = itemInfoRepository.getItemInfoDetailListByIds(ids);
        if (CollectionUtils.isEmpty(itemInfoEntities)) {
            return Collections.emptyList();
        }
        return itemInfoEntities.stream().map(this::convertEntityToVo).collect(Collectors.toList());
    }

    @Override
    public List<ItemInfoInnerVo> getItemInfoDetailListByProjectIds(List<String> projectIds) {
        List<ItemInfoEntity> itemInfoEntities = itemInfoRepository.getItemInfoDetailListByProjectIds(projectIds);
        if (CollectionUtils.isEmpty(itemInfoEntities)) {
            return Collections.emptyList();
        }
        return itemInfoEntities.stream().map(this::convertEntityToVo).collect(Collectors.toList());
    }

    /* Started by AICoder, pid:u5a74o4ce507d341447b0b4cd04828399247810f */
    @Override
    public ItemInfoVo queryItemInfoById(String id) {
        // 根据项目id获取绑定的商机id
        ItemInfoEntity itemInfo = itemInfoRepository.getItemInfoDetailById(id);
        if (itemInfo == null) {
            log.error("itemInfo is null, id:{}", id);
            throw new BusinessException(ProjectStatusCode.ITEM_DOES_NOT_EXIST);
        }

        // 根据商机id获取商机详情
        ProjectEntity projectEntity = projectRepository.selectProjectById(itemInfo.getProjectId());
        if (projectEntity == null) {
            log.error("projectEntity is null, itemId:{},projectId:{}", id,itemInfo.getProjectId());
            throw new BusinessException(ProjectStatusCode.BUSINESS_OPPORTUNITIES_DO_NOT_EXIST);
        }

        ItemInfoVo itemInfoVo = new ItemInfoVo();
        itemInfoVo.setId(itemInfo.getId());
        itemInfoVo.setItemName(projectEntity.getName());
        itemInfoVo.setCustomerName(projectEntity.getCustomer());
        itemInfoVo.setProjectStage(projectEntity.getProjectStage());
        itemInfoVo.setSchemeSE(projectEntity.getSchemeSe());
        itemInfoVo.setDescription(projectEntity.getNotes());
        //从深化设计中获取负责人信息
        String designPrincipal=getDesignPrincipalFromDeepDesign(itemInfo.getId());
        itemInfoVo.setDesignPrincipal(designPrincipal);
        // 获取商机交接信息
        ProjectHandoverEntity handoverEntity = projectHandoverRepository.getProjectHandoverInfoByItemId(itemInfo.getId());
        if (handoverEntity == null) {
            log.info("handoverEntity is null, id:{}", id);
            //throw new BusinessException(ProjectStatusCode.HANDOVER_INFO_DOES_NOT_EXIST);
            //这里修改为不抛出异常提示,能获取到哪些项目信息就回显哪些信息
            return itemInfoVo;
        }

        UserVo deliverUserVo = systemService.getUserinfoById(handoverEntity.getDeliverTd());
        UserVo createUserVo = systemService.getUserinfoById(projectEntity.getCreateBy());
        itemInfoVo.setDeliverTD(deliverUserVo.getDisplayText());
        itemInfoVo.setCreateBy(createUserVo.getDisplayText());
        itemInfoVo.setCreateTime(projectEntity.getCreateTime());
        //工程交付信息
        ProjectDeliveryEntity deliveryEntity = deliveryRepository.queryById(id);
        log.info("queryItemInfoById deliveryEntity is null, id:{}", id);
        if (deliveryEntity != null) {
            itemInfoVo.setOverallState(deliveryEntity.getOverallState());
            String allStatus = OverallProjectStatusEnums.getNameByCode(deliveryEntity.getOverallState());
            itemInfoVo.setOverallStateName(I18nUtil.getI18nFromString(allStatus));

        }else {
            itemInfoVo.setOverallStateName(null);
            itemInfoVo.setOverallState(null);
        }

        return itemInfoVo;
    }

    /**
     * 根据项目id从深化设计中获取设计负责人信息
     * @param itemId 项目id
     * */
    private String getDesignPrincipalFromDeepDesign(String itemId) {
        DeepenImplementationVo deepenImplementationVo = deepenDesignDomainService.queryDeepenImplementation(itemId);
        return deepenImplementationVo==null?"":deepenImplementationVo.getDesignDirector();
    }

    /* Ended by AICoder, pid:u5a74o4ce507d341447b0b4cd04828399247810f */

    // 将转换逻辑封装成独立方法
    private ItemInfoInnerVo convertEntityToVo(ItemInfoEntity entity) {
        ItemInfoInnerVo vo = new ItemInfoInnerVo();
        vo.setId(entity.getId());
        vo.setProjectId(entity.getProjectId());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setCreateBy(entity.getCreateBy());
        vo.setCreateName(entity.getCreateName());
        vo.setUpdateBy(entity.getUpdateBy());
        return vo;
    }

    /* Ended by AICoder, pid:00a2eqb3e90ae6f14cec0af7a0e4717aff27e2e0 */

    /**
     * 构建区域树。
     *
     * @param areaTreeVoList 区域树VO列表。
     * @return 构建后的区域树列表。
     */
    private List<AreaTreeVo> buildTree(List<AreaTreeVo> areaTreeVoList){
        List<ProjectVo> projectVos = selectAllItem();
        Map<String, List<ProjectVo>> areaProjectMap = projectVos.stream().collect(Collectors.groupingBy(ProjectVo::getAreaId));
        if (areaTreeVoList == null || areaTreeVoList.isEmpty()) {
            return Collections.emptyList();
        }
        // 1. 创建一个映射，将每个节点的ID映射到其对应的节点对象
        Map<String, AreaTreeVo> idToNodeMap = new HashMap<>();
        for (AreaTreeVo node : areaTreeVoList) {
            idToNodeMap.put(node.getId(), node);
        }
        List<AreaTreeVo> rootNodes = new ArrayList<>();
        for (AreaTreeVo node : areaTreeVoList) {
            String parentId = node.getParentId();
            Integer areaLevel = node.getAreaLevel();
            if (areaLevel.equals(GlobalConstants.ONE)) {
                // 如果节点等级为1，则将其添加到根节点列表中
                rootNodes.add(node);
            }else {
                AreaTreeVo areaTreeVo = idToNodeMap.get(parentId);
                if(areaTreeVo != null){
                    if (areaTreeVo.getChildren() == null) {
                        areaTreeVo.setChildren(new ArrayList<>());
                    }
                    // 将当前节点添加到父节点的子节点列表中
                    areaTreeVo.getChildren().add(node);
                }
                List<ProjectVo> projectVoList = areaProjectMap.get(node.getId());
                if (projectVoList != null) {
                    List<AreaTreeVo> areaTreeOppoVos = buildAreaTreeOppoVos(projectVoList);
                    node.setChildren(areaTreeOppoVos);
                }
            }
        }
        // 返回根节点列表
        rootNodes.sort((o1, o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getAreaName(), o2.getAreaName()));
        rootNodes.forEach(this::sort);
        return rootNodes;
    }

    private List<ProjectVo> selectAllItem() {
        List<ItemInfoEntity> itemInfoEntities = itemInfoRepository.queryAllItem();
        if (CollectionUtils.isEmpty(itemInfoEntities)) {
            return Collections.emptyList();
        }
        //TODO 如项目和商机一张表，查询需要调整
        //找到对应项目
        List<String> allProjectIds = itemInfoEntities.stream().map(ItemInfoEntity::getProjectId).collect(Collectors.toList());
        //使用项目id替换商机id
        Map<String, ItemInfoEntity> itemInfoEntityMap = itemInfoEntities.stream().collect(Collectors.toMap(ItemInfoEntity::getProjectId, p -> p, (p1, p2) -> p1));
        List<ProjectVo> projectVos = projectRepository.selectProject(allProjectIds, null);
        projectVos.forEach(p -> {
            ItemInfoEntity itemInfoEntity = itemInfoEntityMap.get(p.getId());
            if (itemInfoEntity != null) {
                //项目id
                p.setId(itemInfoEntity.getId());
                //商机id
                p.setOpportunityId(itemInfoEntity.getProjectId());
            }
        });

        return projectVos;
    }

    private void sort(AreaTreeVo node) {
        List<AreaTreeVo> children = node.getChildren();
        if (children != null && !children.isEmpty()) {
            children.sort((o1, o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getAreaName(), o2.getAreaName()));
            for (AreaTreeVo child : children) {
                sort(child);
            }
        }
    }
    private List<AreaTreeVo> buildAreaTreeOppoVos(List<ProjectVo> projectVoList) {
        List<AreaTreeVo> areaTreeOppoVos = new ArrayList<>();
        for (ProjectVo projectVo : projectVoList) {
            AreaTreeVo areaTreeVo = new AreaTreeVo();
            areaTreeVo.setId(projectVo.getId());
            //权限角色绑定在原商机上
            areaTreeVo.setResourceId(projectVo.getOpportunityId());
            areaTreeVo.setAreaName(projectVo.getName());
            areaTreeVo.setOppo(true);
            areaTreeOppoVos.add(areaTreeVo);
        }
        return areaTreeOppoVos;
    }
}
