package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.infrastructure.repository.dto.LaunchBiddingDataDto;
import com.zte.uedm.dcdigital.infrastructure.repository.dto.ProjectDataDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机统计数据访问接口
 * 
 * <AUTHOR> Assistant
 */
@Mapper
public interface BusinessStatisticsMapper {

    /**
     * 获取指定地区的所有子地区ID（包括自己）
     * 使用递归查询获取完整的地区层级
     * 
     * @param areaId 地区ID
     * @return 所有子地区ID列表
     */
    List<String> getAllSubAreaIds(@Param("areaId") String areaId);

    /**
     * 统计指定地区列表中截止到指定日期的所有商机总数（不限制阶段）
     *
     * @param areaIds 地区ID列表
     * @param date 截止日期，格式：yyyy-MM-dd
     * @return 商机总数
     */
    Long countAllProjectsByAreaIds(@Param("areaIds") List<String> areaIds, @Param("date") String date);

    /**
     * 统计指定地区列表中截止到指定日期当前处于特定阶段的商机数量
     * 这里需要考虑商机可能会变更阶段，所以要查询截止到指定日期时处于该阶段的商机
     *
     * @param areaIds 地区ID列表
     * @param date 截止日期，格式：yyyy-MM-dd
     * @param projectStage 项目阶段：1-立项阶段，2-标前阶段，3-投标阶段，4-交标阶段
     * @return 特定阶段商机数量
     */
    Long countProjectsByStageAtDate(@Param("areaIds") List<String> areaIds,
                                   @Param("date") String date,
                                   @Param("projectStage") Integer projectStage);

    /**
     * 统计指定地区列表中在指定日期新增的商机数量
     * 
     * @param areaIds 地区ID列表
     * @param date 指定日期，格式：yyyy-MM-dd
     * @return 新增商机数量
     */
    Long countProjectsAddedOnDate(@Param("areaIds") List<String> areaIds, @Param("date") String date);

    /**
     * 统计指定地区列表中在指定日期启动投标的商机数量
     * 通过查询 launch_bidding 表的 create_time 字段
     *
     * @param areaIds 地区ID列表
     * @param date 指定日期，格式：yyyy-MM-dd
     * @return 启动投标商机数量
     */
    Long countProjectsStartedBiddingOnDate(@Param("areaIds") List<String> areaIds, @Param("date") String date);

    /**
     * 批量查询所有项目数据（用于性能优化）
     * 查询截止到指定日期的所有项目数据，用于计算累计统计字段
     * 注意：不限制开始时间，因为需要统计历史所有数据
     *
     * @param endDate 截止日期，格式：yyyy-MM-dd
     * @return 项目数据列表
     */
    List<ProjectDataDto> selectAllProjectsUpToDate(@Param("endDate") String endDate);

    /**
     * 批量查询所有启动投标数据（用于性能优化）
     * 查询指定日期范围内的所有启动投标数据，避免在循环中执行SQL
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @return 启动投标数据列表
     */
    List<LaunchBiddingDataDto> selectAllLaunchBiddingInDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询系统中第一个商机的创建日期
     * 用于当用户不传开始时间时，确定查询的起始日期
     *
     * @return 第一个商机的创建日期，格式：yyyy-MM-dd，如果没有商机则返回null
     */
    String selectFirstProjectCreateDate();
}
