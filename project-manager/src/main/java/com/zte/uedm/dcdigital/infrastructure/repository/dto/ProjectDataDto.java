package com.zte.uedm.dcdigital.infrastructure.repository.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 项目数据DTO，用于存储从数据库查询出的原始项目数据
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Setter
@ToString
public class ProjectDataDto {

    /**
     * 项目ID
     */
    private String id;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 地区ID
     */
    private String areaId;

    /**
     * 项目阶段：1-立项阶段，2-标前阶段，3-投标阶段，4-交标阶段
     */
    private Integer projectStage;

    /**
     * 创建时间（TEXT格式：YYYY-MM-DD HH24:MI:SS）
     */
    private String createTime;

    /**
     * 创建日期（从createTime提取的日期部分：YYYY-MM-DD）
     */
    private String createDate;
}
