package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商机统计查询请求DTO
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Setter
@ToString
public class BusinessStatisticsQueryDto {

    /**
     * 开始日期，格式：20250715
     * 如果不传开始日期但传了结束日期，会以第一个商机的创建时间作为开始日期
     */

    private String startDate;

    /**
     * 结束日期，格式：20250715（可选，如果不传则只查询单个时间点）
     */
    private String endDate;

}
