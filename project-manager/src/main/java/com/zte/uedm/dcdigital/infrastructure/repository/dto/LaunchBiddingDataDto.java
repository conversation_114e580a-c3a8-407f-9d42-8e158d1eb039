package com.zte.uedm.dcdigital.infrastructure.repository.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 启动投标数据DTO，用于存储从数据库查询出的原始启动投标数据
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Setter
@ToString
public class LaunchBiddingDataDto {

    /**
     * 项目ID
     */
    private String id;

    /**
     * 创建时间（TEXT格式：YYYY-MM-DD HH24:MI:SS）
     */
    private String createTime;

    /**
     * 创建日期（从createTime提取的日期部分：YYYY-MM-DD）
     */
    private String createDate;

    /**
     * 项目地区ID（通过JOIN project表获取）
     */
    private String areaId;
}
