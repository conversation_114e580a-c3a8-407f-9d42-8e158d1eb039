package com.zte.uedm.dcdigital.application.statistics.impl;

import com.zte.uedm.dcdigital.application.statistics.BusinessStatisticsService;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectAreaObj;
import com.zte.uedm.dcdigital.domain.service.ProjectAreaDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.dto.LaunchBiddingDataDto;
import com.zte.uedm.dcdigital.infrastructure.repository.dto.ProjectDataDto;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessStatisticsMapper;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 商机统计服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class BusinessStatisticsServiceImpl implements BusinessStatisticsService {

    @Autowired
    private BusinessStatisticsMapper businessStatisticsMapper;

    @Autowired
    private ProjectAreaDomainService projectAreaDomainService;

    @Override
    public List<BusinessStatisticsVo> getAreaBusinessStatistics(BusinessStatisticsQueryDto queryDto) {
        log.info("Starting area business statistics query with params: {}", queryDto);


        try {
            // 1. 生成日期范围列表
            List<String> dateList = generateDateList(queryDto.getStartDate(), queryDto.getEndDate());
            log.debug("Generated date list: {}", dateList);

            // 2. 获取所有地区及其层级关系
            List<ProjectAreaObj> allAreas = getAllAreasWithHierarchy();
            log.debug("Found {} areas", allAreas.size());

            // 3. 一次性查询所有需要的数据（性能优化）
            String endDate = formatDateForQuery(dateList.get(dateList.size() - 1));

            // 确定启动投标数据的查询范围
            String launchBiddingStartDate;
            if (queryDto.getEndDate() == null || queryDto.getEndDate().isEmpty()) {
                // 如果只传了单个日期，启动投标数据也只查询该日期
                launchBiddingStartDate = formatDateForQuery(queryDto.getStartDate());
            } else {
                // 如果传了日期范围，启动投标数据查询整个范围
                launchBiddingStartDate = formatDateForQuery(dateList.get(0));
            }

            log.info("Batch querying data up to {} and launch biddings from {} to {}", endDate, launchBiddingStartDate, endDate);
            // 查询截止到最后日期的所有项目数据（用于累计统计）
            List<ProjectDataDto> allProjects = businessStatisticsMapper.selectAllProjectsUpToDate(endDate);
            // 查询时间范围内的启动投标数据（用于当日统计）
            List<LaunchBiddingDataDto> allLaunchBiddings = businessStatisticsMapper.selectAllLaunchBiddingInDateRange(launchBiddingStartDate, endDate);
            log.info("Found {} projects (up to {}) and {} launch biddings ({} to {})",
                    allProjects.size(), endDate, allLaunchBiddings.size(), launchBiddingStartDate, endDate);

            // 4. 构建地区层级关系映射
            Map<String, List<String>> areaHierarchyMap = buildAreaHierarchyMap(allAreas);

            // 5. 使用并发Stream处理所有日期和地区的组合
            List<BusinessStatisticsVo> result = dateList.parallelStream()
                    .flatMap(date -> allAreas.parallelStream()
                            .map(area -> calculateAreaStatisticsFromData(area, date, allProjects, allLaunchBiddings, areaHierarchyMap)))
                    .collect(Collectors.toList());

            log.info("Generated {} statistics records", result.size());
            return result;

        } catch (Exception e) {
            log.error("Error occurred while generating business statistics", e);
            throw new RuntimeException("Failed to generate business statistics: " + e.getMessage(), e);
        }
    }

    /**
     * 生成日期范围列表
     * 如果只传了结束日期，会以第一个商机的创建时间作为开始日期
     */
    private List<String> generateDateList(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();

        if (endDate == null || endDate.isEmpty()) {
            // 只查询单个时间点
            dateList.add(startDate);
        } else {
            // 生成日期范围
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

            // 如果没有传开始日期，使用第一个商机的创建时间
            String actualStartDate = startDate;
            if (startDate == null || startDate.isEmpty()) {
                String firstProjectDate = businessStatisticsMapper.selectFirstProjectCreateDate();
                if (firstProjectDate != null) {
                    // 将 yyyy-MM-dd 格式转换为 yyyyMMdd 格式
                    actualStartDate = firstProjectDate.replace("-", "");
                    log.info("No start date provided, using first project date: {}", actualStartDate);
                } else {
                    // 如果没有任何商机，使用结束日期作为开始日期
                    actualStartDate = endDate;
                    log.warn("No projects found in system, using end date as start date: {}", actualStartDate);
                }
            }

            LocalDate start = LocalDate.parse(actualStartDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);

            LocalDate current = start;
            while (!current.isAfter(end)) {
                dateList.add(current.format(formatter));
                current = current.plusDays(1);
            }
        }

        return dateList;
    }

    /**
     * 获取所有地区及其层级关系
     */
    private List<ProjectAreaObj> getAllAreasWithHierarchy() {
        // 查询所有地区，传入空的地区ID列表和地区名称
        return projectAreaDomainService.selectAreaListBy(Collections.emptyList(), null);
    }

    /**
     * 构建地区层级关系映射
     * 每个地区ID映射到其所有子地区ID列表（包括自己）
     */
    private Map<String, List<String>> buildAreaHierarchyMap(List<ProjectAreaObj> allAreas) {
        Map<String, List<String>> hierarchyMap = new ConcurrentHashMap<>();

        for (ProjectAreaObj area : allAreas) {
            List<String> subAreaIds = businessStatisticsMapper.getAllSubAreaIds(area.getId());
            hierarchyMap.put(area.getId(), subAreaIds);
        }

        return hierarchyMap;
    }

    /**
     * 基于预查询的数据计算地区统计信息
     */
    private BusinessStatisticsVo calculateAreaStatisticsFromData(ProjectAreaObj area, String date,
                                                                List<ProjectDataDto> allProjects,
                                                                List<LaunchBiddingDataDto> allLaunchBiddings,
                                                                Map<String, List<String>> areaHierarchyMap) {
        BusinessStatisticsVo statistics = new BusinessStatisticsVo();
        statistics.setDay(date);
        statistics.setAreaId(area.getId());
        statistics.setAreaName(area.getAreaName());
        statistics.setParentAreaId(area.getParentId());

        try {
            String formattedDate = formatDateForQuery(date);
            List<String> subAreaIds = areaHierarchyMap.get(area.getId());

            // 过滤出该地区及其子地区的项目数据
            List<ProjectDataDto> areaProjects = allProjects.stream()
                    .filter(project -> subAreaIds.contains(project.getAreaId()))
                    .collect(Collectors.toList());

            // 过滤出该地区及其子地区的启动投标数据
            List<LaunchBiddingDataDto> areaLaunchBiddings = allLaunchBiddings.stream()
                    .filter(bidding -> subAreaIds.contains(bidding.getAreaId()))
                    .collect(Collectors.toList());

            // 1. 统计商机总数（截止到指定日期的所有商机，包括历史数据）
            long allNum = areaProjects.stream()
                    .filter(project -> project.getCreateDate().compareTo(formattedDate) <= 0)
                    .count();
            statistics.setAllNum(allNum);

            // 2. 统计各阶段商机数量（截止到指定日期，当前处于该阶段的商机数量，包括历史数据）
            long bidNum = areaProjects.stream()
                    .filter(project -> project.getCreateDate().compareTo(formattedDate) <= 0 && project.getProjectStage() == 3)
                    .count();
            statistics.setBidNum(bidNum);

            long subBidNum = areaProjects.stream()
                    .filter(project -> project.getCreateDate().compareTo(formattedDate) <= 0 && project.getProjectStage() == 4)
                    .count();
            statistics.setSubBidNum(subBidNum);

            long beforeBidNum = areaProjects.stream()
                    .filter(project -> project.getCreateDate().compareTo(formattedDate) <= 0 && project.getProjectStage() == 2)
                    .count();
            statistics.setBeforeBidNum(beforeBidNum);

            long projectApprovalNum = areaProjects.stream()
                    .filter(project -> project.getCreateDate().compareTo(formattedDate) <= 0 && project.getProjectStage() == 1)
                    .count();
            statistics.setProjectApprovalNum(projectApprovalNum);

            // 3. 统计指定日期当天新增的商机数量（只统计当天，不包括历史数据）
            long projectAddNum = areaProjects.stream()
                    .filter(project -> project.getCreateDate().equals(formattedDate))
                    .count();
            statistics.setProjectAddNum(projectAddNum);

            // 4. 统计指定日期当天启动投标的商机数量（只统计当天，不包括历史数据）
            long projectStartNum = areaLaunchBiddings.stream()
                    .filter(bidding -> bidding.getCreateDate().equals(formattedDate))
                    .count();
            statistics.setProjectStartNum(projectStartNum);

            log.debug("Area {} on {}: allNum={} (cumulative), bidNum={} (cumulative), projectAddNum={} (daily), projectStartNum={} (daily)",
                     area.getId(), date, allNum, bidNum, projectAddNum, projectStartNum);

            return statistics;

        } catch (Exception e) {
            log.error("Error calculating statistics for area: {} on date: {}", area.getId(), date, e);
            // 返回默认值，避免影响整体统计
            return statistics;
        }
    }

    /**
     * 将日期格式从 yyyyMMdd 转换为 yyyy-MM-dd
     * 数据库中存储的时间格式为 TEXT，格式为 'YYYY-MM-DD HH24:MI:SS'
     */
    private String formatDateForQuery(String date) {
        if (date == null || date.length() != 8) {
            throw new IllegalArgumentException("Invalid date format: " + date);
        }

        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate localDate = LocalDate.parse(date, inputFormatter);
            return localDate.format(outputFormatter);
        } catch (Exception e) {
            log.error("Error formatting date: {}", date, e);
            throw new IllegalArgumentException("Invalid date format: " + date, e);
        }
    }
}
