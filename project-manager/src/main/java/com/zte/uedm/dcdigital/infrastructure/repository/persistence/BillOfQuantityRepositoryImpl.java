/* Started by AICoder, pid:302eb96ce7ld9d7140a109cf302bbf90e3f6af5d */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.aggregate.model.BillOfQuantityEntity;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import com.zte.uedm.dcdigital.domain.repository.BillOfQuantityRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.BillOfQuantityConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BillOfQuantityMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BillOfQuantityPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BillOfQuantityQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BillOfQuantityVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BillOfQuantityRepositoryImpl extends ServiceImpl<BillOfQuantityMapper, BillOfQuantityPo> implements BillOfQuantityRepository {
    @Autowired
    private BillOfQuantityMapper billOfQuantityMapper;
    /* Started by AICoder, pid:599c4v572bk2e211410a0ac060ddeb25be397c97 */
    @Override
    public void checkNameCharacteristic(String id, String name, String characteristic, String ascriptionId) {
        /**
         * 检查具有相同名称和特性的记录数。
         *
         * @param id            记录ID，用于排除当前记录
         * @param name          工程量清单名称
         * @param characteristic 特征描述
         * @param ascriptionId  归属项目ID
         * @throws BusinessException 如果存在具有相同名称和特性的记录，抛出业务异常
         */
        Integer n = billOfQuantityMapper.checkNameCharacteristic(id, name, characteristic, ascriptionId);
        if (n > 0) {
            throw new BusinessException(ProjectStatusCode.NAME_AND_CHAR_EXIST);
        }
    }

    @Override
    public BillOfQuantityPo getByNameCharacteristic(String name, String characteristic, String ascriptionId) {
        /**
         * 根据名称、特性和归属ID查询单个工程量清单记录。
         *
         * @param name          工程量清单名称
         * @param characteristic 特征描述
         * @param ascriptionId  归属项目ID
         * @return 符合条件的工程量清单记录
         */
        return billOfQuantityMapper.getByNameCharacteristic(name, characteristic, ascriptionId);
    }
    /* Ended by AICoder, pid:599c4v572bk2e211410a0ac060ddeb25be397c97 */

    @Override
    public void add(BillOfQuantityEntity quantityEntity) {
        BillOfQuantityPo billOfQuantityPo = BillOfQuantityConvert.INSTANCE.entityToPo(quantityEntity);
        baseMapper.insert(billOfQuantityPo);
    }

    @Override
    public BillOfQuantityEntity queryById(String id) {
        BillOfQuantityPo billOfQuantityPo = baseMapper.selectById(id);
        return BillOfQuantityConvert.INSTANCE.poToEntity(billOfQuantityPo);
    }

    @Override
    public void update(BillOfQuantityEntity quantityEntity) {
        BillOfQuantityPo billOfQuantityPo = BillOfQuantityConvert.INSTANCE.entityToPo(quantityEntity);
        baseMapper.updateById(billOfQuantityPo);
    }

    @Override
    public void batchAdd(List<BillOfQuantityEntity> quantityEntities) {
        List<BillOfQuantityPo> billOfQuantityPos = BillOfQuantityConvert.INSTANCE.listEntityToPoList(quantityEntities);
        this.saveBatch(billOfQuantityPos);
    }

    @Override
    public List<BillOfQuantityEntity> queryByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BillOfQuantityPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BillOfQuantityPo::getId, ids);
        List<BillOfQuantityPo> billOfQuantityPos = baseMapper.selectList(queryWrapper);
        return BillOfQuantityConvert.INSTANCE.listPoToEntityList(billOfQuantityPos);
    }

    @Override
    public void batchUpdate(List<BillOfQuantityEntity> billOfQuantityEntityList) {
        if (CollectionUtils.isNotEmpty(billOfQuantityEntityList)) {
            List<BillOfQuantityPo> billOfQuantityPos = BillOfQuantityConvert.INSTANCE.listEntityToPoList(billOfQuantityEntityList);
            this.updateBatchById(billOfQuantityPos);
        }
    }

    @Override
    public void deleteById(String id) {
        baseMapper.deleteById(id);
    }

    @Override
    public void deleteByIds(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            baseMapper.deleteBatchIds(ids);
        }
    }

    @Override
    public List<BillOfQuantityVo> queryByCondition(BillOfQuantityQueryDto queryDto) {
        List<BillOfQuantityPo> billOfQuantityPoList=billOfQuantityMapper.queryByCondition(queryDto);
//        LambdaQueryWrapper<BillOfQuantityPo> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(BillOfQuantityPo::getAscriptionId, queryDto.getId())
//                .like(StringUtils.isNotBlank(queryDto.getName()), BillOfQuantityPo::getName, queryDto.getName())
//                .orderByDesc(BillOfQuantityPo::getName);
//        List<BillOfQuantityPo> billOfQuantityPos = baseMapper.selectList(queryWrapper);
//        return BillOfQuantityConvert.INSTANCE.listPoToVo(billOfQuantityPos);
        return BillOfQuantityConvert.INSTANCE.listPoToVo(billOfQuantityPoList);
    }

    @Override
    public List<BillOfQuantityVo> queryByConditionFilter(BillOfQuantityQueryDto queryDto) {
        List<BillOfQuantityPo> billOfQuantityPoList=billOfQuantityMapper.queryByConditionFilter(queryDto);
        return BillOfQuantityConvert.INSTANCE.listPoToVo(billOfQuantityPoList);
    }

    /* Started by AICoder, pid:9cba9tc2fenc205148e20b21e07ad912c1b51b5f */
    @Override
    public long existSubCategory(String projectId, String productCategoryId) {
        // 如果产品子类ID为空或空白，直接返回0
        if (StringUtils.isBlank(productCategoryId)) {
            return 0;
        }
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<BillOfQuantityPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillOfQuantityPo::getAscriptionId, projectId)
                .eq(BillOfQuantityPo::getProductSubcategory, productCategoryId);

        // 执行查询并返回结果数量
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public void updateOptionsForBills(List<BillOfQuantityEntity> billOfQuantityEntityList) {
        if (CollectionUtils.isNotEmpty(billOfQuantityEntityList)) {
            List<BillOfQuantityPo> billOfQuantityPos = BillOfQuantityConvert.INSTANCE.listEntityToPoList(billOfQuantityEntityList);
            baseMapper.batchUpdateOptionsById(billOfQuantityPos);
        }
    }
    /* Ended by AICoder, pid:9cba9tc2fenc205148e20b21e07ad912c1b51b5f */
}
/* Ended by AICoder, pid:302eb96ce7ld9d7140a109cf302bbf90e3f6af5d */