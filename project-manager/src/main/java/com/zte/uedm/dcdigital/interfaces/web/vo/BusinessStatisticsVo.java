package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商机统计响应VO
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Setter
@ToString
public class BusinessStatisticsVo {

    /**
     * 日期（例：20250620）
     */
    private String day;

    /**
     * 地区ID
     */
    private String areaId;

    /**
     * 地区名称
     */
    private String areaName;

    /**
     * 父地区ID
     */
    private String parentAreaId;

    /**
     * 当前的按区域的商机总数
     */
    private Long allNum;

    /**
     * 处于投标阶段的商机总数
     */
    private Long bidNum;

    /**
     * 处于交标阶段的商机总数
     */
    private Long subBidNum;

    /**
     * 处于标前阶段的商机总数
     */
    private Long beforeBidNum;

    /**
     * 处于立项阶段的商机总数
     */
    private Long projectApprovalNum;

    /**
     * 周期内新增的商机总数
     */
    private Long projectAddNum;

    /**
     * 周期内新增的启动了投标的商机总数
     */
    private Long projectStartNum;

    /**
     * 构造函数，初始化所有数值为0
     */
    public BusinessStatisticsVo() {
        this.allNum = 0L;
        this.bidNum = 0L;
        this.subBidNum = 0L;
        this.beforeBidNum = 0L;
        this.projectApprovalNum = 0L;
        this.projectAddNum = 0L;
        this.projectStartNum = 0L;
    }

    /**
     * 带参数的构造函数
     */
    public BusinessStatisticsVo(String day, String areaId, String areaName, String parentAreaId) {
        this();
        this.day = day;
        this.areaId = areaId;
        this.areaName = areaName;
        this.parentAreaId = parentAreaId;
    }
}
