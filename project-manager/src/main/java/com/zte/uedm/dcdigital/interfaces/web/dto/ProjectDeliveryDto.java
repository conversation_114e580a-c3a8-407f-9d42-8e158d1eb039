/* Started by AICoder, pid:n5e73110024b88e1429a0b86f110481a0665f910 */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@ToString
@Slf4j
public class ProjectDeliveryDto {
    @NotBlank(message = "项目id不能为空")
    private String id;

    @NotNull(message = "项目PD不能为空")
    private UserDto projectPd;

    @NotBlank(message = "当前项目阶段不能为空")
    private String currentProjectStage;

    @NotBlank(message = "项目整体状态不能为空")
    private String overallState;

    @NotNull(message = "项目进度不能为null")
    private Integer deliveryProgress;

    private String pacTime; // 项目验收时间
    private String facTime; // 项目失效时间
    private String projectDeliveryMilepostProgram; // 项目交付里程碑计划
    private String weeklyProjectProgress; // 项目周进度

    /**
     * 验证对象的各个字段是否符合要求。
     *
     * 1. 使用ValidateUtils.validateObj(this)校验bean字段，如果校验失败则抛出BusinessException。
     * 2. 检查projectDeliveryMilepostProgram和weeklyProjectProgress的长度是否超过GlobalConstants.LENGTH_LIMIT（默认1000字符）。
     * 3. 检查deliveryProgress是否在0到100之间。
     * 4. 调用checkFacTime()和checkPacTime()方法分别验证facTime和pacTime的日期格式。
     */
    public void validate() {
        // 校验bean字段
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            throw new BusinessException(StatusCode.INVALID_PARAMETER.getCode(), validResult.getErrorMessage());
        }

        if (StringUtils.isNotBlank(projectDeliveryMilepostProgram) && projectDeliveryMilepostProgram.length() > GlobalConstants.LENGTH_LIMIT) {
            log.error("[ProjectDeliveryDto check param] Invalid parameter: {}", "The project projectDeliveryMilepostProgram cannot be more than 1000 characters");
            throw new BusinessException(ProjectStatusCode.PROJECT_AREA_NAME_LENGTH);
        }

        if (StringUtils.isNotBlank(weeklyProjectProgress) && weeklyProjectProgress.length() > GlobalConstants.LENGTH_LIMIT) {
            log.error("[ProjectDeliveryDto check param] Invalid parameter: {}", "The project weeklyProjectProgress cannot be more than 1000 characters");
            throw new BusinessException(ProjectStatusCode.PROJECT_AREA_NAME_LENGTH);
        }

        if (deliveryProgress > 100 || deliveryProgress < 0) {
            log.error("[ProjectDeliveryDto check param] Invalid parameter: {}", "The project deliveryProgress must be between 0 and 100");
            throw new BusinessException(ProjectStatusCode.PROJECT_AREA_NAME_LENGTH);
        }

        checkFacTime();
        checkPacTime();
    }

    /**
     * 验证facTime的日期格式是否正确。
     *
     * 1. 如果facTime不为空，检查其格式是否为yyyy-MM-dd。
     * 2. 如果格式不正确，记录错误日志并抛出BusinessException。
     * 3. 如果facTime为空，将其设置为StringUtils.EMPTY。
     */
    private void checkFacTime() {
        // 失效日期（日期选择，格式 2024-11-28）
        if (StringUtils.isNotBlank(facTime)) {
            if (!DateTimeUtils.isValidDateFormat(facTime)) {
                log.error("Invalid expiration date:{}, The expiration date format should be yyyy-MM-dd.", facTime);
                throw new BusinessException(StatusCode.INVALID_PARAMETER);
            }
        } else {
            facTime = StringUtils.EMPTY;
        }
    }

    /**
     * 验证pacTime的日期格式是否正确。
     *
     * 1. 如果pacTime不为空，检查其格式是否为yyyy-MM-dd。
     * 2. 如果格式不正确，记录错误日志并抛出BusinessException。
     * 3. 如果pacTime为空，将其设置为StringUtils.EMPTY。
     */
    private void checkPacTime() {
        // 项目验收时间（日期选择，格式 2024-11-28）
        if (StringUtils.isNotBlank(pacTime)) {
            if (!DateTimeUtils.isValidDateFormat(pacTime)) {
                log.error("Invalid expiration date:{}, The expiration date format should be yyyy-MM-dd.", pacTime);
                throw new BusinessException(StatusCode.INVALID_PARAMETER);
            }
        } else {
            pacTime = StringUtils.EMPTY;
        }
    }
}
/* Ended by AICoder, pid:n5e73110024b88e1429a0b86f110481a0665f910 */