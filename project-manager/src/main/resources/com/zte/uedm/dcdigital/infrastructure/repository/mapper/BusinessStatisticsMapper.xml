<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessStatisticsMapper">

    <!-- 递归查询指定地区的所有子地区ID（包括自己） -->
    <select id="getAllSubAreaIds" resultType="java.lang.String">
        WITH RECURSIVE area_tree AS (
            -- 起始节点：指定的地区
            SELECT id, parent_id
            FROM project_area
            WHERE id = #{areaId}
            
            UNION ALL
            
            -- 递归查询：查找所有子地区
            SELECT pa.id, pa.parent_id
            FROM project_area pa
            INNER JOIN area_tree at ON pa.parent_id = at.id
        )
        SELECT DISTINCT id FROM area_tree
    </select>

    <!-- 统计指定地区列表中截止到指定日期的所有商机总数（不限制阶段） -->
    <select id="countAllProjectsByAreaIds" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM project p
        WHERE p.area_id IN
        <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
            #{areaId}
        </foreach>
        AND SUBSTRING(p.create_time, 1, 10) &lt;= #{date}
    </select>

    <!-- 统计指定地区列表中截止到指定日期当前处于特定阶段的商机数量 -->
    <select id="countProjectsByStageAtDate" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM project p
        WHERE p.area_id IN
        <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
            #{areaId}
        </foreach>
        AND p.project_stage = #{projectStage}
        AND SUBSTRING(p.create_time, 1, 10) &lt;= #{date}
    </select>

    <!-- 统计指定地区列表中在指定日期新增的商机数量 -->
    <select id="countProjectsAddedOnDate" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM project p
        WHERE p.area_id IN
        <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
            #{areaId}
        </foreach>
        AND SUBSTRING(p.create_time, 1, 10) = #{date}
    </select>

    <!-- 统计指定地区列表中在指定日期启动投标的商机数量 -->
    <select id="countProjectsStartedBiddingOnDate" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM launch_bidding lb
        INNER JOIN project p ON lb.id = p.id
        WHERE p.area_id IN
        <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
            #{areaId}
        </foreach>
        AND SUBSTRING(lb.create_time, 1, 10) = #{date}
    </select>

    <!-- 批量查询所有项目数据（用于性能优化） -->
    <!-- 注意：这里不能限制开始时间，因为需要统计截止到指定日期的所有历史数据 -->
    <select id="selectAllProjectsUpToDate" resultType="com.zte.uedm.dcdigital.infrastructure.repository.dto.ProjectDataDto">
        SELECT
            p.id,
            p.name,
            p.area_id as areaId,
            p.project_stage as projectStage,
            p.create_time as createTime,
            SUBSTRING(p.create_time, 1, 10) as createDate
        FROM project p
        WHERE SUBSTRING(p.create_time, 1, 10) &lt;= #{endDate}
        ORDER BY p.create_time
    </select>

    <!-- 批量查询所有启动投标数据（用于性能优化） -->
    <select id="selectAllLaunchBiddingInDateRange" resultType="com.zte.uedm.dcdigital.infrastructure.repository.dto.LaunchBiddingDataDto">
        SELECT
            lb.id,
            lb.create_time as createTime,
            SUBSTRING(lb.create_time, 1, 10) as createDate,
            p.area_id as areaId
        FROM launch_bidding lb
        INNER JOIN project p ON lb.id = p.id
        WHERE SUBSTRING(lb.create_time, 1, 10) BETWEEN #{startDate} AND #{endDate}
        ORDER BY lb.create_time
    </select>

    <!-- 查询系统中第一个商机的创建日期 -->
    <select id="selectFirstProjectCreateDate" resultType="java.lang.String">
        SELECT SUBSTRING(p.create_time, 1, 10) as createDate
        FROM project p
        ORDER BY p.create_time ASC
        LIMIT 1
    </select>

</mapper>
