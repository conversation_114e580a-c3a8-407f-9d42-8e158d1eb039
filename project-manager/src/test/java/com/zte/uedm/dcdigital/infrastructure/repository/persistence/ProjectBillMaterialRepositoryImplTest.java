package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.aggregate.model.BillAssociationCountEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectBillMaterialEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectBillMaterialRelMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectBillMaterialPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssociationDto;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class ProjectBillMaterialRepositoryImplTest {
    @InjectMocks
    private ProjectBillMaterialRepositoryImpl projectBillMaterialRepository;
    @Mock
    private ProjectBillMaterialRelMapper projectBillMaterialRelMapper;
    @Mock
    private AuthService authService;

    @BeforeEach
    void setUp() {
    }

    @Test
    void selectAssociatedMaterials() {
        ReflectUtil.setFieldValue(projectBillMaterialRepository, "baseMapper", projectBillMaterialRelMapper);
        Mockito.when(projectBillMaterialRelMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new ProjectBillMaterialPo()));
        List<ProjectBillMaterialEntity> resultList = projectBillMaterialRepository.selectAssociatedMaterials("1");
        Assertions.assertEquals(1, resultList.size());
    }

    @Test
    void selectPagingAssociatedMaterials() {
        ReflectUtil.setFieldValue(projectBillMaterialRepository, "baseMapper", projectBillMaterialRelMapper);
        Mockito.when(projectBillMaterialRelMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new ProjectBillMaterialPo()));
        PageVO<ProjectBillMaterialEntity> pageVO = projectBillMaterialRepository.selectPagingAssociatedMaterials("billId", 1, 10);
        Assertions.assertEquals(1, pageVO.getList().size());
    }

    @Test
    void countAssociatedMaterialNum() {
        ReflectUtil.setFieldValue(projectBillMaterialRepository, "baseMapper", projectBillMaterialRelMapper);
        Mockito.when(projectBillMaterialRelMapper.countAssociatedMaterialNum(Mockito.any())).thenReturn(Collections.singletonList(new BillAssociationCountEntity()));
        List<BillAssociationCountEntity> resultList = projectBillMaterialRepository.countAssociatedMaterialNum(Collections.singletonList("billId"));
        Assertions.assertEquals(1, resultList.size());
    }

    @Test
    void save() {
        MaterialAssociationDto dto = new MaterialAssociationDto();
        dto.setBillId("billId");
        dto.setSelectedMaterials(Collections.singletonList("materialId"));
        Mockito.when(projectBillMaterialRelMapper.delete(Mockito.any())).thenReturn(1);
        try (MockedStatic<SqlHelper> sqlHelperMockedStatic = Mockito.mockStatic(SqlHelper.class)) {
            sqlHelperMockedStatic.when(() -> SqlHelper.executeBatch(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any())).thenReturn(true);
            boolean result = projectBillMaterialRepository.save(dto);
            Assertions.assertTrue(result);
        }
    }

    @Test
    void deleteByBillId() {
        Mockito.when(projectBillMaterialRelMapper.delete(Mockito.any())).thenReturn(1);
        boolean result = projectBillMaterialRepository.deleteByBillId("billId");
        Assertions.assertTrue(result);
    }

    @Test
    void deleteByBillIds() {
        Mockito.when(projectBillMaterialRelMapper.delete(Mockito.any())).thenReturn(1);
        boolean result = projectBillMaterialRepository.deleteByBillIds(Collections.singletonList("billId"));
        Assertions.assertTrue(result);
    }

    @Test
    void deleteByProjectId() {
        Mockito.when(projectBillMaterialRelMapper.delete(Mockito.any())).thenReturn(1);
        boolean result = projectBillMaterialRepository.deleteByProjectId("projectId");
        Assertions.assertTrue(result);
    }

    @Test
    void selectAllAssociatedMaterials() {
        ReflectUtil.setFieldValue(projectBillMaterialRepository, "baseMapper", projectBillMaterialRelMapper);
        Mockito.when(projectBillMaterialRelMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new ProjectBillMaterialPo()));
        PageVO<ProjectBillMaterialEntity> pageVO = projectBillMaterialRepository.selectAllAssociatedMaterials("billId", 1, 10);
        Assertions.assertEquals(1, pageVO.getList().size());
    }
}