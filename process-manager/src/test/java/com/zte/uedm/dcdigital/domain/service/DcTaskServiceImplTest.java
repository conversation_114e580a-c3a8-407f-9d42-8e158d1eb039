package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.constant.TaskConstants;
import com.zte.uedm.dcdigital.domain.common.enums.ApprovalStatusEnum;
import com.zte.uedm.dcdigital.domain.common.enums.ProcessStatusEnum;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalCommentEntity;
import com.zte.uedm.dcdigital.domain.model.process.TaskBo;
import com.zte.uedm.dcdigital.domain.service.impl.DcTaskServiceImpl;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.runtime.ChangeActivityStateBuilder;
import org.flowable.engine.runtime.ExecutionQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceQuery;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class DcTaskServiceImplTest {
    /* Started by AICoder, pid:f86a842f4bcbd4e140490a9c70562e4934864afd */
    @InjectMocks
    private DcTaskServiceImpl dcTaskService;

    @Mock
    private TaskService taskService;

    @Mock
    private AuthService authService;

    @Mock
    private RepositoryService repositoryService;

    @Mock
    private IdentityService identityService;

    @Mock
    private TaskQuery taskQuery;

    @Mock
    private Task task1;

    @Mock
    private Task task2;

    @Mock
    private ProcessInstance processInstance;

    @Mock
    private ApprovalService approvalService;

    @Mock
    private RuntimeService runtimeService;

    @Mock
    private ApprovalCommentService approvalCommentService;

    @Mock
    private HistoryService historyService;

    @Mock
    private SystemService systemService;

    private Map<String, Object> variables = new HashMap<>();

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /* Ended by AICoder, pid:f86a842f4bcbd4e140490a9c70562e4934864afd */

    /* Started by AICoder, pid:586a822f4bzbd4e140490a9c70562e5934834afd */
    @Test
    public void testStartFistTask() throws Exception {

        when(processInstance.getId()).thenReturn("processInstanceId");
        List<Task> tasks = Arrays.asList(task1, task2);
        when(taskService.createTaskQuery()).thenReturn(taskQuery);
        when(taskQuery.processInstanceId("processInstanceId")).thenReturn(taskQuery);
        when(taskQuery.list()).thenReturn(tasks);

        when(task1.getAssignee()).thenReturn("initiatorId");
        when(task2.getAssignee()).thenReturn("otherUserId");

        variables.put(TaskConstants.PROCESS_INITIATOR, "initiatorId");
        when(authService.getUserId()).thenReturn("userId");
        UserVo userVo = new UserVo();
        when(systemService.getUserinfoById("userId")).thenReturn(userVo);
        dcTaskService.startFistTask(processInstance, variables);
        verify(taskService).complete(eq(task1.getId()), eq(variables));
    }

    @Test
    public void testCompleteTaskSuccess() {
        Task task = mock(Task.class);
        when(task.getId()).thenReturn("taskId");
        when(task.getProcessInstanceId()).thenReturn("processInstanceId");
        when(task.isSuspended()).thenReturn(false);
        when(task.getDelegationState()).thenReturn(null);

        TaskQuery taskQuery = mock(TaskQuery.class);
        when(taskService.createTaskQuery()).thenReturn(taskQuery);
        when(taskQuery.taskId("taskId")).thenReturn(taskQuery);
        when(taskQuery.singleResult()).thenReturn(task);

        when(authService.getUserId()).thenReturn("userId");

        BpmnModel bpmnModel = mock(BpmnModel.class);
        when(repositoryService.getBpmnModel(task.getProcessDefinitionId())).thenReturn(bpmnModel);
        FlowElement flowElement = mock(UserTask.class);
        when(bpmnModel.getFlowElement(task.getTaskDefinitionKey())).thenReturn(flowElement);
        List<SequenceFlow> outgoingFlows = Arrays.asList(mock(SequenceFlow.class));
        when(((UserTask) flowElement).getOutgoingFlows()).thenReturn(outgoingFlows);
        EndEvent endEvent = mock(EndEvent.class);
        when(bpmnModel.getFlowElement(outgoingFlows.get(0).getTargetRef())).thenReturn(endEvent);
        when(approvalService.updateApprovalStatus(anyString(), anyInt())).thenReturn(true);
        when(runtimeService.getVariable(task.getProcessInstanceId(), ProcessConstants.PROCESS_STATUS_KEY)).thenReturn(ProcessStatusEnum.RUNNING.getStatus());
        ApprovalCommentEntity commentEntity = new ApprovalCommentEntity();
        when(approvalCommentService.addComment(commentEntity)).thenReturn(true);
        doNothing().when(taskService).complete(anyString(), anyMap());
        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId("taskId");
        taskBo.setResult(TaskConstants.TASK_APPROVAL);
        taskBo.setComment("批准通过");
        boolean result = dcTaskService.complete(taskBo);
        assertTrue(result);
    }
    /* Ended by AICoder, pid:586a822f4bzbd4e140490a9c70562e5934834afd */


    /* Started by AICoder, pid:v86a8c2f4bbbd4e140490a9c70562e3934814afd */
    @Test
    public void testCompleteTaskNull(){
        TaskQuery taskQuery = mock(TaskQuery.class);
        when(taskService.createTaskQuery()).thenReturn(taskQuery);
        when(taskQuery.taskId("taskId")).thenReturn(taskQuery);
        when(taskQuery.singleResult()).thenReturn(null);
        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId("taskId");
        taskBo.setResult(TaskConstants.TASK_APPROVAL);
        taskBo.setComment("批准通过");
        assertThrows(BusinessException.class, () -> {
            dcTaskService.complete(taskBo);
        });
    }

    @Test
    public void testCompleteTaskSuspended(){
        Task task = mock(Task.class);
        when(task.isSuspended()).thenReturn(true);
        when(taskService.createTaskQuery()).thenReturn(taskQuery);
        when(taskQuery.taskId("taskId")).thenReturn(taskQuery);
        when(taskQuery.singleResult()).thenReturn(task);
        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId("taskId");
        taskBo.setResult(TaskConstants.TASK_APPROVAL);
        taskBo.setComment("批准通过");
        assertThrows(BusinessException.class, () -> {
            dcTaskService.complete(taskBo);
        });
    }

    /* Ended by AICoder, pid:v86a8c2f4bbbd4e140490a9c70562e3934814afd */

    /* Started by AICoder, pid:086a892f4b7bd4e140490a9c70562e2934824afd */
    @Test
    public void testCompleteTaskWithDelegation() {
        Task task = mock(Task.class);
        when(task.getId()).thenReturn("taskId");
        when(task.getProcessInstanceId()).thenReturn("processInstanceId");
        when(task.isSuspended()).thenReturn(false);
        when(task.getDelegationState()).thenReturn(DelegationState.PENDING);

        when(taskService.createTaskQuery()).thenReturn(taskQuery);
        when(taskQuery.taskId("taskId")).thenReturn(taskQuery);
        when(taskQuery.singleResult()).thenReturn(task);

        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId("taskId");
        taskBo.setComment("转派给其他人");

        // 执行任务完成
        boolean result = dcTaskService.complete(taskBo);
        assertTrue(result);

    }

    /* Ended by AICoder, pid:086a892f4b7bd4e140490a9c70562e2934824afd */

    /* Started by AICoder, pid:a86a8r2f4bpbd4e140490a9c70562e2934884afd */
    @Test
    public void testCompleteTaskWithApproval(){
        Task task = mock(Task.class);
        when(task.getId()).thenReturn("taskId");
        when(task.getProcessInstanceId()).thenReturn("processInstanceId");
        when(task.isSuspended()).thenReturn(false);
        when(task.getDelegationState()).thenReturn(null);

        when(taskService.createTaskQuery()).thenReturn(taskQuery);
        when(taskQuery.taskId("taskId")).thenReturn(taskQuery);
        when(taskQuery.singleResult()).thenReturn(task);

        when(authService.getUserId()).thenReturn("userId");

        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId("taskId");
        taskBo.setResult(TaskConstants.TASK_APPROVAL);
        taskBo.setComment("批准通过");
        BpmnModel bpmnModel = mock(BpmnModel.class);
        when(repositoryService.getBpmnModel(task.getProcessDefinitionId())).thenReturn(bpmnModel);
        FlowElement flowElement = mock(UserTask.class);
        EndEvent endEvent = mock(EndEvent.class);
        when(bpmnModel.getFlowElement(task.getTaskDefinitionKey())).thenReturn(flowElement,endEvent);
        List<SequenceFlow> outgoingFlows = Arrays.asList(mock(SequenceFlow.class));
        when(((UserTask) flowElement).getOutgoingFlows()).thenReturn(outgoingFlows);
        boolean result = dcTaskService.complete(taskBo);
        assertTrue(result);
    }
    /* Ended by AICoder, pid:a86a8r2f4bpbd4e140490a9c70562e2934884afd */

    /* Started by AICoder, pid:t86a832f4bzbd4e140490a9c70562e2934884afd */
    @Test
    public void testCompleteTaskWithReject(){
        Task task = mock(Task.class);
        when(task.getId()).thenReturn("taskId");
        when(task.getProcessInstanceId()).thenReturn("processInstanceId");
        when(task.isSuspended()).thenReturn(false);
        when(task.getDelegationState()).thenReturn(null);

        when(taskService.createTaskQuery()).thenReturn(taskQuery);
        when(taskQuery.taskId("taskId")).thenReturn(taskQuery);
        when(taskQuery.singleResult()).thenReturn(task);

        when(authService.getUserId()).thenReturn("userId");

        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId("taskId");
        taskBo.setResult(TaskConstants.TASK_REJECT);
        taskBo.setComment("批准通过");
        BpmnModel bpmnModel = mock(BpmnModel.class);
        when(repositoryService.getBpmnModel(task.getProcessDefinitionId())).thenReturn(bpmnModel);
        FlowElement flowElement = mock(UserTask.class);
        EndEvent endEvent = mock(EndEvent.class);
        when(bpmnModel.getFlowElement(task.getTaskDefinitionKey())).thenReturn(flowElement,endEvent);
        List<SequenceFlow> outgoingFlows = Arrays.asList(mock(SequenceFlow.class));
        when(((UserTask) flowElement).getOutgoingFlows()).thenReturn(outgoingFlows);
        boolean result = dcTaskService.complete(taskBo);
        assertTrue(result);
    }
    /* Ended by AICoder, pid:t86a832f4bzbd4e140490a9c70562e2934884afd */

    /* Started by AICoder, pid:o86a852f4b2bd4e140490a9c70562e5934874afd */
    @Test
    public void testStopProcessSuccess() {
        List<Task> taskList = new ArrayList<>();
        Task task1 = mock(Task.class);
        when(task1.getId()).thenReturn("taskId1");
        when(task1.getProcessInstanceId()).thenReturn("processInstanceId");
        taskList.add(task1);

        TaskQuery taskQuery = mock(TaskQuery.class);
        when(taskQuery.processInstanceId("processInstanceId")).thenReturn(taskQuery); // 返回自身以支持链式调用
        when(taskQuery.list()).thenReturn(taskList);

        when(taskService.createTaskQuery()).thenReturn(taskQuery);

        when(authService.getUserId()).thenReturn("userId");
        when(systemService.getUserinfoById("userId")).thenReturn(new UserVo());

        ProcessInstance processInstance = mock(ProcessInstance.class);
        when(processInstance.getProcessInstanceId()).thenReturn("processInstanceId");
        when(processInstance.getProcessDefinitionId()).thenReturn("processDefinitionId");

        ProcessInstanceQuery processInstanceQuery = mock(ProcessInstanceQuery.class);
        when(processInstanceQuery.processInstanceId("processInstanceId")).thenReturn(processInstanceQuery);
        when(processInstanceQuery.singleResult()).thenReturn(processInstance);
        when(runtimeService.createProcessInstanceQuery()).thenReturn(processInstanceQuery);

        BpmnModel bpmnModel = mock(BpmnModel.class);
        when(repositoryService.getBpmnModel("processDefinitionId")).thenReturn(bpmnModel);

        Process process = mock(Process.class);
        when(bpmnModel.getMainProcess()).thenReturn(process);

        EndEvent endNode = mock(EndEvent.class);
        when(endNode.getId()).thenReturn("endNodeId");
        List<EndEvent> endNodes = new ArrayList<>();
        endNodes.add(endNode);
        when(process.findFlowElementsOfType(EndEvent.class, false)).thenReturn(endNodes);

        ExecutionQuery executionQuery = mock(ExecutionQuery.class);
        when(executionQuery.parentId("processInstanceId")).thenReturn(executionQuery);
        when(executionQuery.list()).thenReturn(new ArrayList<>());
        when(runtimeService.createExecutionQuery()).thenReturn(executionQuery);

        when(approvalService.updateApprovalStatus("processInstanceId", ApprovalStatusEnum.WITHDRAWN.getCode())).thenReturn(true);
        HistoricProcessInstance historicProcessInstance = mock(HistoricProcessInstance.class);
        HistoricProcessInstanceQuery historicProcessInstanceQuery = mock(HistoricProcessInstanceQuery.class);
        when(historyService.createHistoricProcessInstanceQuery()).thenReturn(historicProcessInstanceQuery);
        when(historicProcessInstanceQuery.processInstanceId("processInstanceId")).thenReturn(historicProcessInstanceQuery);
        when(historicProcessInstanceQuery.singleResult()).thenReturn(historicProcessInstance);
        when(historicProcessInstance.getStartUserId()).thenReturn("userId");
        ChangeActivityStateBuilder changeActivityStateBuilder = mock(ChangeActivityStateBuilder.class);
        when(runtimeService.createChangeActivityStateBuilder()).thenReturn(changeActivityStateBuilder);
        when(changeActivityStateBuilder.moveExecutionsToSingleActivityId(anyList(),any())).thenReturn(changeActivityStateBuilder);
        doNothing().when(changeActivityStateBuilder).changeState();
        when(approvalService.updateApprovalStatus(anyString(),anyInt())).thenReturn(true);
        boolean result = dcTaskService.stopProcess("processInstanceId");
        assertTrue(result);
    }
    /* Ended by AICoder, pid:o86a852f4b2bd4e140490a9c70562e5934874afd */

    @Test
    public void testGetTasksByProcessInstanceIdAndTaskId() {
        when(authService.getUserId()).thenReturn("userId");
        List<Task> taskList = new ArrayList<>();
        Task task1 = mock(Task.class);
        when(task1.getId()).thenReturn("taskId1");
        when(task1.getProcessInstanceId()).thenReturn("processInstanceId");
        taskList.add(task1);
        TaskQuery taskQuery = mock(TaskQuery.class);
        when(taskService.createTaskQuery()).thenReturn(taskQuery);
        when(taskQuery.processInstanceId("processInstanceId")).thenReturn(taskQuery);
        when(taskQuery.taskId("taskId")).thenReturn(taskQuery);
        when(taskQuery.taskAssignee("userId")).thenReturn(taskQuery);
        when(taskQuery.list()).thenReturn(taskList);
        List<Task> processInstanceId = dcTaskService.getTasksByProcessInstanceIdAndTaskId("processInstanceId","taskId");
        assertEquals(1, processInstanceId.size());
    }
}
