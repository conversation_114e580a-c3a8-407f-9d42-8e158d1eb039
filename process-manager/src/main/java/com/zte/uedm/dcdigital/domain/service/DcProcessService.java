package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.model.process.ProcessBo;
import com.zte.uedm.dcdigital.domain.model.process.ProcessNodeDetailEntity;
import com.zte.uedm.dcdigital.domain.model.process.TaskEntity;

import java.util.List;
import java.util.Map;

public interface DcProcessService {


    /* Started by AICoder, pid:k478dy7b9a68ad514fa80beb309ae84d9785a52f */
    /**
     * 根据流程定义key启动流程
     * @param processKey  流程定义的key
     * @param processBusinessKey 业务key
     * @param variables 扩展参数
     * @return 启动的流程实例ID
     */
    String startProcessByKey(String processKey, String processBusinessKey, Map<String, Object> variables);

    /**
     * 根据流程实例id启动流程
     * @param processId 流程定义的id
     * @param processBusinessKey 业务key
     * @param variables 扩展参数
     * @return 启动的流程实例ID
     */
    String startProcessById(String processId, String processBusinessKey, Map<String, Object> variables);

    /**
     * 查询代办任务列表
     * @param processBo 查询条件
     * @return 代办任务列表
     */
    PageVO<TaskEntity> todoList(ProcessBo processBo);

    /**
     * 查询已办任务列表
     * @param processBo 查询条件
     * @return 已办任务列表
     */
    PageVO<TaskEntity> finishedList(ProcessBo processBo);

    /**
     * 查询我发起的任务列表
     * @param processBo 查询条件
     * @return 我发起的任务列表
     */
    PageVO<TaskEntity> ownList(ProcessBo processBo);

    /**
     * 查询指定流程实例的节点详情
     * @param processId 流程实例ID
     * @return 节点详情列表
     */
    List<ProcessNodeDetailEntity> processNodeDetail(String processId);
    /* Ended by AICoder, pid:k478dy7b9a68ad514fa80beb309ae84d9785a52f */
}
