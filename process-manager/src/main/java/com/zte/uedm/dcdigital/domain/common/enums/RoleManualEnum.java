package com.zte.uedm.dcdigital.domain.common.enums;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum RoleManualEnum {
    ACCEPT(10, "产品SE"),
    TRANSFER(11, "产品SE"),
    COMPLETE(12, "产品SE"),
    ACCEPTANCE(13, "方案经理"),
    REJECT(14, "方案经理"),
    TERMINATED(15, "产品SE"),
    REFUSE(16, "产品SE");
    private final int code;
    private final String description;

    RoleManualEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据代码查找对应的枚举对象
    public static RoleManualEnum fromCode(int code) {
        for (RoleManualEnum type : RoleManualEnum.values()) {
            if (type.getCode()==code) {
                return type;
            }
        }
        log.error("MktAcceptTypeEnum not found for code: {}", code);
        throw new BusinessException(StatusCode.PARAM_ERROR);
    }
}
