package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.CompletedApprovalQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.InitiatedApprovalQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.PendingApprovalQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProcessPendingDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.TaskDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApprovalMapper  extends BaseMapper<ApprovalPo> {


    /* Started by AICoder, pid:hc5fbc70c1u0c79147330a4f4062b10379576bd5 */
    /**
     * 根据流程实例ID查询审批记录。
     *
     * @param flowId 流程实例ID，用于标识特定的审批流程实例。
     * @return 返回与指定流程实例ID匹配的审批记录，如果没有找到匹配项，则返回null。
     */
    ApprovalPo queryByFlowId(@Param("flowId") String flowId);
    /* Ended by AICoder, pid:hc5fbc70c1u0c79147330a4f4062b10379576bd5 */

    List<ApprovalPo> queryByFlowIds(@Param("flowIds") List<String> flowIds);

    List<ApprovalPo> queryPendingList(@Param("queryDto") PendingApprovalQueryDto queryDto, @Param("userIds") List<String> userIds);
    List<ApprovalPo> queryPendingListNew( ProcessPendingDto processBo);

    List<ApprovalPo> queryInitiatedList(@Param("queryDto") InitiatedApprovalQueryDto queryDto, @Param("userIds") List<String> userIds);

    List<ApprovalWithResultObj> queryCompletedList(@Param("userId") String userId, @Param("queryDto") CompletedApprovalQueryDto queryDto, @Param("userIds") List<String> userIds);

    void updateBussesDataJson(@Param("flowId") String flowId, @Param("data") String bussesDataJson);

    void updateFlowIdById(@Param("id") String id, @Param("flowId") String flowId);

    List<TaskDetailVo> calculateTasks(String bussesDataJson);

    ApprovalPo queryByMaterialId(@Param("materialId") String materialId);

    List<TaskDetailVo> calculateByProjectId(String projectId);

    List<ApprovalPo> queryApprovalByProjectIdAndType(@Param("projectId")String projectId, @Param("type")Integer type);
}
