/* Started by AICoder, pid:q87675c6ff25f1314707085ac1c23c200c10e082 */
package com.zte.uedm.dcdigital.domain.common.event;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.dcdigital.common.bean.dto.ApprovalInformationDto;
import com.zte.uedm.dcdigital.common.bean.dto.MaterialDto;
import com.zte.uedm.dcdigital.common.bean.enums.ApprovalResult;
import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.enums.ApprovalStatusEnum;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessStatusCode;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.domain.repository.ApprovalRepository;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/***
 * 物料上架产品经理审批通过后，非18代码更新物料状态
 */
@Slf4j
@Component
public class MaterialInfoSupplementListener implements TaskListener {


    @Autowired
    @Qualifier("systemServiceImpl")
    private SystemService systemService;

    @Autowired
    @Qualifier("productServiceImpl")
    private ProductService productService;

    @Resource
    private ApprovalRepository approvalRepository;


    private static MaterialInfoSupplementListener supplementListener;

    @PostConstruct
    public void init(){
        supplementListener = this;
        supplementListener.systemService = this.systemService;
        supplementListener.productService = this.productService;
        supplementListener.approvalRepository = this.approvalRepository;
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        // 获取当前任务ID
        String taskId = delegateTask.getId();
        // 获取自定义产品类别
        String productCategory = (String) delegateTask.getVariable(ProcessConstants.PRODUCT_CATEGORY);
        if (productCategory == null) {
            log.error("productCategory is null, taskId:{}", taskId);
            throw new BusinessException(ProcessStatusCode.PRODUCT_CATEGORY_NOT_FOUND);
        }
        // 获取定义的节点key
        String taskKey = delegateTask.getTaskDefinitionKey();
        log.info("MaterialInfoSupplementListener taskKey, productCategory: {}, {}", taskKey, productCategory);
        // 根据节点key和产品类别获取角色
        List<String> userIds = queryAssigneesForRoleAndCategory(taskKey, productCategory);
        if (userIds == null || userIds.isEmpty()) {
            log.error("No candidates found for task {}, productCategory:{}", taskId, productCategory);
            throw new BusinessException(ProcessStatusCode.NO_CANDIDATES_FOUND);
        }
        if (RoleCodeEnum.COST_DIRECTOR.getCode().equals(taskKey)) {
            //TODO 获取当前流程实例ID
            String flowId = delegateTask.getProcessInstanceId();
            updateMaterialAfterApproval(flowId);
        }
    }


    private void updateMaterialAfterApproval(String flowId) {
        //更新物料
        ApprovalEntity approvalEntity = supplementListener.approvalRepository.queryByFlowId(flowId);
        ApprovalObj newApproval = supplementListener.approvalRepository.getById(approvalEntity.getId());
        log.info("newApproval: {}", newApproval);
        //此处对于OTH的实际审批已经结束，仅需后续流程补充信息
        if (newApproval.getStatus() == 0 || newApproval.getStatus() == 1) {
            ApprovalInformationDto approvalInfo = new ApprovalInformationDto();
            approvalInfo.setApprovalType(String.valueOf(newApproval.getApprovalType()));
            approvalInfo.setApprovalId(newApproval.getId());
            approvalInfo.setApprovalStatus(2);
            approvalInfo.setApprovalResult(ApprovalResult.PASS.getCode());
            approvalInfo.setApprovalTime(DateTimeUtils.getCurrentTime());
            approvalInfo.setSubmitter(newApproval.getSubmitUser());
            List<MaterialDto> material = JSON.parseArray(newApproval.getBussesDataJson(), MaterialDto.class);
            approvalInfo.setMaterial(material);
            approvalInfo.setMaterialStatus(ProcessConstants.WAIT_SUPPLEMENTARY_COST);
            log.info("Update the material to a special state approvalInfo: {}", approvalInfo);
            supplementListener.productService.approvalUpdate(approvalInfo);
        }
    }

    /**
     * 根据任务键和产品类别查询分配的用户列表。
     *
     * @param taskKey        任务键，用于标识特定的任务。
     * @param productCategory 产品类别，用于进一步筛选用户。
     * @return 返回符合条件的用户ID列表。如果没有找到任何用户，返回一个空列表。
     */
    private List<String> queryAssigneesForRoleAndCategory(String taskKey, String productCategory) {
        log.info("taskKey, productCategory: {}, {}", taskKey, productCategory);
        boolean present = RoleCodeEnum.isEnumValuePresent(taskKey);
        if (present) {
            List<UserVo> userInfo = supplementListener.systemService.getUserByRoleCodeAndResourceId(taskKey, productCategory);
            log.info("userInfo size: {}", userInfo.size());
            return Optional.ofNullable(userInfo)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(UserVo::getId)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}
/* Ended by AICoder, pid:q87675c6ff25f1314707085ac1c23c200c10e082 */