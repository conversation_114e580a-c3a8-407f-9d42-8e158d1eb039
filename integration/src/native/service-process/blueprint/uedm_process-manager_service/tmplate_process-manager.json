{"kind": "Template", "apiVersion": "v1", "namespace": "${NAMESPACE}", "metadata": {"labels": {"name": "@ms.manager.process@"}, "name": "@ms.manager.process@"}, "objects": [{"spec": {"replicas": 1, "selector": {"name": "@ms.manager.process@"}, "template": {"metadata": {"labels": {"name": "@ms.manager.process@"}, "annotations": {"pod.beta.kubernetes.io/init-containers": "[]"}}, "spec": {"containers": [{"name": "@ms.manager.process@-container", "image": "/@tenant.id@/@ms.manager.process@:@dc-digital.service.version@", "imagePullPolicy": "Always", "tty": false, "stdin": false, "command": [], "env": [{"name": "OPENPALETTE_KAFKA_ADDRESS", "value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_ADDRESS]"}, {"name": "OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS", "value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS]"}, {"name": "OPENPALETTE_KAFKA_PORT", "value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_PORT]"}, {"name": "OPENPALETTE_KAFKA_ZOOKEEPER_PORT", "value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_ZOOKEEPER_PORT]"}, {"name": "OPENPALETTE_REDIS_ADDRESS", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_ADDRESS]"}, {"name": "OPENPALETTE_REDIS_PORT", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_PORT]"}, {"name": "OPENPALETTE_REDIS_PASSWORD", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_PASSWORD]"}, {"name": "OPENPALETTE_REDIS_SENTINEL_ADDRESS", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_ADDRESS]"}, {"name": "OPENPALETTE_REDIS_SENTINEL_PORT", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_PORT]"}, {"name": "OPENPALETTE_REDIS_SENTINEL_MASTERNAME", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_MASTERNAME]"}, {"name": "OPENPALETTE_PG_ADDRESS", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_ADDRESS]"}, {"name": "OPENPALETTE_PG_PORT", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_PORT]"}, {"name": "OPENPALETTE_PG_DBNAME", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_DBNAME]"}, {"name": "OPENPALETTE_PG_USERNAME", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_USERNAME]"}, {"name": "OPENPALETTE_PG_PASSWORD", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_PASSWORD]"}, {"name": "net_api_eth", "value": "eth1"}, {"name": "TZ", "value": "${TZ}"}], "ports": [{"containerPort": 29136, "protocol": "TCP"}], "volumeMounts": [], "resources": {"requests": {"pod.alpha.kubernetes.io/opaque-int-resource-affinityCPU": 0, "pod.alpha.kubernetes.io/opaque-int-resource-hugepage": 0, "cpu": 1, "memory": "2Gi"}, "limits": {"cpu": 3, "memory": "4Gi"}}}], "restartPolicy": "Always", "volumes": [], "terminationGracePeriodSeconds": 30}}, "strategy": {"type": "Rolling", "rollingParams": {"timeoutSeconds": "600", "maxUnavailable": "25%", "maxSurge": "25%"}}}, "kind": "DeploymentConfig", "apiVersion": "v1", "metadata": {"name": "@ms.manager.process@", "namespace": "${NAMESPACE}"}}], "parameters": [{"name": "NAMESPACE", "displayName": "", "description": "", "value": "openshift", "section": "None"}, {"name": "@ms.manager.process@", "displayName": "", "description": "", "value": "@ms.manager.process@", "section": "route"}, {"name": "kafka-0", "displayName": "kafka-0", "description": "kafka-0", "value": "", "section": "commonService", "subSection": "Kafka", "type": "string"}, {"name": "redis-0", "displayName": "redis-0", "description": "redis-0", "value": "", "section": "commonService", "subSection": "redis", "type": "string"}, {"name": "postgresql-0", "displayName": "postgresql-0", "description": "postgresql-0", "value": "", "section": "commonService", "subSection": "PostgreSQL", "type": "string"}, {"name": "TZ", "displayName": "TZ", "description": "", "value": "Asia/Shanghai", "section": "env"}], "vnpm_param": {"vnpm_object": [{"name": "@ms.manager.process@", "route_list": [{"serviceName": "${@ms.manager.process@}", "version": "v1", "url": "/api/process-manager/v1", "protocol": "REST", "enable_tls": false, "path": "/api/process-manager/v1", "lb_policy": "round-robin", "port": "29136", "visualRange": "0", "network_plane_type": "net_api", "enable_client_verify": false}], "common_service": [{"logicName": "${kafka-0}"}, {"logicName": "${redis-0}"}, {"logicName": "${postgresql-0}"}], "networks": {"ports": [{"attach_to_network": "net_api", "attributes": {"nic_name": "eth0", "function": "std", "nic_type": "normal", "accelerate": "false"}}, {"attach_to_network": "lan", "attributes": {"nic_name": "eth1", "function": "std", "nic_type": "normal", "accelerate": "false"}}]}, "cluster_info": {"cluster_type": "", "labelselector": []}, "isUseServiceDiscovery": true}]}, "eps_param": {"replicasMin": "1", "replicasMax": "1", "auto_policy": {"@ms.manager.process@-container": []}}}