<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:ma653h058744fdb1410a093a90b7dc8cbe517283 -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.PageStatWeekMapper">

    <resultMap id="PageStatWeekResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatWeekPo">
        <id property="id" column="id"/>
        <result property="day" column="day"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="productCategoryId" column="product_category_id"/>
        <result property="type" column="type"/>
        <result property="resourceId" column="resource_id"/>
        <result property="num" column="num"/>
    </resultMap>

    <sql id="selectPageStatWeekVo">
        SELECT id, day, dept_id, user_id, product_category_id, type, resource_id, num
        FROM page_stat_week
    </sql>

    <insert id="insertPageStatWeek" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatWeekPo">
        INSERT INTO page_stat_week
        (id, day, dept_id, user_id, product_category_id, type, resource_id, num)
        VALUES
        (#{id}, #{day}, #{deptId}, #{userId}, #{productCategoryId}, #{type}, #{resourceId}, #{num})
    </insert>

    <insert id="batchInsertPageStatWeek">
        INSERT INTO page_stat_week
        (id, day, dept_id, user_id, product_category_id, type, resource_id, num)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.day}, #{item.deptId}, #{item.userId},
            #{item.productCategoryId}, #{item.type}, #{item.resourceId}, #{item.num})
        </foreach>
    </insert>

    <update id="batchUpdatePageStatWeek">
        UPDATE page_stat_week
        SET num = CASE id
        <foreach collection="list" item="item" separator=" ">
            WHEN #{item.id} THEN #{item.num}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="selectPageStatWeekList"
            parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatWeekPo"
            resultMap="PageStatWeekResult">
        <include refid="selectPageStatWeekVo"/>
        <where>
            <if test="day != null">AND day = #{day}</if>
            <if test="deptId != null and deptId != ''">AND dept_id = #{deptId}</if>
            <if test="userId != null and userId != ''">AND user_id = #{userId}</if>
            <if test="beginTime != null and endTime != null">
                AND day BETWEEN #{beginTime} AND #{endTime}
            </if>
            <if test="type != null">AND type = #{type}</if>
            <if test="resourceId != null and resourceId != ''">AND resource_id = #{resourceId}</if>
        </where>
    </select>

    <delete id="deletePageStatWeekByIds" parameterType="String">
        DELETE FROM page_stat_week
        WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByWeekNumber">
        DELETE FROM page_stat_week
        WHERE day = #{weekNumber}
    </delete>

    <select id="selectPageStatWeekById"
            parameterType="String"
            resultMap="PageStatWeekResult">
        <include refid="selectPageStatWeekVo"/>
        WHERE id = #{id}
    </select>
</mapper>

        <!-- Ended by AICoder, pid:ma653h058744fdb1410a093a90b7dc8cbe517283 -->