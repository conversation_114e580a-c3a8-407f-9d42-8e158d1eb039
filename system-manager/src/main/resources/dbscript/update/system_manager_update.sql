
--  产品SE按钮权限( 更新产品小类关联用户 更新延保系数)
INSERT INTO auth_role_permission
(id, role_id, permission_id, create_time, update_time, create_by, update_by)
VALUES
 ('137', '65b24875-6c22-417d-954c-a7d162b972e4', '27', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');


-- Started by AICoder, pid:j2740t3ae17df9614e590b42b030d21e18727030
-- 该脚本用于更新权限和菜单配置，执行日期：2025-03-11

DELETE FROM public.auth_menu WHERE id='materialQuery';
DELETE FROM public.auth_menu WHERE id='projectRepository';
UPDATE public.auth_menu SET sort=1 WHERE id='productLibrary';
UPDATE public.auth_menu SET id='LTCManagement',"name"='{"zh-CN":"LTC管理","en-US":"LTC Management"}' WHERE id='projectManagement';
UPDATE public.auth_menu SET parent_id='LTCManagement',id='businessManagement',"name"='{"zh-CN":"商机管理","en-US":"Business Management"}',sort=1 WHERE id='projectMaintenance';
INSERT INTO public.auth_menu (id,"name",url,icon,menu_level,sort,parent_id,create_time,update_time,create_by,update_by) VALUES ('demandManagement','{"zh-CN":"需求管理","en-US":"Demand Management"}','0','0',2,2,'productManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_menu (id,"name",url,icon,menu_level,sort,parent_id,create_time,update_time,create_by,update_by) VALUES ('selectionManagement','{"zh-CN":"选型管理","en-US":"Selection Management"}','0','0',2,3,'productManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_menu (id,"name",url,icon,menu_level,sort,parent_id,create_time,update_time,create_by,update_by) VALUES ('costManagement','{"zh-CN":"成本管理","en-US":"Cost Management"}','0','0',2,4,'productManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_menu (id,"name",url,icon,menu_level,sort,parent_id,create_time,update_time,create_by,update_by) VALUES ('clueManagement','{"zh-CN":"线索管理","en-US":"Clue Management"}','0','0',2,2,'LTCManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_menu (id,"name",url,icon,menu_level,sort,parent_id,create_time,update_time,create_by,update_by) VALUES ('projectManagement','{"zh-CN":"项目管理","en-US":"Project Management"}','0','0',2,3,'LTCManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
-- Ended by AICoder, pid:j2740t3ae17df9614e590b42b030d21e18727030

-- Started by AICoder, pid:u67476dd38v7cc81494309838000780b4db950aa
-- 该脚本用于更新权限配置，执行日期：2025-03-11

INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES ('54','需求管理',0,'demandManagement','demandManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES ('55','选型管理',0,'selectionManagement','selectionManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES ('56','成本管理',0,'costManagement','costManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES ('57','商机管理',0,'businessManagement','businessManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES ('58','线索管理',0,'clueManagement','clueManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
UPDATE public.auth_permission SET "name"='LTC管理',menu_id='LTCManagement',permission_code='LTCManagement' WHERE id='28';
UPDATE public.auth_permission SET "name"='项目管理',menu_id='projectManagement',permission_code='projectManagement' WHERE id='29';
-- Ended by AICoder, pid:u67476dd38v7cc81494309838000780b4db950aa
-- 该脚本用于更新权限配置，执行日期：2025-03-25
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES ('59','自定义产品分组排序',1,'product.group.custom.sort','','2025-03-25 11:24:02','2025-03-25 11:24:02','admin','admin');

-- 该脚本用于更新权限配置，执行日期：2025-03-26
INSERT INTO auth_permission
(id, "name", "type", permission_code, create_time, update_time, create_by, update_by, menu_id)
VALUES
    ('60', '产品小类维护-延保系数-编辑', 1, 'productCategory.maintain.extWarrantyFactor.edit', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('61', '产品小类维护-产品小类人员配置-编辑', 1, 'productCategory.maintain.user.edit', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL);

    --2025-03-28权限点
INSERT INTO auth_permission
(id, "name", "type", permission_code, create_time, update_time, create_by, update_by, menu_id)
VALUES
    ('62', '物料成本信息查看', 1, 'material.cost.info.view', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('63', '物料基本信息查看', 1, 'material.basic.info.view', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('64', '物料交期信息查看', 1, 'material.delivery.info.view', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('65', '产品库-品牌查看', 1, 'product.brand.view', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('66', '产品库-产品文档查看', 1, 'product.document.view', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL);
-- Started by AICoder, pid:jdff8t2493jc139142150b1160fc3a15d369ff01
-- 该脚本用于批量插入角色权限配置，执行日期：2025-03-11

INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('140','cff28622-86d2-4048-8a4a-cab23a161009', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('220','70ef3496-9c4b-4f99-992d-4a40acea03aa', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('138','65b24875-6c22-417d-954c-a7d162b972e4', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('139','84a5117f-e6c8-4583-b3f4-7fc105336004', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('141','133c7aa1-080c-4231-ab75-f219abb5eeea', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('142','06a77708-8267-43d3-b66c-b69209c76937', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('143','b7a13753-5628-4d37-89cb-2e76fcb8e20a', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('144','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('145','9867bda5-2679-4561-a978-33cb5bafbcd0', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('146','fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('147','e5e2ae6a-c863-406c-8316-20aa72fb052f', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('148','7ab75384-319d-4b6d-8827-adc55ab0539f', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('149','484da0ef-0140-4998-95f4-5386a8048d7c', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('150','9d2351be-54bb-461c-bcef-269268794f2a', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('151','3c7eb974-c394-41ef-b1f0-fd4344f7f896', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('152','59b34b85-70f0-4598-b072-c44c0f8b050f', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('153','53806c42-5806-4c51-81ff-15c31196228e', '54', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
-- Ended by AICoder, pid:jdff8t2493jc139142150b1160fc3a15d369ff01

I-- Started by AICoder, pid:51e0fhf1becfbc7146b309d6b020cc1545e9b035
 -- 该脚本用于批量插入角色权限配置，执行日期：2025-03-11

 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('154','cff28622-86d2-4048-8a4a-cab23a161009', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('155','70ef3496-9c4b-4f99-992d-4a40acea03aa', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('156','65b24875-6c22-417d-954c-a7d162b972e4', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('157','84a5117f-e6c8-4583-b3f4-7fc105336004', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('158','133c7aa1-080c-4231-ab75-f219abb5eeea', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('159','06a77708-8267-43d3-b66c-b69209c76937', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('160','b7a13753-5628-4d37-89cb-2e76fcb8e20a', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('161','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('162','9867bda5-2679-4561-a978-33cb5bafbcd0', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('163','fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('164','e5e2ae6a-c863-406c-8316-20aa72fb052f', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('165','7ab75384-319d-4b6d-8827-adc55ab0539f', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('166','484da0ef-0140-4998-95f4-5386a8048d7c', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('167','9d2351be-54bb-461c-bcef-269268794f2a', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('168','3c7eb974-c394-41ef-b1f0-fd4344f7f896', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('169','59b34b85-70f0-4598-b072-c44c0f8b050f', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('170','53806c42-5806-4c51-81ff-15c31196228e', '55', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
 -- Ended by AICoder, pid:51e0fhf1becfbc7146b309d6b020cc1545e9b035


-- Started by AICoder, pid:1363byea16h834214b64097e80bff616d3399592
-- 该脚本用于批量插入角色权限配置，执行日期：2025-03-11

INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('171','cff28622-86d2-4048-8a4a-cab23a161009', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('172','70ef3496-9c4b-4f99-992d-4a40acea03aa', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('173','65b24875-6c22-417d-954c-a7d162b972e4', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('174','84a5117f-e6c8-4583-b3f4-7fc105336004', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('175','133c7aa1-080c-4231-ab75-f219abb5eeea', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('176','06a77708-8267-43d3-b66c-b69209c76937', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('177','b7a13753-5628-4d37-89cb-2e76fcb8e20a', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('178','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('179','9867bda5-2679-4561-a978-33cb5bafbcd0', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('180','fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('181','e5e2ae6a-c863-406c-8316-20aa72fb052f', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('182','7ab75384-319d-4b6d-8827-adc55ab0539f', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('183','484da0ef-0140-4998-95f4-5386a8048d7c', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('184','9d2351be-54bb-461c-bcef-269268794f2a', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('185','3c7eb974-c394-41ef-b1f0-fd4344f7f896', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('186','59b34b85-70f0-4598-b072-c44c0f8b050f', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('187','53806c42-5806-4c51-81ff-15c31196228e', '56', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
-- Ended by AICoder, pid:1363byea16h834214b64097e80bff616d3399592


-- Started by AICoder, pid:t0d68m34a7k60d11434f0bf990c32816fcc7f8f1
-- 该脚本用于批量插入角色权限配置，执行日期：2025-03-11

INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('188','cff28622-86d2-4048-8a4a-cab23a161009', '57', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('191','84a5117f-e6c8-4583-b3f4-7fc105336004', '57', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('193','06a77708-8267-43d3-b66c-b69209c76937', '57', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('199','7ab75384-319d-4b6d-8827-adc55ab0539f', '57', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('200','484da0ef-0140-4998-95f4-5386a8048d7c', '57', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('201','9d2351be-54bb-461c-bcef-269268794f2a', '57', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('202','3c7eb974-c394-41ef-b1f0-fd4344f7f896', '57', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');

INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('205','cff28622-86d2-4048-8a4a-cab23a161009', '58', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('208','84a5117f-e6c8-4583-b3f4-7fc105336004', '58', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('210','06a77708-8267-43d3-b66c-b69209c76937', '58', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('216','7ab75384-319d-4b6d-8827-adc55ab0539f', '58', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('217','484da0ef-0140-4998-95f4-5386a8048d7c', '58', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('218','9d2351be-54bb-461c-bcef-269268794f2a', '58', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');
INSERT INTO public.auth_role_permission (id,role_id, permission_id, create_time, update_time, create_by, update_by) VALUES('219','3c7eb974-c394-41ef-b1f0-fd4344f7f896', '58', '2025-03-11 16:24:02', '2025-03-11 16:24:02', 'admin', 'admin');



--    下面是补充的部分角色拥有的菜单权限 2025-03-25
INSERT INTO auth_role_permission
(id, role_id, permission_id, create_time, update_time, create_by, update_by)
values
    ('222', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('223', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    项目评分查看人 LTC管理 (线索管理、商机管理（文档、品牌引导）)
    ('224', '484da0ef-0140-4998-95f4-5386a8048d7c', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('225', '484da0ef-0140-4998-95f4-5386a8048d7c', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    方案SE LTC管理 (线索管理、商机管理（文档、品牌引导）)
    ('226', '9d2351be-54bb-461c-bcef-269268794f2a', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('227', '9d2351be-54bb-461c-bcef-269268794f2a', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    交付TD LTC管理 (线索管理、商机管理（文档、品牌引导）)
    ('228', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('229', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    成本总监 LTC管理 (线索管理、商机管理（文档、品牌引导）)
    ('230', '84a5117f-e6c8-4583-b3f4-7fc105336004', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('231', '84a5117f-e6c8-4583-b3f4-7fc105336004', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    商务总监 LTC管理 (线索管理、商机管理（文档、品牌引导）)
    ('232', 'cff28622-86d2-4048-8a4a-cab23a161009', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('233', 'cff28622-86d2-4048-8a4a-cab23a161009', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    物流总监 LTC管理 (线索管理、商机管理（文档、品牌引导）)
    ('234', '06a77708-8267-43d3-b66c-b69209c76937', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('235', '06a77708-8267-43d3-b66c-b69209c76937', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    项目查看人员 LTC管理 (线索管理、商机管理（文档、品牌引导）)
    ('236', '7ab75384-319d-4b6d-8827-adc55ab0539f', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('237', '7ab75384-319d-4b6d-8827-adc55ab0539f', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --市场投标支持产品SE LTC管理 (线索管理、商机管理（文档、品牌引导）)
    ('238', '59b34b85-70f0-4598-b072-c44c0f8b050f', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('239', '59b34b85-70f0-4598-b072-c44c0f8b050f', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin')
    ;
-- Ended by AICoder, pid:t0d68m34a7k60d11434f0bf990c32816fcc7f8f1





INSERT INTO auth_role_permission
(id, role_id, permission_id, create_time, update_time, create_by, update_by)
VALUES
    -- 该脚本用于批量插入角色权限配置，执行日期：2025-03-25  产品经理、产品se自定义产品分组排序
    ('240','65b24875-6c22-417d-954c-a7d162b972e4', '59', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('241','70ef3496-9c4b-4f99-992d-4a40acea03aa', '59', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    -- 该脚本用于批量插入角色权限配置，执行日期：2025-03-26  产品经理、产品se 按钮编辑权限
    -- 产品经理按钮权限(产品小类维护-延保系数-编辑,产品小类维护-产品小类人员配置-编辑)
    ('242', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '60', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('243', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '61', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --  产品SE按钮权限(产品小类维护-延保系数-编辑)
    ('244', '65b24875-6c22-417d-954c-a7d162b972e4', '60', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --！！！！这里线上更新漏掉了！！！！！！  产品经理按钮权限(更新产品小类关联用户)
    ('245', '65b24875-6c22-417d-954c-a7d162b972e4', '27', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),


    --    下面是补充的部分角色拥有的菜单权限 2025-03-26
    --     项目管理员 产品管理
    ('246', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    项目评分查看人 产品管理
    ('247', '484da0ef-0140-4998-95f4-5386a8048d7c', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    方案SE 产品管理
    ('248', '9d2351be-54bb-461c-bcef-269268794f2a', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    交付TD 产品管理
    ('249', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    成本总监 产品管理
    ('250', '84a5117f-e6c8-4583-b3f4-7fc105336004', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    商务总监 产品管理
    ('251', 'cff28622-86d2-4048-8a4a-cab23a161009', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    物流总监 产品管理
    ('252', '06a77708-8267-43d3-b66c-b69209c76937', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    项目查看人员 产品管理
    ('253', '7ab75384-319d-4b6d-8827-adc55ab0539f', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --市场投标支持产品SE 产品管理
    ('254', '59b34b85-70f0-4598-b072-c44c0f8b050f', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),

     --    项目管理员 菜单权限(工作台、我的工作台、产品库、知识库、文档、FAQ)
    ('255', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('256', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('257', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('258', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('259', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('260', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    方案SE 菜单权限(工作台、我的工作台、产品库、知识库、文档、FAQ)
    ('261', '9d2351be-54bb-461c-bcef-269268794f2a', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('262', '9d2351be-54bb-461c-bcef-269268794f2a', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('263', '9d2351be-54bb-461c-bcef-269268794f2a', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('264', '9d2351be-54bb-461c-bcef-269268794f2a', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('265', '9d2351be-54bb-461c-bcef-269268794f2a', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('266', '9d2351be-54bb-461c-bcef-269268794f2a', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),

    --    项目评分查看人(工作台、我的工作台、产品库、知识库、文档、FAQ)
    ('267', '484da0ef-0140-4998-95f4-5386a8048d7c', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('268', '484da0ef-0140-4998-95f4-5386a8048d7c', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('269', '484da0ef-0140-4998-95f4-5386a8048d7c', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('270', '484da0ef-0140-4998-95f4-5386a8048d7c', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('271', '484da0ef-0140-4998-95f4-5386a8048d7c', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('272', '484da0ef-0140-4998-95f4-5386a8048d7c', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),

    --    交付TD(工作台、我的工作台、产品库、知识库、文档、FAQ)
    ('273', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('274', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('275', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('276', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('277', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('278', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    成本总监(工作台、我的工作台、产品库、知识库、文档、FAQ)
    ('279', '84a5117f-e6c8-4583-b3f4-7fc105336004', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('280', '84a5117f-e6c8-4583-b3f4-7fc105336004', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('281', '84a5117f-e6c8-4583-b3f4-7fc105336004', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('282', '84a5117f-e6c8-4583-b3f4-7fc105336004', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('283', '84a5117f-e6c8-4583-b3f4-7fc105336004', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('284', '84a5117f-e6c8-4583-b3f4-7fc105336004', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    商务总监(工作台、我的工作台、产品库、知识库、文档、FAQ)
    ('285', 'cff28622-86d2-4048-8a4a-cab23a161009', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('286', 'cff28622-86d2-4048-8a4a-cab23a161009', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('287', 'cff28622-86d2-4048-8a4a-cab23a161009', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('288', 'cff28622-86d2-4048-8a4a-cab23a161009', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('289', 'cff28622-86d2-4048-8a4a-cab23a161009', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('290', 'cff28622-86d2-4048-8a4a-cab23a161009', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    物流总监(工作台、我的工作台、产品库、知识库、文档、FAQ)
    ('291', '06a77708-8267-43d3-b66c-b69209c76937', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('292', '06a77708-8267-43d3-b66c-b69209c76937', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('293', '06a77708-8267-43d3-b66c-b69209c76937', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('294', '06a77708-8267-43d3-b66c-b69209c76937', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('295', '06a77708-8267-43d3-b66c-b69209c76937', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('296', '06a77708-8267-43d3-b66c-b69209c76937', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    项目查看人员(工作台、我的工作台、产品库、知识库、文档、FAQ)
    ('297', '7ab75384-319d-4b6d-8827-adc55ab0539f', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('298', '7ab75384-319d-4b6d-8827-adc55ab0539f', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('299','7ab75384-319d-4b6d-8827-adc55ab0539f', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('300', '7ab75384-319d-4b6d-8827-adc55ab0539f', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('301', '7ab75384-319d-4b6d-8827-adc55ab0539f', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('302','7ab75384-319d-4b6d-8827-adc55ab0539f', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --市场投标支持产品SE(工作台、我的工作台、产品库、知识库、文档、FAQ、商机管理、菜单管理)
    ('303', '59b34b85-70f0-4598-b072-c44c0f8b050f', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('304', '59b34b85-70f0-4598-b072-c44c0f8b050f', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('305','59b34b85-70f0-4598-b072-c44c0f8b050f', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),


    --    2025-03-28添加脚本语句
    --市场投标支持产品SE(商机管理、菜单管理)
    ('306','59b34b85-70f0-4598-b072-c44c0f8b050f', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('307','59b34b85-70f0-4598-b072-c44c0f8b050f', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --产品SE(商机管理、菜单管理)
    ('308','65b24875-6c22-417d-954c-a7d162b972e4', '57', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('309','65b24875-6c22-417d-954c-a7d162b972e4', '58', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),

    --  配置经理(工作台、我的工作台、产品管理、产品库、知识库、文档、FAQ)
    ('310', '133c7aa1-080c-4231-ab75-f219abb5eeea', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('311', '133c7aa1-080c-4231-ab75-f219abb5eeea', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('312', '133c7aa1-080c-4231-ab75-f219abb5eeea', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('313', '133c7aa1-080c-4231-ab75-f219abb5eeea', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('314', '133c7aa1-080c-4231-ab75-f219abb5eeea', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('315', '133c7aa1-080c-4231-ab75-f219abb5eeea', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('316', '133c7aa1-080c-4231-ab75-f219abb5eeea', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),

    --  市场代表(工作台、我的工作台、产品管理、产品库、知识库、文档、FAQ)
    ('317', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('318', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('319', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('320', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('321', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('322', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('323', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --  售后代表(工作台、我的工作台、产品管理、产品库、知识库、文档、FAQ)
    ('324', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('325', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('326', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('327', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('328', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('329', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('330', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --  工程总监(工作台、我的工作台、产品管理、产品库、知识库、文档、FAQ)
    ('331', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('332', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('333', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('334', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('335', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('336', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('337', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin')
    ;

        --2025-03-28部分角色权限点
    --    物料成本信息查看(产品经理、产品SE、成本总监)
    INSERT INTO auth_role_permission
    (id, role_id, permission_id, create_time, update_time, create_by, update_by)
    values
    ('338', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '62', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('339', '65b24875-6c22-417d-954c-a7d162b972e4', '62', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('340', '84a5117f-e6c8-4583-b3f4-7fc105336004', '62', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    物料基本信息查看(产品经理、产品SE、商务总监、配置经理、市场代表、售后代表、工程总监、物流总监、成本总监)
    ('341', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '63', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('342', '65b24875-6c22-417d-954c-a7d162b972e4', '63', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('343', '84a5117f-e6c8-4583-b3f4-7fc105336004', '63', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('344', 'cff28622-86d2-4048-8a4a-cab23a161009', '63', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('345', '133c7aa1-080c-4231-ab75-f219abb5eeea', '63', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('346', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '63', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('347', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '63', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('348', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '63', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('349', '06a77708-8267-43d3-b66c-b69209c76937', '63', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    物料交期信息查看(产品经理、产品SE、成本总监、物流总监)
    ('350', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '64', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('351', '65b24875-6c22-417d-954c-a7d162b972e4', '64', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('352', '84a5117f-e6c8-4583-b3f4-7fc105336004', '64', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('353', '06a77708-8267-43d3-b66c-b69209c76937', '64', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    产品库-品牌查看(产品经理、产品SE)
    ('354', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '65', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('355', '65b24875-6c22-417d-954c-a7d162b972e4', '65', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --    产品库-产品文档查看(产品经理、产品SE、品牌维护人)
    ('356', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '66', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('357', '65b24875-6c22-417d-954c-a7d162b972e4', '66', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('358', '9867bda5-2679-4561-a978-33cb5bafbcd0', '66', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('400', '65b24875-6c22-417d-954c-a7d162b972e4', '28', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('401', '59b34b85-70f0-4598-b072-c44c0f8b050f', '28', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin')
    ;





    -- 应前端要求增加菜单路由，通过更新语句实现  2025-03-26
    -- 先更新所有一级菜单（无父级）
    UPDATE auth_menu
    SET url = id
    WHERE menu_level = 1;

    -- 再更新所有二级菜单（带父级）
    UPDATE auth_menu AS child
    SET url = parent.id || '/' || child.id
    FROM auth_menu AS parent
    WHERE child.parent_id = parent.id
      AND child.menu_level = 2;

UPDATE auth_role SET name='方案经理' WHERE id='9d2351be-54bb-461c-bcef-269268794f2a';

-- 新增材料助理角色 2025-04-07
insert into auth_role
    (id, name, code, create_time, update_time, create_by, update_by)
	values
	( '1e9e7696-2708-461f-8120-183bfc7bf1b1', '材料助理', 'material-assistant', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
	( '1e9e7696-2708-461f-8120-1234567890aa', '工作台角色', 'workbench-role', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system')
	;

-- 新增需求管理按钮权限码 2025-04-07
INSERT INTO auth_permission
    (id, "name", "type", permission_code, create_time, update_time, create_by, update_by, menu_id)
VALUES
    ('69', '需求管理-需求详情-新增选型单', 1, 'demandManagement.detail.add', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('70', '需求管理-需求详情-编辑选型单', 1, 'demandManagement.detail.edit', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('71', '需求管理-需求详情-删除选型单', 1, 'demandManagement.detail.delete', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('72', '需求管理-需求详情-创建物料', 1, 'demandManagement.detail.createMaterial', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('73', '需求管理-需求详情-创建物料-成本拆份', 1, 'demandManagement.detail.createMaterial.costSplitting', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('74', '需求管理-成本总监编辑选型单', 1, 'demandManagement.cost.conversion', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('75', '商机管理-选型需求', 1, 'selectionRequirements', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
    ('76', '需求管理-需求详情', 1, 'demandManagement.detail', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL)
    ;

-- 更新需求管理权限点 先删后插 2025-04-10
delete from auth_role_permission where permission_id = '54';
INSERT INTO auth_role_permission
    (id, role_id, permission_id, create_time, update_time, create_by, update_by)
    values
    -- 需求管理(产品经理、产品SE、成本总监、材料助理)
    ('140','70ef3496-9c4b-4f99-992d-4a40acea03aa', '54', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    ('141','65b24875-6c22-417d-954c-a7d162b972e4', '54', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    ('142','84a5117f-e6c8-4583-b3f4-7fc105336004', '54', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    ('143','1e9e7696-2708-461f-8120-183bfc7bf1b1', '54', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),

    -- 需求管理-成本总监编辑选型单(成本总监)
    ('408','84a5117f-e6c8-4583-b3f4-7fc105336004', '74', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    -- 需求管理-需求详情-新增选型单(产品经理、产品SE)
    ('409','70ef3496-9c4b-4f99-992d-4a40acea03aa', '69', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    ('410','65b24875-6c22-417d-954c-a7d162b972e4', '69', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    -- 需求管理-需求详情-编辑选型单(产品经理、产品SE)
    ('411','70ef3496-9c4b-4f99-992d-4a40acea03aa', '70', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    ('412','65b24875-6c22-417d-954c-a7d162b972e4', '70', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    -- 需求管理-需求详情-删除选型单(产品经理、产品SE)
    ('413','70ef3496-9c4b-4f99-992d-4a40acea03aa', '71', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    ('414','65b24875-6c22-417d-954c-a7d162b972e4', '71', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    -- 需求管理-需求详情-创建物料(产品经理、产品SE、成本总监)
    -- ('415','70ef3496-9c4b-4f99-992d-4a40acea03aa', '72', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    -- ('416','65b24875-6c22-417d-954c-a7d162b972e4', '72', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    -- ('417','84a5117f-e6c8-4583-b3f4-7fc105336004', '72', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    -- 需求管理-需求详情-创建物料-成本拆份(成本总监)
    ('418','84a5117f-e6c8-4583-b3f4-7fc105336004', '73', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    -- 商机管理-选型需求(方案经理)
    ('419','84a5117f-e6c8-4583-b3f4-7fc105336004', '75', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    -- 需求管理-需求详情按钮(产品经理、产品SE、成本总监、材料助理)
    ('420','70ef3496-9c4b-4f99-992d-4a40acea03aa', '76', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    ('421','65b24875-6c22-417d-954c-a7d162b972e4', '76', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    ('422','84a5117f-e6c8-4583-b3f4-7fc105336004', '76', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system'),
    ('423','1e9e7696-2708-461f-8120-183bfc7bf1b1', '76', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system')
    ;

    --2025-04-10权限点 成本总监 选型单成本编辑-成本拆分
INSERT INTO auth_permission
(id, "name", "type", permission_code, create_time, update_time, create_by, update_by, menu_id)
VALUES
      ('67', '产品管理-成本管理-招采成本-编辑', 1, 'procurement.cost.edit', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
      ('68', '产品管理-成本管理-招采成本-成本拆分', 1, 'product.material.batch.update.cost', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
      ('80', '产品管理-成本管理-招采成本-成本查看', 1, 'procurement.cost.view', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL),
      ('81', '产品管理-成本管理-招采成本-物料成本查看', 1, 'product.material.cost.view', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin', NULL);

INSERT INTO auth_role_permission
      (id, role_id, permission_id, create_time, update_time, create_by, update_by)
      values
      ('402', '84a5117f-e6c8-4583-b3f4-7fc105336004', '67', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
      ('403', '84a5117f-e6c8-4583-b3f4-7fc105336004', '68', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
      ('424', '84a5117f-e6c8-4583-b3f4-7fc105336004', '80', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
      ('425', '84a5117f-e6c8-4583-b3f4-7fc105336004', '81', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
--    工作台角色菜单  工作台
      ('404', '1e9e7696-2708-461f-8120-1234567890aa', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
--    工作台角色菜单  我的工作台
      ('405', '1e9e7696-2708-461f-8120-1234567890aa', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
      ('426', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
      ('427', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
      ('428', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin')
      ;



    -- 2025-04-21 权限点 产品信息配置(产品小类维护)-材料类别-编辑 产品信息配置(产品小类维护)-采购商务-编辑
    INSERT INTO auth_permission (id, name, "type", permission_code, menu_id, create_time, update_time, create_by, update_by) VALUES
    ('82', '产品信息配置(产品小类维护)-材料类别-编辑', 1, 'productCategory.material.cat.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('83', '产品信息配置(产品小类维护)-采购商务-编辑', 1, 'productCategory.procurement.business.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

    INSERT INTO auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by) VALUES
    -- 2025-04-21 角色权限点 产品经理 产品信息配置(产品小类维护)-材料类别-编辑 产品信息配置(产品小类维护)-采购商务-编辑
    ('429', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '82', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('430', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '83', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    -- 2025-04-21 角色权限点 产品SE 产品信息配置(产品小类维护)-材料类别-编辑 产品信息配置(产品小类维护)-采购商务-编辑
    ('431', '65b24875-6c22-417d-954c-a7d162b972e4', '82', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('432', '65b24875-6c22-417d-954c-a7d162b972e4', '83', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

    -- 2025-04-23 添加采购商务角色
    INSERT INTO auth_role(id, "name", code, create_time, update_time, create_by, update_by)VALUES
    ('1e9e7696-2708-461f-8120-2697cc159357', '采购商务', 'procurement-business', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system');

    -- 2025-04-26 添加物料成本变更权限码
    INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
    ('84', '物料成本变更权限码', 1, 'material.cost.info.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

    -- 2025-04-27 添加成本总监的物料成本变更权限码
    INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by)
    VALUES('433', '84a5117f-e6c8-4583-b3f4-7fc105336004', '84', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

    -- 2025-04-27 添加产品需求看板编辑权限码
    INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
        ('85', '产品需求看板信息编辑权限码', 1, 'product.requirement.dashboard.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');
         -- 2025-04-27 添加方案经理、产品经理、产品SE、材料助理的产品需求看板编辑权限
    INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by)
        VALUES
        ('434', '9d2351be-54bb-461c-bcef-269268794f2a', '85', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
        ('435', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '85', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
        ('436', '65b24875-6c22-417d-954c-a7d162b972e4', '85', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
        ('437', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '85', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

    -- 2025-04-28 添加产品需求看板字段编辑权限码
        INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
            ('86', '产品需求看板代码状态编辑权限码', 1, 'product.requirement.dashboard.code.status.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
            ('87', '产品需求看板投标品牌编辑权限码', 1, 'product.requirement.dashboard.bid.brand.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
            ('88', '产品需求看板招标时间编辑权限码', 1, 'product.requirement.dashboard.bid.time.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
            ('89', '产品需求看板需求确认时间编辑权限码', 1, 'product.requirement.dashboard.demand.confirmation.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
            ('90', '产品需求看板状态-资源模式-期望开标编辑权限码', 1, 'product.requirement.dashboard.status.resource.expect.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');
    -- 2025-05-07 添加投标结论相关权限码
    INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
    ('91', '投标结论查看', 1, 'project.bidding.conclusion.view', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('92', '投标结论编辑', 1, 'project.bidding.conclusion.update', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
        ('93', '售前售后交接信息填写', 1, 'project.handover.completed.add', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');
    -- 2025-05-15 添加项目管理相关权限码
    INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
    ('94', '项目管理编辑', 1, 'project.item.update', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('95', '售前售后交接信息更新', 1, 'project.handover.completed.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('96', '工程交付信息更新', 1, 'project.delivery.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('97', '项目管理-遗留问题新增', 1, 'item.legacy.issues.add', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('98', '项目管理-遗留问题编辑', 1, 'item.legacy.issues.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('99', '项目管理-遗留问题删除', 1, 'item.legacy.issues.delete', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('100', '项目管理-文档新增', 1, 'item.document.add', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('101', '项目管理-文档修改', 1, 'item.document.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('102', '项目管理-文档删除', 1, 'item.document.delete', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('103', '项目管理-文档查询', 1, 'item.document.view', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('104', '产品库-物料规格书备注维护', 1, 'material.specification.remark.maintain', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');


        --2025-05-14 添加售前售后交接信息更新、工程交付信息更新权限码
    INSERT INTO public.auth_permission
    (id, name, "type", permission_code, menu_id, create_time, update_time, create_by, update_by)
    VALUES('95', '售前售后交接信息更新', 1, 'project.handover.completed.edit', NULL, '2025-05-14 14:00:00', '2025-05-14 14:00:00', 'admin', 'admin');
    INSERT INTO public.auth_permission
    (id, name, "type", permission_code, menu_id, create_time, update_time, create_by, update_by)
    VALUES('96', '工程交付信息更新', 1, 'project.delivery.edit', NULL, '2025-05-14 14:00:00', '2025-05-14 14:00:00', 'admin', 'admin');
      -- 2025-04-28 添加产品SE或材料助理 产品需求看板 代码状态编辑权限
        INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by)
            VALUES
            ('438', '65b24875-6c22-417d-954c-a7d162b972e4', '86', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
            ('439', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '86', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');
    -- 2025-04-28 添加产品SE 产品需求看板 产品需求看板投标品牌编辑权限
        INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by)
            VALUES
            ('440', '65b24875-6c22-417d-954c-a7d162b972e4', '87', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');
 -- 2025-04-28 添加方产品经理、产品SE和材料助理的 产品需求看板 产品需求看板招标时间编辑权限
        INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by)
            VALUES
            ('441', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '88', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
            ('442', '65b24875-6c22-417d-954c-a7d162b972e4', '88', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
            ('443', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '88', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');
  -- 2025-04-28 添加方案经理或产品SE 产品需求看板 需求确认时间编辑权限
        INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by)
            VALUES
            ('444', '9d2351be-54bb-461c-bcef-269268794f2a', '89', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
            ('445', '65b24875-6c22-417d-954c-a7d162b972e4', '89', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');
   -- 2025-04-28 添加方案经理 产品需求看板 产品需求看板状态-资源模式-期望开标编辑权限
        INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by)
            VALUES
            ('446', '9d2351be-54bb-461c-bcef-269268794f2a', '90', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

    -- 2025-05-07 添加方案经理投标结论相关权限码
INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by) VALUES
('447', '9d2351be-54bb-461c-bcef-269268794f2a', '91', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('448', '9d2351be-54bb-461c-bcef-269268794f2a', '92', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('449', '9d2351be-54bb-461c-bcef-269268794f2a', '93', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

  -- 2025-05-12 添加项目管理相关权限码
    INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
    ('94', '项目管理编辑', 1, 'project.item.update', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

  -- 2025-05-12 添加交付TD相关权限码
    INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by) VALUES
    ('450', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '94', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');
    -- 2025-05-12 增加项目PD角色
    INSERT INTO public.auth_role (id,"name",code,create_time,update_time,create_by,update_by) VALUES
    	('251ae6e3-2018-4740-8cd8-8eaa57de8141','项目PD','project-pd','2025-04-11 13:27:32','2025-04-11 13:27:32','system','system');
    -- 2025-05-15 添加设计负责人角色
    INSERT INTO public.auth_role
    (id, "name", code, create_time, update_time, create_by, update_by)
    VALUES('sjfzrae6a-c863-406c-123456789aaabbbc', '设计负责人', 'design-principal', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system');
    --2025-05-14 配置交付TD 售前售后交接信息更新、工程交付信息更新权限码
    INSERT INTO public.auth_role_permission
    (id, role_id, permission_id, create_time, update_time, create_by, update_by)
    VALUES('451', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '95', '2025-05-14 14:00:00', '2025-05-14 14:00:00', 'admin', 'admin');
    INSERT INTO public.auth_role_permission
    (id, role_id, permission_id, create_time, update_time, create_by, update_by)
    VALUES('452', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '96', '2025-05-14 14:00:00', '2025-05-14 14:00:00', 'admin', 'admin');

    --2025-05-15 修改菜单排序
    update public.auth_menu set sort = 1 where id = 'clueManagement';

    update public.auth_menu set sort = 2 where id = 'businessManagement';

    update public.auth_menu set sort = 3 where id = 'projectManagement';

   --2025-05-15 配置交付TD、设计负责人在项目管理中的部分按钮权限码

    INSERT INTO public.auth_role_permission
    (id, role_id, permission_id, create_time, update_time, create_by, update_by)
    VALUES
    ('453', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '97', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('454', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '98', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('455', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '99', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('456', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '100', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('457', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '101', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('458', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '102', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('459', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '103', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),  'admin', 'admin'),
    ('460', 'sjfzrae6a-c863-406c-123456789aaabbbc', '100', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),  'admin', 'admin'),
    ('461', 'sjfzrae6a-c863-406c-123456789aaabbbc', '101', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),  'admin', 'admin'),
    ('462', 'sjfzrae6a-c863-406c-123456789aaabbbc', '102', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('463', 'sjfzrae6a-c863-406c-123456789aaabbbc', '103', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');


--- 2025-05-23
DROP TABLE IF EXISTS auth_department_role_resource;
CREATE TABLE public.auth_department_role_resource (
	id text NOT NULL, -- 关联ID
	department_id text NOT NULL, -- 部门id
	role_id text NOT NULL, -- 角色ID
	resource_id text NOT NULL, -- 资源ID，对应的是auth_resource表中的ID
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建用户
	update_by text NOT NULL, -- 更新用户
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.auth_department_role_resource IS '部门角色资源关联表';
COMMENT ON COLUMN public.auth_department_role_resource.id IS '关联ID';
COMMENT ON COLUMN public.auth_department_role_resource.department_id IS ' 部门id';
COMMENT ON COLUMN public.auth_department_role_resource.role_id IS '角色ID';
COMMENT ON COLUMN public.auth_department_role_resource.resource_id IS '资源ID，对应的是auth_resource表中的ID';
COMMENT ON COLUMN public.auth_department_role_resource.create_time IS '创建时间';
COMMENT ON COLUMN public.auth_department_role_resource.update_time IS '更新时间';
COMMENT ON COLUMN public.auth_department_role_resource.create_by IS '创建用户';
COMMENT ON COLUMN public.auth_department_role_resource.update_by IS '更新用户';


   --2025-05-21 产品经理、产品SE在物料规格书中的备注权限码

    INSERT INTO public.auth_role_permission
    (id, role_id, permission_id, create_time, update_time, create_by, update_by)
    VALUES
    ('464', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '104', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('465', '65b24875-6c22-417d-954c-a7d162b972e4', '104', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('467', '1e9e7696-2708-461f-8120-1234567890aa', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('468', '1e9e7696-2708-461f-8120-1234567890aa', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('469', '1e9e7696-2708-461f-8120-1234567890aa', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('470', '1e9e7696-2708-461f-8120-1234567890aa', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('471', '1e9e7696-2708-461f-8120-1234567890aa', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');





INSERT INTO auth_menu (id, name, url, icon, menu_level, sort, parent_id, create_time, update_time, create_by, update_by) VALUES
	 ('systemAccessStatistics', '{"zh-CN":"系统运营分析","en-US":"System Statistic"}', 'billboard/systemAccessStatistics', '0', 0, 15, 'billboard', '2025-02-25 14:46:58', '2025-02-25 14:46:58', 'admin', 'admin');



INSERT INTO auth_permission (id, name, type, permission_code, create_time, update_time, create_by, update_by, menu_id)
VALUES (105, '系统运营分析', 0, 'systemAccessStatistics', '2025-02-08 09:15:01', '2025-02-08 09:15:01', 'admin', 'admin', 'systemAccessStatistics');




INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by) VALUES
('480', '53806c42-5806-4c51-81ff-15c31196228e', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('481', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('482', '65b24875-6c22-417d-954c-a7d162b972e4', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('483', '84a5117f-e6c8-4583-b3f4-7fc105336004', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('484', 'cff28622-86d2-4048-8a4a-cab23a161009', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('485', '133c7aa1-080c-4231-ab75-f219abb5eeea', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('486', '06a77708-8267-43d3-b66c-b69209c76937', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('487', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('488', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('489', '9867bda5-2679-4561-a978-33cb5bafbcd0', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('490', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('491', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('492', '7ab75384-319d-4b6d-8827-adc55ab0539f', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('493', '484da0ef-0140-4998-95f4-5386a8048d7c', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('494', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('495', '59b34b85-70f0-4598-b072-c44c0f8b050f', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('496', '9d2351be-54bb-461c-bcef-269268794f2a', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('497', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('498', '1e9e7696-2708-461f-8120-2697cc159357', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('499', '1e9e7696-2708-461f-8120-1234567890aa', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('500', '251ae6e3-2018-4740-8cd8-8eaa57de8141', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('501', 'sjfzrae6a-c863-406c-123456789aaabbbc', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');


INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by) VALUES
('502', '53806c42-5806-4c51-81ff-15c31196228e', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('503', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('504', '65b24875-6c22-417d-954c-a7d162b972e4', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('505', '84a5117f-e6c8-4583-b3f4-7fc105336004', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('506', 'cff28622-86d2-4048-8a4a-cab23a161009', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('507', '133c7aa1-080c-4231-ab75-f219abb5eeea', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('508', '06a77708-8267-43d3-b66c-b69209c76937', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('509', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('510', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('511', '9867bda5-2679-4561-a978-33cb5bafbcd0', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('512', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('513', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('514', '7ab75384-319d-4b6d-8827-adc55ab0539f', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('515', '484da0ef-0140-4998-95f4-5386a8048d7c', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('516', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('517', '59b34b85-70f0-4598-b072-c44c0f8b050f', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('518', '9d2351be-54bb-461c-bcef-269268794f2a', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('519', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('520', '1e9e7696-2708-461f-8120-2697cc159357', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('521', '1e9e7696-2708-461f-8120-1234567890aa', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('522', '251ae6e3-2018-4740-8cd8-8eaa57de8141', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('523', 'sjfzrae6a-c863-406c-123456789aaabbbc', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

--启用 uuid-ossp 扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- 功能评价表
-- 删除旧表（如果存在）
DROP TABLE IF EXISTS eval_checklist;

-- 创建新表，id 为 UUID 类型并自动填充
CREATE TABLE eval_checklist (
    id           UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- 主键，自动生成 UUID
    function_id  VARCHAR(100) NOT NULL,                        -- 功能id
    function_name VARCHAR(255) NOT NULL,                       -- 功能名称
    menu_id      VARCHAR(100),                                 -- 菜单id（可为空）
    create_by    VARCHAR(32) NOT NULL,                         -- 创建者
    create_time  VARCHAR(32) NOT NULL,                         -- 创建时间
    update_by    VARCHAR(32),                                  -- 更新者（可为空）
    update_time  VARCHAR(32)                                   -- 更新时间（可为空）
);

-- 添加注释部分
COMMENT ON COLUMN eval_checklist.id IS '主键';
COMMENT ON COLUMN eval_checklist.function_id IS '功能id';
COMMENT ON COLUMN eval_checklist.function_name IS '功能名称';
COMMENT ON COLUMN eval_checklist.menu_id IS '菜单id';
COMMENT ON COLUMN eval_checklist.create_by IS '创建者';
COMMENT ON COLUMN eval_checklist.create_time IS '创建时间';
COMMENT ON COLUMN eval_checklist.update_by IS '更新者';
COMMENT ON COLUMN eval_checklist.update_time IS '更新时间';


INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('0546e789-03c1-41b6-82cb-ecd9091f7e33'::uuid, 'workbench:evalPoint', '我的工作台', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('ba7063d9-4512-4e23-8c74-50d17545c525'::uuid, 'productManagement:productLibrary:material:evalPoint', '产品管理-产品库-物料', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('cf3f722f-f600-4a7b-b677-dd7422dbfb6a'::uuid, 'productManagement:productLibrary:coreParameters:evalPoint', '产品管理-产品库-核心参数', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b550bd1e-8c45-4e03-942f-432ecb406a7d'::uuid, 'productManagement:productLibrary:brand:evalPoint', '产品管理-产品库-品牌', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('0139e97f-5b99-40d5-ac6c-14ca62b26130'::uuid, 'productManagement:productLibrary:document:evalPoint', '产品管理-产品库-文档', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('a9598957-1578-4fdd-a170-f6bac81e9bdd'::uuid, 'productManagement:productLibrary:faq:evalPoint', '产品管理-产品库-FAQ', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('442b10d2-cbf0-4177-9238-a8fe0b99f3cb'::uuid, 'productManagement:productLibrary:echoWall:evalPoint', '产品管理-产品库-回音墙', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('692b10d2-cbf0-4177-a638-a8fe0b99f3cc'::uuid, 'productManagement:productLibrary:documentAnalyse:evalPoint', 'LTC管理-商机管理-标书分析', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('4769d34b-3a45-4eef-a9c0-a6ef5cbc229c'::uuid, 'productManagement:demandManagement:evalPoint', '产品管理-需求管理', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b88fc6ef-52d0-4459-b7b6-6f4717cbd8c7'::uuid, 'productManagement:selectionManagement:evalPoint', '产品管理-选型管理', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('1181453b-fc26-4d25-909c-f3ba032fe68c'::uuid, 'productManagement:costManagement:evalPoint', '产品管理-成本管理', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('f08f495c-0031-4948-9a1d-3703a4c392fd'::uuid, 'LTCManagement:businessManagement:info:evalPoint', 'LTC管理-商机管理-商机信息', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b6c29ccc-b629-4c90-8626-c078503a9af0'::uuid, 'LTCManagement:businessManagement:selectRequire:evalPoint', 'LTC管理-商机管理-选型需求', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b9593ace-862c-44b9-83b2-f52bbe717c0b'::uuid, 'LTCManagement:businessManagement:clarificationDocument:evalPoint', 'LTC管理-商机管理-标书澄清', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('bcdea7d9-c977-4a3d-802b-82809c6c2547'::uuid, 'LTCManagement:businessManagement:planProduction:evalPoint', 'LTC管理-商机管理-方案制作', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b3028bb3-176b-4271-ab92-51143e3d5c8a'::uuid, 'LTCManagement:businessManagement:brandGuid:evalPoint', 'LTC管理-商机管理-品牌引导', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('af8fcd53-7afb-4a69-aa47-462c3e1c5a15'::uuid, 'LTCManagement:businessManagement:document:evalPoint', 'LTC管理-商机管理-文档', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('ebbe34cc-ef21-42cb-920d-0d6601426ab0'::uuid, 'LTCManagement:businessManagement:requireBoard:evalPoint', 'LTC管理-商机管理-产品需求看板', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('ec699875-eef2-41fe-bf46-d758c2855be6'::uuid, 'LTCManagement:projectManagement:overviewInfo:evalPoint', 'LTC管理-项目管理-概览信息', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('d8a90824-2731-4750-8bfd-d9f797cfbfa8'::uuid, 'LTCManagement:projectManagement:handoverInfo:evalPoint', 'LTC管理-项目管理-交接信息', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('5e9c6e99-d1f5-4398-b8de-811e2b846e82'::uuid, 'LTCManagement:projectManagement:enginDeep:evalPoint', 'LTC管理-项目管理-工程深化设计', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('bf82e806-889c-4a11-a723-7dff571a4647'::uuid, 'LTCManagement:projectManagement:projectDelivery:evalPoint', 'LTC管理-项目管理-工程交付', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('da52756b-d6d9-4463-a870-5b38bc488d35'::uuid, 'LTCManagement:projectManagement:document:evalPoint', 'LTC管理-项目管理-文档', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('6dd88ee1-f5da-48f7-aaa4-5a526a83f557'::uuid, 'knowledgeBase:document:evalPoint', '知识库-文档', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('f5cc3be9-f127-45da-b7dc-674577a7e507'::uuid, 'knowledgeBase:faq:evalPoint', '知识库-faq', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('033086fe-34fd-4dae-9b89-5e662229f366'::uuid, 'billboard:projectBrand:evalPoint', '看板-项目品牌评分', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('91fcba7d-6122-4b8b-bc6c-93a6bfd968cf'::uuid, 'billboard:productBrand:evalPoint', '看板-产品品牌评分', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('4d02f0e8-6e68-43b4-b141-31043385a062'::uuid, 'billboard:systemAccess:evalPoint', '看板-系统访问统计', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);


-- 用户评价数据表
DROP TABLE IF EXISTS eval_data;

CREATE TABLE eval_data (
    id            VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    function_id   VARCHAR(100) NOT NULL,             -- 功能id
    user_id       VARCHAR(100) NOT NULL,             -- 用户id
    user_name     VARCHAR(255) NOT NULL,             -- 用户名称
    operation_type INTEGER NOT NULL,                 -- 操作类型: 0:赞,1:踩
    eval_content  VARCHAR(500)  NULL,                -- 评价内容
    create_by     VARCHAR(32) NOT NULL,              -- 创建者
    create_time   VARCHAR(32) NOT NULL               -- 创建时间
);
--  创建function_id，user_id，operation_type组合唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_eval_data_function_user_op
ON eval_data(function_id, user_id, operation_type);

-- 注释部分
COMMENT ON COLUMN eval_data.id IS '主键';
COMMENT ON COLUMN eval_data.function_id IS '功能id';
COMMENT ON COLUMN eval_data.user_id IS '用户id';
COMMENT ON COLUMN eval_data.user_name IS '用户名称';
COMMENT ON COLUMN eval_data.operation_type IS '操作类型: 0:赞,1:踩';
COMMENT ON COLUMN eval_data.eval_content IS '评价内容';
COMMENT ON COLUMN eval_data.create_by IS '创建者';
COMMENT ON COLUMN eval_data.create_time IS '创建时间';

-- 用户评价历史数据表
DROP TABLE IF EXISTS eval_data_history;

CREATE TABLE eval_data_history (
    id            VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    function_id   VARCHAR(100) NOT NULL,             -- 功能id
    user_id       VARCHAR(100) NOT NULL,             -- 用户id
    user_name     VARCHAR(255) NOT NULL,             -- 用户名称
    operation_type INTEGER NOT NULL,                 -- 操作类型: 0:赞,1:踩
    eval_content  VARCHAR(500)  NULL,                -- 评价内容
    create_by     VARCHAR(32) NOT NULL,              -- 创建者
    create_time   VARCHAR(32) NOT NULL               -- 创建时间
);

-- 注释部分
COMMENT ON COLUMN eval_data_history.id IS '主键';
COMMENT ON COLUMN eval_data_history.function_id IS '功能id';
COMMENT ON COLUMN eval_data_history.user_id IS '用户id';
COMMENT ON COLUMN eval_data_history.user_name IS '用户名称';
COMMENT ON COLUMN eval_data_history.operation_type IS '操作类型: 0:赞,1:踩';
COMMENT ON COLUMN eval_data_history.eval_content IS '评价内容';
COMMENT ON COLUMN eval_data_history.create_by IS '创建者';
COMMENT ON COLUMN eval_data_history.create_time IS '创建时间';



-- 登录统计日表
DROP TABLE IF EXISTS login_stat_day;

CREATE TABLE login_stat_day (
    id         VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    day        INTEGER   NOT NULL,             -- 日期（例：20250620）
    dept_id    VARCHAR(100)  NULL,             -- 组织ID
    user_id    VARCHAR(100) NOT NULL,             -- 用户ID
    login_time VARCHAR(32)  NOT NULL              -- 登录时间
);

-- 注释部分
COMMENT ON COLUMN login_stat_day.id IS '主键';
COMMENT ON COLUMN login_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN login_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN login_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN login_stat_day.login_time IS '登录时间';

-- 登录统计周表
DROP TABLE IF EXISTS login_stat_week;

CREATE TABLE login_stat_week (
    id      VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    day     INTEGER   NOT NULL,                -- 日期（例：202501）
    dept_id VARCHAR(100)  NULL,             -- 组织ID
    user_id VARCHAR(100) NOT NULL,             -- 用户ID
    num     INTEGER  NOT NULL                  -- 登录天数
);

-- 注释部分
COMMENT ON COLUMN login_stat_week.id IS '主键';
COMMENT ON COLUMN login_stat_week.day IS '日期（例：202501）';
COMMENT ON COLUMN login_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN login_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN login_stat_week.num IS '登录天数';

-- 登录统计月表
DROP TABLE IF EXISTS login_stat_month;

CREATE TABLE login_stat_month (
    id      VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    day     INTEGER   NOT NULL,                -- 日期（例：202506）
    dept_id VARCHAR(100)  NULL,             -- 组织ID
    user_id VARCHAR(100) NOT NULL,             -- 用户ID
    num     INTEGER  NOT NULL                  -- 登录天数
);

-- 注释部分
COMMENT ON COLUMN login_stat_month.id IS '主键';
COMMENT ON COLUMN login_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN login_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN login_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN login_stat_month.num IS '登录天数';


-- 登录统计年表
DROP TABLE IF EXISTS login_stat_year;

CREATE TABLE login_stat_year (
    id      VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    day     INTEGER   NOT NULL,                -- 日期（例：2025）
    dept_id VARCHAR(100)  NULL,             -- 组织ID
    user_id VARCHAR(100) NOT NULL,             -- 用户ID
    num     INTEGER  NOT NULL                  -- 登录天数
);

-- 注释部分
COMMENT ON COLUMN login_stat_year.id IS '主键';
COMMENT ON COLUMN login_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN login_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN login_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN login_stat_year.num IS '登录天数';

-- 搜索统计和智能体统计表更新脚本
-- 搜索统计日表
CREATE TABLE IF NOT EXISTS search_stat_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    file_num TEXT NOT NULL,
    faq_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE search_stat_day IS '搜索统计日表';
COMMENT ON COLUMN search_stat_day.id IS '主表id';
COMMENT ON COLUMN search_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN search_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN search_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN search_stat_day.product_category_id IS '产品小类id';
COMMENT ON COLUMN search_stat_day.material_num IS '物料次数';
COMMENT ON COLUMN search_stat_day.file_num IS '文档次数';
COMMENT ON COLUMN search_stat_day.faq_num IS 'FAQ次数';
COMMENT ON COLUMN search_stat_day.create_time IS '记录时间';

-- 搜索统计周表
CREATE TABLE IF NOT EXISTS search_stat_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    file_num TEXT NOT NULL,
    faq_num TEXT NOT NULL
);

COMMENT ON TABLE search_stat_week IS '搜索统计周表';
COMMENT ON COLUMN search_stat_week.id IS '主表id';
COMMENT ON COLUMN search_stat_week.day IS '日期（例：202506）';
COMMENT ON COLUMN search_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN search_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN search_stat_week.product_category_id IS '产品小类id';
COMMENT ON COLUMN search_stat_week.material_num IS '物料次数';
COMMENT ON COLUMN search_stat_week.file_num IS '文档次数';
COMMENT ON COLUMN search_stat_week.faq_num IS 'FAQ次数';

-- 搜索统计月表
CREATE TABLE IF NOT EXISTS search_stat_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    file_num TEXT NOT NULL,
    faq_num TEXT NOT NULL
);

COMMENT ON TABLE search_stat_month IS '搜索统计月表';
COMMENT ON COLUMN search_stat_month.id IS '主表id';
COMMENT ON COLUMN search_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN search_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN search_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN search_stat_month.product_category_id IS '产品小类id';
COMMENT ON COLUMN search_stat_month.material_num IS '物料次数';
COMMENT ON COLUMN search_stat_month.file_num IS '文档次数';
COMMENT ON COLUMN search_stat_month.faq_num IS 'FAQ次数';

-- 搜索统计年表
CREATE TABLE IF NOT EXISTS search_stat_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    file_num TEXT NOT NULL,
    faq_num TEXT NOT NULL
);

COMMENT ON TABLE search_stat_year IS '搜索统计年表';
COMMENT ON COLUMN search_stat_year.id IS '主表id';
COMMENT ON COLUMN search_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN search_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN search_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN search_stat_year.product_category_id IS '产品小类id';
COMMENT ON COLUMN search_stat_year.material_num IS '物料次数';
COMMENT ON COLUMN search_stat_year.file_num IS '文档次数';
COMMENT ON COLUMN search_stat_year.faq_num IS 'FAQ次数';

-- 智能体统计日表
CREATE TABLE IF NOT EXISTS intelligence_stat_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    bid_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE intelligence_stat_day IS '智能体统计日表';
COMMENT ON COLUMN intelligence_stat_day.id IS '主表id';
COMMENT ON COLUMN intelligence_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN intelligence_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN intelligence_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN intelligence_stat_day.material_num IS '物料智能体次数';
COMMENT ON COLUMN intelligence_stat_day.bid_num IS '标书分析智能体次数';
COMMENT ON COLUMN intelligence_stat_day.create_time IS '记录时间';

-- 智能体统计周表
CREATE TABLE IF NOT EXISTS intelligence_stat_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    bid_num TEXT NOT NULL
);

COMMENT ON TABLE intelligence_stat_week IS '智能体统计周表';
COMMENT ON COLUMN intelligence_stat_week.id IS '主表id';
COMMENT ON COLUMN intelligence_stat_week.day IS '日期（例：202506）';
COMMENT ON COLUMN intelligence_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN intelligence_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN intelligence_stat_week.material_num IS '物料智能体次数';
COMMENT ON COLUMN intelligence_stat_week.bid_num IS '标书分析智能体次数';

-- 智能体统计月表
CREATE TABLE IF NOT EXISTS intelligence_stat_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    bid_num TEXT NOT NULL
);

COMMENT ON TABLE intelligence_stat_month IS '智能体统计月表';
COMMENT ON COLUMN intelligence_stat_month.id IS '主表id';
COMMENT ON COLUMN intelligence_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN intelligence_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN intelligence_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN intelligence_stat_month.material_num IS '物料智能体次数';
COMMENT ON COLUMN intelligence_stat_month.bid_num IS '标书分析智能体次数';

-- 智能体统计年表
CREATE TABLE IF NOT EXISTS intelligence_stat_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    bid_num TEXT NOT NULL
);

COMMENT ON TABLE intelligence_stat_year IS '智能体统计年表';
COMMENT ON COLUMN intelligence_stat_year.id IS '主表id';
COMMENT ON COLUMN intelligence_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN intelligence_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN intelligence_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN intelligence_stat_year.material_num IS '物料智能体次数';
COMMENT ON COLUMN intelligence_stat_year.bid_num IS '标书分析智能体次数';

-- 页面访问统计日表
DROP TABLE IF EXISTS page_stat_day;

CREATE TABLE page_stat_day (
    id                  TEXT NOT NULL PRIMARY KEY, -- 主表id
    day                 INTEGER NOT NULL,             -- 日期（例：20250620）
    dept_id             TEXT  NULL,             -- 组织ID
    user_id             TEXT NOT NULL,             -- 用户ID
    product_category_id TEXT,                      -- 产品小类id
    type                TEXT,                      -- 1、品牌，2、物料，3文档，4、FAQ
    resource_id          TEXT  NULL,             -- 对应资源ID
    num                 INTEGER NOT NULL,             -- 访问次数
    create_time         TEXT NOT NULL              -- 记录时间
);

-- 注释部分
COMMENT ON COLUMN page_stat_day.id IS '主表id';
COMMENT ON COLUMN page_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN page_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN page_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN page_stat_day.product_category_id IS '产品小类id';
COMMENT ON COLUMN page_stat_day.type IS '1、品牌，2、物料，3文档，4、FAQ';
COMMENT ON COLUMN page_stat_day.resource_id IS '对应资源ID';
COMMENT ON COLUMN page_stat_day.num IS '访问次数';
COMMENT ON COLUMN page_stat_day.create_time IS '记录时间';

-- 页面访问统计周表
DROP TABLE IF EXISTS page_stat_week;

CREATE TABLE page_stat_week (
    id                  TEXT NOT NULL PRIMARY KEY, -- 主表id
    day                 INTEGER NOT NULL,             -- 日期（例：202506）
    dept_id             TEXT  NULL,             -- 组织ID
    user_id             TEXT NOT NULL,             -- 用户ID
    product_category_id TEXT,                      -- 产品小类id
    type                TEXT,                      -- 1、品牌，2、物料，3文档，4、FAQ
    resource_id          TEXT  NULL,             -- 对应资源ID
    num                 INTEGER NOT NULL              -- 访问次数
);

-- 注释部分
COMMENT ON COLUMN page_stat_week.id IS '主表id';
COMMENT ON COLUMN page_stat_week.day IS '日期（例：202506）';
COMMENT ON COLUMN page_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN page_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN page_stat_week.product_category_id IS '产品小类id';
COMMENT ON COLUMN page_stat_week.type IS '1、品牌，2、物料，3文档，4、FAQ';
COMMENT ON COLUMN page_stat_week.resource_id IS '对应资源ID';
COMMENT ON COLUMN page_stat_week.num IS '访问次数';


-- 页面访问统计月表
DROP TABLE IF EXISTS page_stat_month;

CREATE TABLE page_stat_month (
    id                  TEXT NOT NULL PRIMARY KEY, -- 主表id
    day                 INTEGER NOT NULL,             -- 日期（例：202506）
    dept_id             TEXT  NULL,             -- 组织ID
    user_id             TEXT NOT NULL,             -- 用户ID
    product_category_id TEXT,                      -- 产品小类id
    type                TEXT,                      -- 1、品牌，2、物料，3文档，4、FAQ
    resource_id          TEXT  NULL,             -- 对应资源ID
    num                 INTEGER NOT NULL              -- 访问次数
);

-- 注释部分
COMMENT ON COLUMN page_stat_month.id IS '主表id';
COMMENT ON COLUMN page_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN page_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN page_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN page_stat_month.product_category_id IS '产品小类id';
COMMENT ON COLUMN page_stat_month.type IS '1、品牌，2、物料，3文档，4、FAQ';
COMMENT ON COLUMN page_stat_month.resource_id IS '对应资源ID';
COMMENT ON COLUMN page_stat_month.num IS '访问次数';

-- 页面访问统计年表
DROP TABLE IF EXISTS page_stat_year;

CREATE TABLE page_stat_year (
    id                  TEXT NOT NULL PRIMARY KEY, -- 主表id
    day                 INTEGER NOT NULL,             -- 日期（例：2025）
    dept_id             TEXT  NULL,             -- 组织ID
    user_id             TEXT NOT NULL,             -- 用户ID
    product_category_id TEXT,                      -- 产品小类id
    type                TEXT,                      -- 1、品牌，2、物料，3文档，4、FAQ
    resource_id          TEXT  NULL,             -- 对应资源ID
    num                 INTEGER NOT NULL              -- 访问次数
);

-- 注释部分
COMMENT ON COLUMN page_stat_year.id IS '主表id';
COMMENT ON COLUMN page_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN page_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN page_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN page_stat_year.product_category_id IS '产品小类id';
COMMENT ON COLUMN page_stat_year.type IS '1、品牌，2、物料，3文档，4、FAQ';
COMMENT ON COLUMN page_stat_year.resource_id IS '对应资源ID';
COMMENT ON COLUMN page_stat_year.num IS '访问次数';

-- 文档下载统计日表
CREATE TABLE IF NOT EXISTS file_stat_day (
    id TEXT NOT NULL PRIMARY KEY,
    day INTEGER NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    download_num INTEGER NOT NULL,
    preview_num INTEGER NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE file_stat_day IS '文档下载统计日表';
COMMENT ON COLUMN file_stat_day.id IS '主表id';
COMMENT ON COLUMN file_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN file_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN file_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN file_stat_day.resource_id IS '资源id';
COMMENT ON COLUMN file_stat_day.download_num IS '下载次数';
COMMENT ON COLUMN file_stat_day.preview_num IS '预览次数';
COMMENT ON COLUMN file_stat_day.create_time IS '记录时间';

-- 文档下载统计周表
CREATE TABLE IF NOT EXISTS file_stat_week (
    id TEXT NOT NULL PRIMARY KEY,
    day INTEGER NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    download_num INTEGER NOT NULL,
    preview_num INTEGER NOT NULL
);

COMMENT ON TABLE file_stat_week IS '文档下载统计周表';
COMMENT ON COLUMN file_stat_week.id IS '主表id';
COMMENT ON COLUMN file_stat_week.day IS '日期（例：202506）';
COMMENT ON COLUMN file_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN file_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN file_stat_week.resource_id IS '资源id';
COMMENT ON COLUMN file_stat_week.download_num IS '下载次数';
COMMENT ON COLUMN file_stat_week.preview_num IS '预览次数';

-- 文档下载统计月表
CREATE TABLE IF NOT EXISTS file_stat_month (
    id TEXT NOT NULL PRIMARY KEY,
    day INTEGER NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    download_num INTEGER NOT NULL,
    preview_num INTEGER NOT NULL
);

COMMENT ON TABLE file_stat_month IS '文档下载统计月表';
COMMENT ON COLUMN file_stat_month.id IS '主表id';
COMMENT ON COLUMN file_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN file_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN file_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN file_stat_month.resource_id IS '资源id';
COMMENT ON COLUMN file_stat_month.download_num IS '下载次数';
COMMENT ON COLUMN file_stat_month.preview_num IS '预览次数';

-- 文档下载统计年表
CREATE TABLE IF NOT EXISTS file_stat_year (
    id TEXT NOT NULL PRIMARY KEY,
    day INTEGER NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    download_num INTEGER NOT NULL,
    preview_num INTEGER NOT NULL
);

COMMENT ON TABLE file_stat_year IS '文档下载统计年表';
COMMENT ON COLUMN file_stat_year.id IS '主表id';
COMMENT ON COLUMN file_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN file_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN file_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN file_stat_year.resource_id IS '资源id';
COMMENT ON COLUMN file_stat_year.download_num IS '下载次数';
COMMENT ON COLUMN file_stat_year.preview_num IS '预览次数';

-- 20250721
DROP TABLE IF EXISTS business_stat_day;
CREATE TABLE public.business_stat_day (
	id text NOT NULL,
	"day" text NULL,
	product_category_id text NULL,
	all_task_num int4 NULL,
	normal_task_num int4 NULL,
	extension_task_num int4 NULL,
	all_lectotype_num int4 NULL,
	bid_lectotype_num int4 NULL,
	appoint_lectotype_num int4 NULL,
	create_time text NULL
);
DROP TABLE IF EXISTS business_stat_week;
CREATE TABLE public.business_stat_week (
	id text NOT NULL,
	"day" text NULL,
	product_category_id text NULL,
	all_task_num int4 NULL,
	normal_task_num int4 NULL,
	extension_task_num int4 NULL,
	all_lectotype_num int4 NULL,
	bid_lectotype_num int4 NULL,
	appoint_lectotype_num int4 NULL,
	create_time text NULL
);
DROP TABLE IF EXISTS business_stat_month;
CREATE TABLE public.business_stat_month (
	id text NOT NULL,
	"day" text NULL,
	product_category_id text NULL,
	all_task_num int4 NULL,
	normal_task_num int4 NULL,
	extension_task_num int4 NULL,
	all_lectotype_num int4 NULL,
	bid_lectotype_num int4 NULL,
	appoint_lectotype_num int4 NULL,
	create_time text NULL
);
DROP TABLE IF EXISTS business_stat_year;
CREATE TABLE public.business_stat_year (
	id text NOT NULL,
	"day" text NULL,
	product_category_id text NULL,
	all_task_num int4 NULL,
	normal_task_num int4 NULL,
	extension_task_num int4 NULL,
	all_lectotype_num int4 NULL,
	bid_lectotype_num int4 NULL,
	appoint_lectotype_num int4 NULL,
	create_time text NULL
);

-- 资产数量统计方案 - 物料相关表
-- 物料资产编辑记录表
CREATE TABLE IF NOT EXISTS asset_material_upd (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_id TEXT NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产日表
CREATE TABLE IF NOT EXISTS asset_material_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    up_num int4 NOT NULL,
    up_change_num int4 NOT NULL,
    down_num int4 NOT NULL,
    down_change_num int4 NOT NULL,
    update_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产周表
CREATE TABLE IF NOT EXISTS asset_material_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    up_num int4 NOT NULL,
    up_change_num int4 NOT NULL,
    down_num int4 NOT NULL,
    down_change_num int4 NOT NULL,
    update_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产月表
CREATE TABLE IF NOT EXISTS asset_material_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    up_num int4 NOT NULL,
    up_change_num int4 NOT NULL,
    down_num int4 NOT NULL,
    down_change_num int4 NOT NULL,
    update_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产年表
CREATE TABLE IF NOT EXISTS asset_material_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    up_num int4 NOT NULL,
    up_change_num int4 NOT NULL,
    down_num int4 NOT NULL,
    down_change_num int4 NOT NULL,
    update_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 资产数量统计方案 - 商机相关表
-- 商机资产启动记录表
CREATE TABLE IF NOT EXISTS asset_business_upd (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT,
    project_id TEXT NOT NULL,
    create_time TEXT NOT NULL
);

-- 商机资产日表
CREATE TABLE IF NOT EXISTS asset_business_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    bid_num int4 NOT NULL,
    sub_bid_num int4 NOT NULL,
    before_bid_num int4 NOT NULL,
    project_approval_num int4 NOT NULL,
    project_add_num int4 NOT NULL,
    project_start_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 商机资产周表
CREATE TABLE IF NOT EXISTS asset_business_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    bid_num int4 NOT NULL,
    sub_bid_num int4 NOT NULL,
    before_bid_num int4 NOT NULL,
    project_approval_num int4 NOT NULL,
    project_add_num int4 NOT NULL,
    project_start_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 商机资产月表
CREATE TABLE IF NOT EXISTS asset_business_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    bid_num int4 NOT NULL,
    sub_bid_num int4 NOT NULL,
    before_bid_num int4 NOT NULL,
    project_approval_num int4 NOT NULL,
    project_add_num int4 NOT NULL,
    project_start_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 商机资产年表
CREATE TABLE IF NOT EXISTS asset_business_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    bid_num int4 NOT NULL,
    sub_bid_num int4 NOT NULL,
    before_bid_num int4 NOT NULL,
    project_approval_num int4 NOT NULL,
    project_add_num int4 NOT NULL,
    project_start_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产表注释
COMMENT ON TABLE asset_material_upd IS '物料资产编辑记录表';
COMMENT ON COLUMN asset_material_upd.id IS '主表id';
COMMENT ON COLUMN asset_material_upd.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_material_upd.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_material_upd.material_id IS '物料id';
COMMENT ON COLUMN asset_material_upd.create_time IS '时间';

COMMENT ON TABLE asset_material_day IS '物料资产日表';
COMMENT ON COLUMN asset_material_day.id IS '主表id';
COMMENT ON COLUMN asset_material_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_material_day.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_material_day.all_num IS '总数';
COMMENT ON COLUMN asset_material_day.up_num IS '已上架物料数（当天上架的物料，头一天没有记录则为0）';
COMMENT ON COLUMN asset_material_day.up_change_num IS '已上架物料变化数（相比头一天的上架数量变化）';
COMMENT ON COLUMN asset_material_day.down_num IS '未上架物料数（当天未上架的物料）';
COMMENT ON COLUMN asset_material_day.down_change_num IS '未上架物料变化数（相比头一天的未上架数量变化）';
COMMENT ON COLUMN asset_material_day.update_num IS '更新物料数（当天编辑的物料）';
COMMENT ON COLUMN asset_material_day.create_time IS '时间';

COMMENT ON TABLE asset_material_week IS '物料资产周表';
COMMENT ON COLUMN asset_material_week.id IS '主表id';
COMMENT ON COLUMN asset_material_week.day IS '日期（例：202501）';
COMMENT ON COLUMN asset_material_week.product_category_id IS '组织ID';
COMMENT ON COLUMN asset_material_week.all_num IS '总数';
COMMENT ON COLUMN asset_material_week.up_num IS '已上架物料数（当周上架的物料头）';
COMMENT ON COLUMN asset_material_week.up_change_num IS '已上架物料变化数（相比头一周的上架数量变化）';
COMMENT ON COLUMN asset_material_week.down_num IS '未上架物料数（当周未上架的物料）';
COMMENT ON COLUMN asset_material_week.down_change_num IS '未上架物料变化数（相比头一周的未上架数量变化）';
COMMENT ON COLUMN asset_material_week.update_num IS '更新物料数（当周编辑的物料）';
COMMENT ON COLUMN asset_material_week.create_time IS '时间';

COMMENT ON TABLE asset_material_month IS '物料资产月表';
COMMENT ON COLUMN asset_material_month.id IS '主表id';
COMMENT ON COLUMN asset_material_month.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_material_month.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_material_month.all_num IS '总数';
COMMENT ON COLUMN asset_material_month.up_num IS '已上架物料数（当月上架的物料）';
COMMENT ON COLUMN asset_material_month.up_change_num IS '已上架物料变化数（相比头一月的上架数量变化）';
COMMENT ON COLUMN asset_material_month.down_num IS '未上架物料数（当月未上架的物料）';
COMMENT ON COLUMN asset_material_month.down_change_num IS '未上架物料变化数（相比头一月的未上架数量变化）';
COMMENT ON COLUMN asset_material_month.update_num IS '更新物料数（当月编辑的物料）';
COMMENT ON COLUMN asset_material_month.create_time IS '时间';

COMMENT ON TABLE asset_material_year IS '物料资产年表';
COMMENT ON COLUMN asset_material_year.id IS '主表id';
COMMENT ON COLUMN asset_material_year.day IS '日期（例：2025）';
COMMENT ON COLUMN asset_material_year.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_material_year.all_num IS '总数';
COMMENT ON COLUMN asset_material_year.up_num IS '已上架物料数（当年上架的物料）';
COMMENT ON COLUMN asset_material_year.up_change_num IS '已上架物料变化数（相比头一年的上架数量变化）';
COMMENT ON COLUMN asset_material_year.down_num IS '未上架物料数（当年未上架的物料）';
COMMENT ON COLUMN asset_material_year.down_change_num IS '未上架物料变化数（相比头一年的未上架数量变化）';
COMMENT ON COLUMN asset_material_year.update_num IS '更新物料数（当年编辑的物料）';
COMMENT ON COLUMN asset_material_year.create_time IS '时间';

-- 商机资产表注释
COMMENT ON TABLE asset_business_upd IS '商机资产启动记录表';
COMMENT ON COLUMN asset_business_upd.id IS '主表id';
COMMENT ON COLUMN asset_business_upd.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_business_upd.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_upd.project_id IS '商机id';
COMMENT ON COLUMN asset_business_upd.create_time IS '时间';

COMMENT ON TABLE asset_business_day IS '商机资产日表';
COMMENT ON COLUMN asset_business_day.id IS '主表id';
COMMENT ON COLUMN asset_business_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_business_day.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_day.all_num IS '当前的按区域的商机总数';
COMMENT ON COLUMN asset_business_day.bid_num IS '处于投标阶段的商机总数';
COMMENT ON COLUMN asset_business_day.sub_bid_num IS '处于交标阶段的商机总数';
COMMENT ON COLUMN asset_business_day.before_bid_num IS '处于标前阶段的商机总数';
COMMENT ON COLUMN asset_business_day.project_approval_num IS '处于立项阶段的商机总数';
COMMENT ON COLUMN asset_business_day.project_add_num IS '周期内新增的商机总数';
COMMENT ON COLUMN asset_business_day.project_start_num IS '周期内新增的启动了投标的商机总数';
COMMENT ON COLUMN asset_business_day.create_time IS '时间';

COMMENT ON TABLE asset_business_week IS '商机资产周表';
COMMENT ON COLUMN asset_business_week.id IS '主表id';
COMMENT ON COLUMN asset_business_week.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_business_week.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_week.all_num IS '当前的按区域的商机总数';
COMMENT ON COLUMN asset_business_week.bid_num IS '处于投标阶段的商机总数';
COMMENT ON COLUMN asset_business_week.sub_bid_num IS '处于交标阶段的商机总数';
COMMENT ON COLUMN asset_business_week.before_bid_num IS '处于标前阶段的商机总数';
COMMENT ON COLUMN asset_business_week.project_approval_num IS '处于立项阶段的商机总数';
COMMENT ON COLUMN asset_business_week.project_add_num IS '周期内新增的商机总数';
COMMENT ON COLUMN asset_business_week.project_start_num IS '周期内新增的启动了投标的商机总数';
COMMENT ON COLUMN asset_business_week.create_time IS '时间';

COMMENT ON TABLE asset_business_month IS '商机资产月表';
COMMENT ON COLUMN asset_business_month.id IS '主表id';
COMMENT ON COLUMN asset_business_month.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_business_month.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_month.all_num IS '当前的按区域的商机总数';
COMMENT ON COLUMN asset_business_month.bid_num IS '处于投标阶段的商机总数';
COMMENT ON COLUMN asset_business_month.sub_bid_num IS '处于交标阶段的商机总数';
COMMENT ON COLUMN asset_business_month.before_bid_num IS '处于标前阶段的商机总数';
COMMENT ON COLUMN asset_business_month.project_approval_num IS '处于立项阶段的商机总数';
COMMENT ON COLUMN asset_business_month.project_add_num IS '周期内新增的商机总数';
COMMENT ON COLUMN asset_business_month.project_start_num IS '周期内新增的启动了投标的商机总数';
COMMENT ON COLUMN asset_business_month.create_time IS '时间';

COMMENT ON TABLE asset_business_year IS '商机资产年表';
COMMENT ON COLUMN asset_business_year.id IS '主表id';
COMMENT ON COLUMN asset_business_year.day IS '日期（例：2025）';
COMMENT ON COLUMN asset_business_year.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_year.all_num IS '当前的按区域的商机总数';
COMMENT ON COLUMN asset_business_year.bid_num IS '处于投标阶段的商机总数';
COMMENT ON COLUMN asset_business_year.sub_bid_num IS '处于交标阶段的商机总数';
COMMENT ON COLUMN asset_business_year.before_bid_num IS '处于标前阶段的商机总数';
COMMENT ON COLUMN asset_business_year.project_approval_num IS '处于立项阶段的商机总数';
COMMENT ON COLUMN asset_business_year.project_add_num IS '周期内新增的商机总数';
COMMENT ON COLUMN asset_business_year.project_start_num IS '周期内新增的启动了投标的商机总数';
COMMENT ON COLUMN asset_business_year.create_time IS '时间';

-- FAQ资产编辑记录表
CREATE TABLE IF NOT EXISTS asset_faq_upd (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL ,
    product_category_id TEXT NOT NULL,
    faq_id TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_upd IS 'FAQ资产编辑记录表';
COMMENT ON COLUMN asset_faq_upd.id IS '主表id';
COMMENT ON COLUMN asset_faq_upd.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_faq_upd.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_upd.faq_id IS 'faq的id';
COMMENT ON COLUMN asset_faq_upd.create_time IS '时间';

-- FAQ资产日表
CREATE TABLE IF NOT EXISTS asset_faq_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num TEXT NOT NULL,
    change_num TEXT NOT NULL,
    update_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_day IS 'FAQ资产日表';
COMMENT ON COLUMN asset_faq_day.id IS '主表id';
COMMENT ON COLUMN asset_faq_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_faq_day.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_day.all_num IS '总FAQ数（所有的FAQ）';
COMMENT ON COLUMN asset_faq_day.change_num IS 'FAQ变化数（相比头一天的数量变化）';
COMMENT ON COLUMN asset_faq_day.update_num IS '更新FAQ数（当天编辑的FAQ）';
COMMENT ON COLUMN asset_faq_day.create_time IS '时间';

-- FAQ资产周表
CREATE TABLE IF NOT EXISTS asset_faq_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num TEXT NOT NULL,
    change_num TEXT NOT NULL,
    update_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_week IS 'FAQ资产周表';
COMMENT ON COLUMN asset_faq_week.id IS '主表id';
COMMENT ON COLUMN asset_faq_week.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_faq_week.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_week.all_num IS '总FAQ数';
COMMENT ON COLUMN asset_faq_week.change_num IS 'FAQ变化数';
COMMENT ON COLUMN asset_faq_week.update_num IS '更新FAQ数';
COMMENT ON COLUMN asset_faq_week.create_time IS '时间';

-- FAQ资产月表：
CREATE TABLE IF NOT EXISTS asset_faq_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num TEXT NOT NULL,
    change_num TEXT NOT NULL,
    update_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_month IS 'FAQ资产月表';
COMMENT ON COLUMN asset_faq_month.id IS '主表id';
COMMENT ON COLUMN asset_faq_month.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_faq_month.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_month.all_num IS '总FAQ数';
COMMENT ON COLUMN asset_faq_month.change_num IS 'FAQ变化数';
COMMENT ON COLUMN asset_faq_month.update_num IS '更新FAQ数';
COMMENT ON COLUMN asset_faq_month.create_time IS '时间';

-- FAQ资产年表：
CREATE TABLE IF NOT EXISTS asset_faq_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num TEXT NOT NULL,
    change_num TEXT NOT NULL,
    update_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_year IS 'FAQ资产年表';
COMMENT ON COLUMN asset_faq_year.id IS '主表id';
COMMENT ON COLUMN asset_faq_year.day IS '日期（例：2025）';
COMMENT ON COLUMN asset_faq_year.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_year.all_num IS '总FAQ数（所有的FAQ）';
COMMENT ON COLUMN asset_faq_year.change_num IS 'FAQ变化数（相比头一天的数量变化）';
COMMENT ON COLUMN asset_faq_year.update_num IS '更新FAQ数（当天编辑的FAQ）';
COMMENT ON COLUMN asset_faq_year.create_time IS '时间';

-- 商机资产统计表添加父节点ID字段 - 2025-07-26
-- 为商机资产日表添加父节点ID字段
ALTER TABLE asset_business_day ADD COLUMN IF NOT EXISTS parent_area_id TEXT;
COMMENT ON COLUMN asset_business_day.parent_area_id IS '父级地区ID';

-- 为商机资产周表添加父节点ID字段
ALTER TABLE asset_business_week ADD COLUMN IF NOT EXISTS parent_area_id TEXT;
COMMENT ON COLUMN asset_business_week.parent_area_id IS '父级地区ID';

-- 为商机资产月表添加父节点ID字段
ALTER TABLE asset_business_month ADD COLUMN IF NOT EXISTS parent_area_id TEXT;
COMMENT ON COLUMN asset_business_month.parent_area_id IS '父级地区ID';

-- 为商机资产年表添加父节点ID字段
ALTER TABLE asset_business_year ADD COLUMN IF NOT EXISTS parent_area_id TEXT;
COMMENT ON COLUMN asset_business_year.parent_area_id IS '父级地区ID';