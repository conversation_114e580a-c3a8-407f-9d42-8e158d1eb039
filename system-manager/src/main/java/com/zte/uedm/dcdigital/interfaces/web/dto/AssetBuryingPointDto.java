package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 资产埋点DTO
 * 用于前端埋点接口的通用参数
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class AssetBuryingPointDto {

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 产品小类ID或地区ID
     */
    private String typeId;

    /**
     * 操作类型：1-物料，2-文档，3-FAQ，4-商机
     */
    private Integer operationType;
}
