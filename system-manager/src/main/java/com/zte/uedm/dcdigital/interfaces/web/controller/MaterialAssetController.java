package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.service.MaterialAssetDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssetBuryingPointDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssetJuniorExportDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssetJuniorQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssetQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.MaterialAssetJuniorVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.MaterialAssetStatVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 物料资产统计控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@Path("/uportal/asset")
@Api(value = "物料资产统计", tags = {"物料资产统计接口"})
@Controller
public class MaterialAssetController {

    @Autowired
    private MaterialAssetDomainService materialAssetDomainService;

    /**
     * 物料数量统计和变化统计
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    @POST
    @Path("/statMaterial")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "物料数量统计和变化统计",
            notes = "按时间维度统计物料总数和变化情况，支持天/周/月/年维度",
            httpMethod = "POST"
    )
    public BaseResult<MaterialAssetStatVo> statMaterial(MaterialAssetQueryDto queryDto) {
        log.info("Material asset statistics request received, queryDto: {}", queryDto);
        
        try {
            // 参数基础验证
            if (queryDto == null) {
                log.error("Query DTO is null");
                return BaseResult.failed("Query parameters cannot be null");
            }

            // 调用业务服务
            MaterialAssetStatVo result = materialAssetDomainService.getStatMaterial(queryDto);
            
            log.info("Material asset statistics completed successfully, data size: {}", 
                    result.getDataList() != null ? result.getDataList().size() : 0);
            
            return BaseResult.success(result);
            
        } catch (Exception e) {
            log.error("Error processing material asset statistics request", e);
            return BaseResult.failed("Failed to get material asset statistics: " + e.getMessage());
        }
    }

    /**
     * 物料下级统计
     * 
     * @param queryDto 查询参数
     * @return 下级统计结果
     */
    @POST
    @Path("/statMaterialJunior")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "物料下级统计",
            notes = "展示下级产品小类的详细统计数据，支持分页和变化趋势显示",
            httpMethod = "POST"
    )
    public BaseResult<MaterialAssetJuniorVo> statMaterialJunior(MaterialAssetJuniorQueryDto queryDto) {
        log.info("Material asset junior statistics request received, queryDto: {}", queryDto);
        
        try {
            // 参数基础验证
            if (queryDto == null) {
                log.error("Junior query DTO is null");
                return BaseResult.failed("Query parameters cannot be null");
            }

            // 分页参数默认值处理
            if (queryDto.getPageNum() == null || queryDto.getPageNum() < 1) {
                queryDto.setPageNum(1);
            }
            if (queryDto.getPageSize() == null || queryDto.getPageSize() < 1) {
                queryDto.setPageSize(10);
            }

            // 调用业务服务
            MaterialAssetJuniorVo result = materialAssetDomainService.getStatMaterialJunior(queryDto);
            
            log.info("Material asset junior statistics completed successfully, total: {}, page size: {}", 
                    result.getTotal(), result.getJuniorList() != null ? result.getJuniorList().size() : 0);
            
            return BaseResult.success(result);
            
        } catch (Exception e) {
            log.error("Error processing material asset junior statistics request", e);
            return BaseResult.failed("Failed to get material asset junior statistics: " + e.getMessage());
        }
    }

    /**
     * 物料下级统计导出
     *
     * @param exportDto 导出参数
     */
    @POST
    @Path("/statMaterialJuniorExport")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "物料下级统计导出",
            notes = "导出物料下级统计的全量数据到Excel文件",
            httpMethod = "POST"
    )
    public void statMaterialJuniorExport(MaterialAssetJuniorExportDto exportDto) {
        log.info("Material asset junior export request received, exportDto: {}", exportDto);

        try {
            // 参数基础验证
            if (exportDto == null) {
                log.error("Export DTO is null");
                throw new IllegalArgumentException("Export parameters cannot be null");
            }

            // 调用业务服务
            materialAssetDomainService.exportMaterialAssetJunior(exportDto);

            log.info("Material asset junior export completed successfully");

        } catch (Exception e) {
            log.error("Error processing material asset junior export request", e);
            throw new RuntimeException("Failed to export material asset junior statistics: " + e.getMessage());
        }
    }

    /**
     * 物料资产埋点接口
     *
     * @param buryingPointDto 埋点参数
     * @return 处理结果
     */
    @POST
    @Path("/buryingPoint")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "物料资产埋点接口",
            notes = "用于记录物料编辑操作，支持异步处理",
            httpMethod = "POST"
    )
    public BaseResult<String> buryingPoint(MaterialAssetBuryingPointDto buryingPointDto) {
        log.info("Material asset burying point request received, dto: {}", buryingPointDto);

        try {
            // 参数基础验证
            if (buryingPointDto == null) {
                log.error("Burying point DTO is null");
                return BaseResult.failed("Burying point parameters cannot be null");
            }

            if (buryingPointDto.getOperationType() == null || buryingPointDto.getOperationType() != 1) {
                log.error("Invalid operation type: {}", buryingPointDto.getOperationType());
                return BaseResult.failed("Operation type must be 1 for material");
            }

            // 异步处理埋点数据
            materialAssetDomainService.processMaterialEditBuryingPointAsync(buryingPointDto);

            log.info("Material asset burying point request processed successfully");
            return BaseResult.success("Burying point processed successfully");

        } catch (Exception e) {
            log.error("Error processing material asset burying point request", e);
            return BaseResult.failed("Failed to process burying point: " + e.getMessage());
        }
    }
}
