package com.zte.uedm.dcdigital.domain.aggregate.model.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商机资产启动记录表实体类
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BusinessAssetUpdEntity {

    /**
     * 主表id
     */
    private String id;

    /**
     * 日期（例：20250620）
     */
    private String day;

    /**
     * 地区ID
     */
    private String areaId;

    /**
     * 商机id
     */
    private String projectId;

    /**
     * 创建时间
     */
    private String createTime;
}
