package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.service.BusinessAssetDomainService;

import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessAssetQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessAssetStatVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 商机资产统计控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@Path("/uportal/asset")
@Api(value = "商机资产统计", tags = {"商机资产统计接口"})
@Controller
public class BusinessAssetController {

    @Autowired
    private BusinessAssetDomainService businessAssetDomainService;

    /**
     * 商机数量统计和变化统计
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    @POST
    @Path("/statBusiness")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "商机数量统计和变化统计",
            notes = "按时间维度统计商机总数和变化情况，支持天/周/月/年维度，支持下级商机变化查询",
            httpMethod = "POST"
    )
    public BaseResult<BusinessAssetStatVo> statBusiness(BusinessAssetQueryDto queryDto) {
        log.info("Business asset statistics request received, queryDto: {}", queryDto);
        
        try {

            // 调用业务服务
            BusinessAssetStatVo result = businessAssetDomainService.getStatBusiness(queryDto);
            
            log.info("Business asset statistics completed successfully, timeStatList size: {}, areaStatList size: {}",
                    result.getTimeStatList() != null ? result.getTimeStatList().size() : 0,
                    result.getAreaStatList() != null ? result.getAreaStatList().size() : 0);
            
            return BaseResult.success(result);
            
        } catch (Exception e) {
            log.error("Error processing business asset statistics request", e);
            return BaseResult.failed("Failed to get business asset statistics: " + e.getMessage());
        }
    }

    /**
     * 商机统计数据导出
     *
     */
    @POST
    @Path("/statBusinessExport")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "商机统计数据导出",
            notes = "导出商机统计数据为Excel文件",
            httpMethod = "POST"
    )
    public void statBusinessExport(BusinessAssetQueryDto queryDto) {
        log.info("Business asset statistics export request received, queryDto: {}", queryDto);

        try {
            // 参数基础验证
            if (queryDto == null) {
                log.error("Query DTO is null");
                throw new IllegalArgumentException("Query parameters cannot be null");
            }

            // 必填参数验证
            if (queryDto.getStartTime() == null || queryDto.getStartTime().trim().isEmpty()) {
                log.error("Start time is required for export");
                throw new IllegalArgumentException("Start time is required");
            }

            if (queryDto.getEndTime() == null || queryDto.getEndTime().trim().isEmpty()) {
                log.error("End time is required for export");
                throw new IllegalArgumentException("End time is required");
            }

            if (queryDto.getAreaId() == null || queryDto.getAreaId().trim().isEmpty()) {
                log.error("Area ID is required for export");
                throw new IllegalArgumentException("Area ID is required");
            }

            if (queryDto.getTimeType() == null || queryDto.getTimeType() < 1 || queryDto.getTimeType() > 4) {
                log.error("Invalid time type for export: {}", queryDto.getTimeType());
                throw new IllegalArgumentException("Time type must be 1(day), 2(week), 3(month), or 4(year)");
            }

            // 验证时间点参数
            if (queryDto.getTimePoint() == null || queryDto.getTimePoint().trim().isEmpty()) {
                log.warn("Time point is null for export, using end time as default");
                queryDto.setTimePoint(queryDto.getEndTime());
            } else {
                // 验证时间点是否在时间范围内
                if (queryDto.getTimePoint().compareTo(queryDto.getStartTime()) < 0 ||
                    queryDto.getTimePoint().compareTo(queryDto.getEndTime()) > 0) {
                    log.error("Time point {} is not within range [{}, {}] for export",
                            queryDto.getTimePoint(), queryDto.getStartTime(), queryDto.getEndTime());
                    throw new IllegalArgumentException("Time point must be within the specified time range");
                }
            }

            // 调用导出服务
            businessAssetDomainService.exportBusinessAssetStat(queryDto);

            log.info("Business asset statistics export completed successfully");

        } catch (Exception e) {
            log.error("Error processing business asset statistics export request", e);
            throw new RuntimeException("Failed to export business asset statistics: " + e.getMessage());
        }
    }
}
