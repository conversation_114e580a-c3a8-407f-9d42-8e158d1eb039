/* Started by AICoder, pid:8769eg091dq60a2147e909dee159d387b93896e6 */
package com.zte.uedm.dcdigital.domain.service.strategy.loginstat;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import com.zte.uedm.dcdigital.domain.aggregate.repository.LoginStatYearRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.UserRepository;
import com.zte.uedm.dcdigital.domain.common.constant.SystemConstants;
import com.zte.uedm.dcdigital.domain.utils.ParseUtils;
import com.zte.uedm.dcdigital.domain.utils.StatCurrentDateUtils;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.AuthDeptUserVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.LoginStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class YearLoginStatStrategy extends AbstractLoginStatStrategy implements LoginStatStrategy {

    @Autowired
    private LoginStatYearRepository yearRepository;

    @Autowired
    private UserRepository userRepository;

    @Override
    public LoginStatisticsVo handle(LoginStatQueryDto dto) {
        LoginStatisticsVo loginStatisticsVo = new LoginStatisticsVo();
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        //查询所有部门信息
        AuthDeptEntity deptEntity = new AuthDeptEntity();
        List<AuthDeptEntity> deptVoList = deptRepository.queryDeptByCondition(deptEntity);
        List<String> leafIds = getLeafDept(dto.getDeptId(), deptVoList);
        long deptAllUser = deptUserIds != null ? deptUserIds.size() : 0;
        List<UserEntity> userEntities = userRepository.selectUserByIds(deptUserIds);
        Map<String, UserEntity> userEntityMap = userEntities.stream()
                .collect(Collectors.toMap(UserEntity::getId, Function.identity()));

        LoginStatYearEntity yearEntity = new LoginStatYearEntity();
        Integer beginTime = ParseUtils.parseStringToInt(dto.getStartTime());
        Integer endTime = ParseUtils.parseStringToInt(dto.getEndTime());
        yearEntity.setBeginTime(beginTime);
        yearEntity.setEndTime(endTime);
        //获取年表记录
        List<LoginStatYearEntity> filteredLoginStatYearList = yearRepository.getLoginStatYearList(yearEntity).stream()
                .filter(entity -> StringUtils.isNotBlank(entity.getDeptId()) && leafIds.contains(entity.getDeptId()))
                .collect(Collectors.toList());
        List<String> userIds = filteredLoginStatYearList.stream().map(LoginStatYearEntity::getUserId).distinct().collect(Collectors.toList());
        setDepartmentIdPath(filteredLoginStatYearList, LoginStatYearEntity::getDeptId, LoginStatYearEntity::setDeptPathId);
        //总登录天数
        long totalLoginDays = filteredLoginStatYearList.stream()
                .map(LoginStatYearEntity::getNum)
                .filter(Objects::nonNull)
                .mapToInt(Integer::intValue)
                .sum();
        //活跃用户数
        long activeUser = userIds.size();
        loginStatisticsVo.setTotalUser(deptAllUser);
        loginStatisticsVo.setActiveUser(activeUser);
        loginStatisticsVo.setAvgLoginDays(calculateAvgLoginDays(totalLoginDays, activeUser));
        loginStatisticsVo.setActiveUserRate(calculateActiveUserRate(activeUser, deptAllUser));

        List<Integer> fullDateRange = StatCurrentDateUtils.generateYearRange(beginTime, endTime);
        Map<Integer, List<LoginStatYearEntity>> groupedByYear = filteredLoginStatYearList.stream()
                .collect(Collectors.groupingBy(LoginStatYearEntity::getDay));

        assembleSystemStatistics(loginStatisticsVo, fullDateRange, groupedByYear,deptAllUser);
        List<AuthDeptUserVo> deptTrees = getDeptTreeByDeptId(dto.getDeptId());

        if (CollectionUtils.isNotEmpty(deptTrees) && deptTrees.stream().anyMatch(deptTree -> deptTree.getDeptId().equals(dto.getDeptId()))) {
            assembleUserLogin(loginStatisticsVo, fullDateRange, groupedByYear, deptUserIds, userEntityMap);
            return loginStatisticsVo;
        }

        assembleLevelDepart(loginStatisticsVo, fullDateRange, groupedByYear,dto.getDeptId());
        return loginStatisticsVo;
    }

    @Override
    public List<LoginStatisticsVo.UserLoginDetail> getExported(LoginStatQueryDto dto, HttpServletResponse response) {
        //根据deptId获取部门叶子结点信息(只有叶子结点)
        List<AuthDeptUserVo> deptTrees = getDeptTreeByDeptId(dto.getDeptId());
        if (CollectionUtils.isNotEmpty(deptTrees) && deptTrees.stream().anyMatch(deptTree -> deptTree.getDeptId().equals(dto.getDeptId()))) {
            //确定是叶子结点
            //获取当前部门成员id
            List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
            //获取部门成员姓名信息
            List<UserEntity> userEntities = userRepository.selectUserByIds(deptUserIds);
            //转成key为userId,value为UserEntity的map集合
            Map<String, UserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(UserEntity::getId, Function.identity()));
            LoginStatYearEntity yearEntity = new LoginStatYearEntity();
            //字符串转数字
            Integer beginTime = ParseUtils.parseStringToInt(dto.getStartTime());
            Integer endTime = ParseUtils.parseStringToInt(dto.getEndTime());
            yearEntity.setBeginTime(beginTime);
            yearEntity.setEndTime(endTime);
            //获取用户登录总数据(年)
            //获取年表记录
            List<LoginStatYearEntity> filteredLoginStatYearList = yearRepository.getLoginStatYearList(yearEntity).stream()
                    .filter(entity -> !"".equals(entity.getDeptId())) // 过滤掉 "",排除部门id为""的脏数据记录
                    .filter(entity -> CollectionUtils.isEmpty(deptUserIds) || deptUserIds.contains(entity.getUserId())) // 条件过滤,筛选出用户ID在部门用户ID列表中的年统计记录
                    .collect(Collectors.toList());
            // 生成完整的时间区间列表
            List<Integer> fullDateRange = StatCurrentDateUtils.generateYearRange(beginTime, endTime);

            // 按 day 字段分组，生成 Map<day, List<LoginStatYearEntity>>
            Map<Integer, List<LoginStatYearEntity>> groupedByDay = filteredLoginStatYearList.stream()
                    .collect(Collectors.groupingBy(LoginStatYearEntity::getDay));
            LoginStatisticsVo loginStatisticsVo = new LoginStatisticsVo();
            assembleUserLogin(loginStatisticsVo, fullDateRange, groupedByDay, deptUserIds, userEntityMap);
            return loginStatisticsVo.getUserLoginDetails();
        }
        return Collections.emptyList();
    }

    private void assembleSystemStatistics(LoginStatisticsVo loginStatisticsVo, List<Integer> fullDateRange,
                                          Map<Integer, List<LoginStatYearEntity>> groupedByYear,long deptAllUserNum) {
        List<LoginStatisticsVo.TotalSystem> totalSystemList = new ArrayList<>();

        for (Integer dateInt : fullDateRange) {
            LoginStatisticsVo.TotalSystem totalSystem = new LoginStatisticsVo.TotalSystem();
            String day = ParseUtils.formatYear(dateInt);
            totalSystem.setTimePeriod(day);

            if (groupedByYear.containsKey(dateInt)) {
                List<LoginStatYearEntity> dayData = groupedByYear.getOrDefault(dateInt, new ArrayList<>());
                List<String> userIds = dayData.stream()
                        .map(LoginStatYearEntity::getUserId)
                        .distinct()
                        .collect(Collectors.toList());

                long activeUserNum = userIds.size();
                totalSystem.setActiveUser(activeUserNum);
                long totalLoginDayNum = dayData.stream()
                        .map(LoginStatYearEntity::getNum)
                        .filter(Objects::nonNull)
                        .mapToInt(Integer::intValue)
                        .sum();

                totalSystem.setAvgLoginDays(calculateAvgLoginDays(totalLoginDayNum, activeUserNum));
                totalSystem.setActiveUserRate(calculateActiveUserRate(activeUserNum, deptAllUserNum));
            } else {
                totalSystem.setActiveUser(0);
                totalSystem.setAvgLoginDays(0);
                totalSystem.setActiveUserRate(0);
            }

            totalSystemList.add(totalSystem);
        }

        loginStatisticsVo.setTotalSystem(totalSystemList);
    }

    private void assembleLevelDepart(LoginStatisticsVo loginStatisticsVo, List<Integer> fullDateRange,
                                     Map<Integer, List<LoginStatYearEntity>> groupedByYear, String deptId) {
        List<LoginStatisticsVo.LevelDepart> levelDepartList = new ArrayList<>();
        //获取直属下级部门名称
        Map<String, String> subDeptNameMap = getSubDeptMap(deptId);
        // 遍历直属下级部门
        for (Map.Entry<String, String> entry : subDeptNameMap.entrySet()) {
            //当前遍历的部门id
            String currentDeptId = entry.getKey();
            LoginStatisticsVo.LevelDepart levelDepartVo = new LoginStatisticsVo.LevelDepart();
            //部门活跃人数
            List<Integer> departActiveName = new ArrayList<>();
            //活跃用户比
            List<Integer> activeUserRate = new ArrayList<>();
            //时间
            List<String> timePeriod = new ArrayList<>();

            //当前遍历直属部门的用户总数
            int totalUserSum = getDeptUserIds(currentDeptId).size();
            // 遍历完整时间组装list数据
            for (Integer dateInt : fullDateRange) {
                //当前日期的活跃用户
                List<LoginStatYearEntity> dayData = groupedByYear.getOrDefault(dateInt, new ArrayList<>());
                //当前部门的活跃用户(根据subDeptNameMap.size()判断是直接聚合计算还是分开计算)
                long activeUserSum;
                if (subDeptNameMap.size() > 1) {
                    activeUserSum = dayData.stream()
                            .filter(Objects::nonNull)  // 过滤掉null元素
                            .filter(loginStatWeekEntity ->
                                    loginStatWeekEntity.getDeptPathId() != null &&  // 检查deptPathId不为null
                                            loginStatWeekEntity.getDeptPathId().contains(currentDeptId))
                            .map(LoginStatYearEntity::getUserId)
                            .filter(Objects::nonNull)  // 过滤掉null userId
                            .distinct()
                            .count();
                } else {
                    activeUserSum = dayData.size();
                }
                //时间周期
                timePeriod.add(ParseUtils.formatYear(dateInt));
                //活跃用户
                departActiveName.add((int) activeUserSum);
                //活跃用户比例
                activeUserRate.add((int) calculateActiveUserRate(activeUserSum, totalUserSum));
            }
            levelDepartVo.setDepartName(subDeptNameMap.get(entry.getKey()));
            levelDepartVo.setActiveUserRate(activeUserRate);
            levelDepartVo.setTimePeriod(timePeriod);
            levelDepartVo.setDepartActiveName(departActiveName);
            levelDepartList.add(levelDepartVo);
        }
        levelDepartList.sort(Comparator.comparing(LoginStatisticsVo.LevelDepart::getDepartName, Comparator.nullsLast(String::compareTo)));
        loginStatisticsVo.setLevelDepart(levelDepartList);
    }

    private void assembleUserLogin(LoginStatisticsVo loginStatisticsVo, List<Integer> fullDateRange,
                                   Map<Integer, List<LoginStatYearEntity>> groupedByYear, List<String> deptUserIds,
                                   Map<String, UserEntity> userEntityMap) {
        List<LoginStatisticsVo.UserLoginDetail> userLoginDetails = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(deptUserIds)) {
            for (String userId : deptUserIds) {
                LoginStatisticsVo.UserLoginDetail userLoginDetail = new LoginStatisticsVo.UserLoginDetail();
                List<Map<String, Long>> listMap = new ArrayList<>();
                long loginSum = SystemConstants.ZERO;
                //遍历完整时间找到用户在每个时间段内的登录次数
                for (Integer dateInt : fullDateRange) {
                    String formattedDate = ParseUtils.formatYearZn(dateInt); // 提前计算格式化日期
                    List<LoginStatYearEntity> yearEntityList = groupedByYear.getOrDefault(dateInt, Collections.emptyList());

                    boolean isUserLogin = yearEntityList.stream()
                            .anyMatch(entity -> userId.equals(entity.getUserId()));

                    long sumDay = SystemConstants.ZERO;
                    if (isUserLogin) {
                        sumDay = yearEntityList.stream()
                                .filter(entity -> userId.equals(entity.getUserId()))
                                .findFirst()
                                .map(LoginStatYearEntity::getNum)
                                .orElse(SystemConstants.ZERO);
                        loginSum++;
                    }

                    Map<String, Long> map = new HashMap<>();
                    map.put(formattedDate, sumDay);
                    listMap.add(map);
                }

                userLoginDetail.setPeriodLoginStatTimes(listMap);
                userLoginDetail.setUserName(userEntityMap.getOrDefault(userId, new UserEntity()).getName());
                userLoginDetail.setTotal(loginSum);
                userLoginDetails.add(userLoginDetail);
            }
        }

        loginStatisticsVo.setUserLoginDetails(userLoginDetails);
    }
}

/* Ended by AICoder, pid:8769eg091dq60a2147e909dee159d387b93896e6 */