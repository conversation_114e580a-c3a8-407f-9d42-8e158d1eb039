/* Started by AICoder, pid:r3fe0re66bmf5481405b0a32406b8756dc42625e */
package com.zte.uedm.dcdigital.domain.aggregate.model.entity;

import lombok.Getter;
import lombok.Setter;

/**
 * 部门-角色-资源关联实体类，用于表示部门、角色和资源之间的多对多关系。
 * <AUTHOR>
 */
@Getter
@Setter
public class AuthDepartmentRoleResourceEntity {

    /**
     * 部门-角色-资源关联ID，唯一标识一个部门、角色和资源的关联记录。
     */
    private String id;

    /**
     * 部门ID，表示该关联记录所属的部门。
     */
    private String departmentId;

    /**
     * 角色ID，表示与部门和资源关联的角色。
     */
    private String roleId;

    /**
     * 资源ID，表示与部门和角色关联的资源。
     */
    private String resourceId;

    /**
     * 创建时间，记录该关联记录的创建时间。
     */
    private String createTime;

    /**
     * 最后更新时间，记录该关联记录的最后更新时间。
     */
    private String updateTime;

    /**
     * 创建该记录的用户标识符，表示创建该关联记录的用户。
     */
    private String createBy;

    /**
     * 最后更新该记录的用户标识符，表示最后更新该关联记录的用户。
     */
    private String updateBy;
}
/* Ended by AICoder, pid:r3fe0re66bmf5481405b0a32406b8756dc42625e */