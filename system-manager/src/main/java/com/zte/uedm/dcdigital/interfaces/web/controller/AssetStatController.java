package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.service.BusinessAssetDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.AssetBuryingPointDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 资产统计通用控制器
 * 处理各种资产的埋点和通用统计功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Path("/uportal/asset")
@Api(value = "资产统计通用接口", tags = {"资产统计通用接口"})
@Controller
public class AssetStatController {

    @Autowired
    private BusinessAssetDomainService businessAssetDomainService;

    /**
     * 前端埋点接口
     * 处理各种资产类型的埋点数据
     * 
     * @param buryingPointDto 埋点数据
     * @return 处理结果
     */
    @POST
    @Path("/buryingPoints")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "前端埋点接口",
            notes = "处理各种资产类型的埋点数据，支持物料、文档、FAQ、商机等类型",
            httpMethod = "POST"
    )
    public BaseResult<String> buryingPoint(AssetBuryingPointDto buryingPointDto) {
        log.info("Asset burying point request received, buryingPointDto: {}", buryingPointDto);
        
        try {
            // 参数基础验证
            if (buryingPointDto == null) {
                log.error("Burying point DTO is null");
                return BaseResult.failed("Burying point data cannot be null");
            }

            // 必填参数验证
            if (buryingPointDto.getResourceId() == null || buryingPointDto.getResourceId().trim().isEmpty()) {
                log.error("Resource ID is required");
                return BaseResult.failed("Resource ID is required");
            }

            if (buryingPointDto.getOperationType() == null || buryingPointDto.getOperationType() < 1 || buryingPointDto.getOperationType() > 4) {
                log.error("Invalid operation type: {}", buryingPointDto.getOperationType());
                return BaseResult.failed("Operation type must be 1(material), 2(document), 3(FAQ), or 4(business)");
            }

            // 根据操作类型处理不同的埋点数据
            switch (buryingPointDto.getOperationType()) {
                case 1:
                    // 物料埋点处理
                    log.info("Processing material burying point for resourceId: {}", buryingPointDto.getResourceId());
                    // TODO: 调用物料埋点处理服务
                    break;
                case 2:
                    // 文档埋点处理
                    log.info("Processing document burying point for resourceId: {}", buryingPointDto.getResourceId());
                    // TODO: 调用文档埋点处理服务
                    break;
                case 3:
                    // FAQ埋点处理
                    log.info("Processing FAQ burying point for resourceId: {}", buryingPointDto.getResourceId());
                    // TODO: 调用FAQ埋点处理服务
                    break;
                case 4:
                    // 商机埋点处理
                    log.info("Processing business burying point for resourceId: {}", buryingPointDto.getResourceId());
                    businessAssetDomainService.processBusinessStartBuryingPointAsync(
                            buryingPointDto.getResourceId(), 
                            buryingPointDto.getTypeId()
                    );
                    break;
                default:
                    log.warn("Unsupported operation type: {}", buryingPointDto.getOperationType());
                    return BaseResult.failed("Unsupported operation type");
            }
            
            log.info("Asset burying point processed successfully");
            return BaseResult.success("Burying point data processed successfully");
            
        } catch (Exception e) {
            log.error("Error processing asset burying point request", e);
            return BaseResult.failed("Failed to process burying point data: " + e.getMessage());
        }
    }
}
