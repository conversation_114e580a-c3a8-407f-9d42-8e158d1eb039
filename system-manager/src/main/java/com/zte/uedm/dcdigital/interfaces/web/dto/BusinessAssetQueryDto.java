package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商机资产统计查询DTO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BusinessAssetQueryDto {

    /**
     * 开始时间，格式根据timeType而定：
     * - timeType=1(天): yyyyMMdd (如: 20250105)
     * - timeType=2(周): yyyyww (如: 202530)
     * - timeType=3(月): yyyyMM (如: 202505)
     * - timeType=4(年): yyyy (如: 2025)
     */
    private String startTime;

    /**
     * 结束时间，格式同startTime
     */
    private String endTime;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 时间类型：1-天，2-周，3-月，4-年
     */
    private Integer timeType;

    /**
     * 时间点：下级列表展示的具体时间点，格式根据timeType而定：
     * - timeType=1(天): yyyyMMdd (如: 20250105)
     * - timeType=2(周): yyyyww (如: 202530)
     * - timeType=3(月): yyyyMM (如: 202505)
     * - timeType=4(年): yyyy (如: 2025)
     * 该时间点必须在startTime和endTime范围内
     */
    private String timePoint;
}
