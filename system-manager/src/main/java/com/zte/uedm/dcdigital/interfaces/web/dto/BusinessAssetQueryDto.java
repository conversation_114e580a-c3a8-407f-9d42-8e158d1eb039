package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商机资产统计查询DTO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BusinessAssetQueryDto {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 时间类型：1-天，2-周，3-月，4-年
     */
    private Integer timeType;

    /**
     * 时间点：下级列表展示的具体时间点（格式：yyyyMMdd）
     * 该时间点必须在startTime和endTime范围内
     */
    private String timePoint;
}
