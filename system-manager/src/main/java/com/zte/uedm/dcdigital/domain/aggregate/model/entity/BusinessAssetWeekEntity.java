package com.zte.uedm.dcdigital.domain.aggregate.model.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商机资产周表实体类
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BusinessAssetWeekEntity {

    /**
     * 主表id
     */
    private String id;

    /**
     * 日期（例：202506）
     */
    private String day;

    /**
     * 地区ID
     */
    private String areaId;

    /**
     * 父级地区ID
     */
    private String parentAreaId;

    /**
     * 当前的按区域的商机总数
     */
    private Long allNum;

    /**
     * 处于投标阶段的商机总数
     */
    private Long bidNum;

    /**
     * 处于交标阶段的商机总数
     */
    private Long subBidNum;

    /**
     * 处于标前阶段的商机总数
     */
    private Long beforeBidNum;

    /**
     * 处于立项阶段的商机总数
     */
    private Long projectApprovalNum;

    /**
     * 周期内新增的商机总数
     */
    private Long projectAddNum;

    /**
     * 周期内新增的启动了投标的商机总数
     */
    private Long projectStartNum;

    /**
     * 创建时间
     */
    private String createTime;
}
