package com.zte.uedm.dcdigital.domain.gateway.impl;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.zte.uedm.dcdigital.common.bean.system.RoleVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.HttpServletUtil;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.config.UacProperties;
import com.zte.uedm.dcdigital.domain.aggregate.model.MenuEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import com.zte.uedm.dcdigital.domain.aggregate.repository.*;
import com.zte.uedm.dcdigital.domain.common.enums.UacCodeEnum;
import com.zte.uedm.dcdigital.domain.gateway.UacApiService;
import com.zte.uedm.dcdigital.domain.gateway.dto.UacBaseResponseDTO;
import com.zte.uedm.dcdigital.domain.gateway.dto.UacVerifyTokenRequestDTO;
import com.zte.uedm.dcdigital.domain.gateway.dto.UacVerifyTokenResponseDTO;
import com.zte.uedm.dcdigital.domain.service.LoginStatsDomainService;
import com.zte.uedm.dcdigital.domain.service.UserPermissionService;
import com.zte.uedm.dcdigital.domain.utils.AesUtils;
import com.zte.uedm.dcdigital.domain.utils.AuthUtil;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacLoginUserDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacTokenInfoDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacUserInfoDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.log.domain.bean.OperationTypeOptional;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationLogRankEnum;
import com.zte.uedm.dcdigital.log.service.CommonLogService;
import com.zte.uedm.dcdigital.sdk.system.AuthConstant;
import com.zte.uedm.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.client.RedisClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UacApiServiceImpl implements UacApiService {
    //注入restTemplate用于http远程调用
    @Autowired
    private RestTemplate restTemplate;
    //注入configProperties获取配置文件参数值
    @Autowired
    private UacProperties uacPropertiesConfig;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserPermissionService permissionService;

    @Autowired
    private UserRoleResourceRepository userRoleResourceRepository;

    @Autowired
    private MenuRepository menuRepository;

    @Autowired
    private DeptRepository deptRepository;

    @Autowired
    private DeptUserRelationRepository deptUserRelationRepository;

    @Autowired
    private AuthDepartmentRoleResourceRepository authDepartmentRoleResourceRepository;

    @Lazy
    @Autowired
    private LoginStatsDomainService loginStatsDomainService;

    @Autowired
    private CommonLogService logService;

    /* Started by AICoder, pid:z8188k0065949a1146900961a0237a4915b072de */
    @Override
    public UacTokenInfoDto getAccessTokenByCode(String code, String redirectUri, HttpServletResponse response) {
        response = HttpServletUtil.getCurrentResponse();
        if (response == null) {
            log.error("Cannot obtain HttpServletResponse");
            throw new BusinessException(StatusCode.UAC_OBTAIN_DATA_FAILED);
        }
        // 构建请求体参数
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("code", code);
        formData.add("client_secret", uacPropertiesConfig.getClientSecret());
        formData.add("redirect_uri", redirectUri);
        formData.add("client_id", uacPropertiesConfig.getClientId());
        formData.add("grant_type", "authorization_code");

        // 创建 HttpHeaders
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 创建 HttpEntity
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(formData, headers);
        // 构建 URI
        String uri = UriComponentsBuilder.fromHttpUrl(uacPropertiesConfig.getIssuer() + "/auth/external/login/oauth/accesstoken.serv")
                .build()
                .toUriString();
        // 发起请求并获取响应
        ResponseEntity<Map> tokenResponse;
        try {
            tokenResponse = restTemplate.postForEntity(uri, request, Map.class);
        } catch (RestClientException e) {
            log.error("Failed to request the UAC with URL: {}", uri, e);
            throw new BusinessException(StatusCode.UAC_OBTAIN_DATA_FAILED);
        }
        /* Started by AICoder, pid:n916dz07355df9914671095ec0094c297a34b145 */
        // 检查响应体是否为空
        if (tokenResponse.getBody() == null || tokenResponse.getBody().isEmpty()) {
            log.error("uac response body returned is empty");
            throw new BusinessException(StatusCode.UAC_OBTAIN_DATA_FAILED);
        }
        Map<String, Object> responseBody = tokenResponse.getBody();
        log.info("UAC response body get access token: {}", JSON.toJSONString(responseBody));

        // 定义需要检查的键
        String[] requiredKeys = {
                AuthConstant.UAC_ACCESS_TOKEN,
                AuthConstant.UAC_EXPIRES_IN,
                AuthConstant.UAC_TOKEN,
                AuthConstant.UAC_REFRESH_TOKEN
        };
        // 检查响应体中是否缺少任何一个必需的键
        for (String key : requiredKeys) {
            if (responseBody.get(key) == null) {
                log.error("uac parameters returned missing keys:{}", key);
                throw new BusinessException(StatusCode.UAC_OBTAIN_DATA_FAILED);
            }
        }
        /* Ended by AICoder, pid:n916dz07355df9914671095ec0094c297a34b145 */
        try {
            //开始封装uac返回的token信息
            return addCookieInfo(responseBody, response);
        } catch (Exception e) {
            log.error("return custom result exception", e);
            throw new BusinessException(StatusCode.UAC_EXCEPTION);
        }
    }

    //封装添加cookie参数的方法
    private UacTokenInfoDto addCookieInfo(Map<String, Object> responseBody, HttpServletResponse response) {
        int expiresIn = 129600;
        String accessToken = responseBody.get(AuthConstant.UAC_ACCESS_TOKEN).toString();
        String uacToken = responseBody.get(AuthConstant.UAC_TOKEN).toString();
        String refreshToken = responseBody.get(AuthConstant.UAC_REFRESH_TOKEN).toString();
        UacLoginUserDto userInfoDto = getUacUserInfo(accessToken, refreshToken, "");
        // 将用户的accessToken存入cookie，名为"X-dc-access-token"并添加到HTTP响应中
        Cookie cookieAccessToken = new Cookie(AuthConstant.COOKIE_ACCESS_TOKEN, accessToken);
        cookieAccessToken.setMaxAge(expiresIn);
        cookieAccessToken.setSecure(true);
        cookieAccessToken.setPath("/");
        response.addCookie(cookieAccessToken);

        // 将用户的X-dc-uid存入cookie，名为"X-dc-uid"并添加到HTTP响应中
        Cookie cookieAccountId = new Cookie(AuthConstant.COOKIE_UAC_ACCOUNT_ID, userInfoDto.getId());
        cookieAccountId.setMaxAge(expiresIn);
        cookieAccountId.setSecure(true);
        cookieAccountId.setPath("/");
        response.addCookie(cookieAccountId);

        // 将用户的accessToken存入cookie，名为"X-dc-token"并添加到HTTP响应中
        Cookie cookieUacToken = new Cookie(AuthConstant.COOKIE_UAC_TOKEN, uacToken);
        cookieUacToken.setMaxAge(expiresIn);
        cookieUacToken.setSecure(true);
        cookieUacToken.setPath("/");
        response.addCookie(cookieUacToken);

        // 将用户的refreshToken存入cookie，名为"refresh-token"并添加到HTTP响应中
        Cookie cookieRefreshToken = new Cookie(AuthConstant.COOKIE_UAC_REFRESH_TOKEN, refreshToken);
        cookieRefreshToken.setMaxAge(expiresIn);
        cookieRefreshToken.setSecure(true);
        cookieRefreshToken.setPath("/");
        response.addCookie(cookieRefreshToken);


        //开始封装uac返回的token信息
        UacTokenInfoDto tokenInfoDto = new UacTokenInfoDto();
        tokenInfoDto.setAccessToken(accessToken);
        tokenInfoDto.setExpiresIn(expiresIn);
        tokenInfoDto.setUacToken(uacToken);
        tokenInfoDto.setRefreshToken(refreshToken);

        return tokenInfoDto;
    }
    /* Ended by AICoder, pid:z8188k0065949a1146900961a0237a4915b072de */

    /* Started by AICoder, pid:f415c0f0b4r3168141bc099590c80a3f87d014fd */
    @Override
    public UacLoginUserDto getUacUserInfo(String accessToken, String refreshToken, String userId) {
        if (!StringUtils.isBlank(userId)) {
            UserEntity userEntity = userRepository.selectUserById(userId);
            if (userEntity != null) {
                UacLoginUserDto userDto = new UacLoginUserDto();
                BeanUtils.copyProperties(userEntity, userDto);
                logService.sendLog("login.user", OperationTypeOptional.OPERATION_TYPE_VIEW, JSON.toJSONString(userId), OperationLogRankEnum.DEFAULT.getId(), "module.system.manager");
                try {
                    refreshToken(refreshToken);
                } catch (Exception e) {
                    log.error("refresh token error: ", e);
                }
                return userDto;
            }
        }
        try {
            if (StringUtils.isBlank(refreshToken)) {
                log.error("cookie name access token is empty");
                throw new BusinessException(StatusCode.INVALID_TOKEN);
            }
            accessToken = refreshToken(refreshToken);
            // 获取基础URL，该URL指向UAC用户信息服务端点
            String baseUrl = uacPropertiesConfig.getIssuer() + "/auth/external/userinfo.serv";

            // 使用UriComponentsBuilder来构建带有查询参数的URL，将access_token作为查询参数添加
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .queryParam(AuthConstant.UAC_ACCESS_TOKEN, accessToken);

            // 构建最终的URL字符串
            String url = builder.toUriString();

            // 发送GET请求并获取响应，期望返回类型为UacLoginUserDto
            UacLoginUserDto response = restTemplate.getForObject(url, UacLoginUserDto.class);

            // 检查响应是否为空，如果为空则记录错误日志并抛出业务异常
            if (response == null) {
                log.error("received null response from uac user info service");
                throw new BusinessException(StatusCode.INVALID_TOKEN);
            }

            // 返回获取到的用户信息
            return response;
        } catch (RestClientException e) {
            // 记录错误日志并抛出业务异常，表示获取用户信息失败
            log.error("failed to obtain uac user information", e);
            throw new BusinessException(StatusCode.UAC_EXCEPTION);
        }
    }

    /* Started by AICoder, pid:ebc5a82e75kf67f140b10a3ad015ad2eb29142f9 */
    @Override
    public void logout() {
        HttpServletRequest request = HttpServletUtil.getCurrentRequest();
        HttpServletResponse response = HttpServletUtil.getCurrentResponse();

        if (request == null || response == null) {
            log.error("Cannot obtain HttpServletRequest or HttpServletResponse during logout");
            throw new BusinessException(StatusCode.UAC_EXCEPTION);
        }

        // 安全移除Cookie（确保request和response非空）
        try {
            removeCookie(request, response, AuthConstant.COOKIE_UAC_ACCOUNT_ID);
            removeCookie(request, response, AuthConstant.COOKIE_UAC_TOKEN);
            removeCookie(request, response, AuthConstant.COOKIE_ACCESS_TOKEN);
            removeCookie(request, response, AuthConstant.COOKIE_KEY_USERNAME);
        } catch (Exception e) {
            log.error("Failed to remove cookies during logout", e);
            throw new BusinessException(StatusCode.UAC_EXCEPTION);
        }
    }

    /* Ended by AICoder, pid:ebc5a82e75kf67f140b10a3ad015ad2eb29142f9 */

    /* Ended by AICoder, pid:f415c0f0b4r3168141bc099590c80a3f87d014fd */

    @Override
    public UacVerifyTokenResponseDTO verifyToken(String accountId, String uacToken, String ip, String refreshToken) {
        // 构建请求头参数
        HttpHeaders headers = buildVerifyTokenHeaders();
        // 构建请求体参数
        UacVerifyTokenRequestDTO requestDTO = new UacVerifyTokenRequestDTO();
        requestDTO.setAccountId(accountId);
        requestDTO.setToken(AesUtils.aesGcmEncrypt(uacToken, uacPropertiesConfig.getEncryptionKey()));
        requestDTO.setClientIp(ip);
        requestDTO.setSystemCode(uacPropertiesConfig.getAppId());
        requestDTO.setVerifyCode(requestDTO.generateVerifyCode());

        HttpEntity<UacVerifyTokenRequestDTO> request = new HttpEntity<>(requestDTO, headers);
        String requestUrl = uacPropertiesConfig.getIssuer() + "/external/auth/token/v2/verify.serv";
        UacBaseResponseDTO<UacVerifyTokenResponseDTO> uacBaseResponseDTO = null;

        // 请求UAC
        try {
            uacBaseResponseDTO = restTemplate.exchange(requestUrl, HttpMethod.POST, request,
                    new ParameterizedTypeReference<UacBaseResponseDTO<UacVerifyTokenResponseDTO>>() {
                    }).getBody();
        } catch (Exception e) {
            log.error("request to UAC server error", e);
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }
        // 判断响应结果
        if (uacBaseResponseDTO == null) {
            log.error("UAC response body is empty");
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }

        if (UacCodeEnum.CODE_0000.getCode().equals(uacBaseResponseDTO.getCode())) {
            //统计用户登录次数
            loginStatsDomainService.addRecordLogin(accountId);
            return uacBaseResponseDTO.getData();
        }
        log.warn("UAC verify token failed: {}", JSON.toJSONString(uacBaseResponseDTO));
        try {
            refreshToken(refreshToken);
        } catch (Exception e) {
            throw new BusinessException(StatusCode.INVALID_TOKEN);
        }
        return uacBaseResponseDTO.getData();
    }

    private String refreshToken(String refreshToken) {
        HttpServletResponse response = HttpServletUtil.getCurrentResponse();
        // 检查 response 是否为空
        if (response == null) {
            log.error("HttpServletResponse is null, cannot proceed with token refresh");
            throw new BusinessException(StatusCode.UAC_EXCEPTION);
        }
        // 构建请求体参数
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("code", "");
        formData.add("client_secret", uacPropertiesConfig.getClientSecret());
        formData.add("redirect_uri", "");
        formData.add("client_id", uacPropertiesConfig.getClientId());
        formData.add("grant_type", "refresh_token");
        formData.add("refresh_token", refreshToken);

        // 创建 HttpHeaders
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 创建 HttpEntity
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(formData, headers);
        // 构建 URI
        String uri = UriComponentsBuilder.fromHttpUrl(uacPropertiesConfig.getIssuer() + "/auth/external/login/oauth/accesstoken.serv")
                .build()
                .toUriString();
        // 发起请求并获取响应
        ResponseEntity<Map> tokenResponse;
        try {
            tokenResponse = restTemplate.postForEntity(uri, request, Map.class);
        } catch (RestClientException e) {
            log.error("Failed to request the UAC with URL: {}", uri, e);
            throw new BusinessException(StatusCode.INVALID_TOKEN);
        }
        /* Started by AICoder, pid:n916dz07355df9914671095ec0094c297a34b145 */
        // 检查响应体是否为空
        if (tokenResponse.getBody() == null || tokenResponse.getBody().isEmpty()) {
            log.error("uac response body returned is empty");
            throw new BusinessException(StatusCode.UAC_OBTAIN_DATA_FAILED);
        }
        Map<String, Object> responseBody = tokenResponse.getBody();
        log.info("UAC response body refresh token: {}", JSON.toJSONString(responseBody));

        /* Ended by AICoder, pid:n916dz07355df9914671095ec0094c297a34b145 */
        try {
            //开始封装uac返回的token信息
            addRefreshToken(responseBody, response);
            return responseBody.get(AuthConstant.UAC_ACCESS_TOKEN).toString();
        } catch (Exception e) {
            log.error("return custom result exception", e);
            throw new BusinessException(StatusCode.UAC_EXCEPTION);
        }
    }

    private void addRefreshToken(Map<String, Object> responseBody, HttpServletResponse response) {
        // 1. 参数校验
        if (responseBody == null || response == null) {
            log.error("addRefreshToken responseBody or response is null");
            throw new BusinessException(StatusCode.UAC_EXCEPTION);
        }
        int expiresIn = 129600;
        String accessToken = responseBody.get(AuthConstant.UAC_ACCESS_TOKEN).toString();
        String refreshToken = responseBody.get(AuthConstant.UAC_REFRESH_TOKEN).toString();
        // 将用户的accessToken存入cookie，名为"X-dc-access-token"并添加到HTTP响应中
        Cookie cookieAccessToken = new Cookie(AuthConstant.COOKIE_ACCESS_TOKEN, accessToken);
        cookieAccessToken.setMaxAge(expiresIn);
        cookieAccessToken.setSecure(true);
        cookieAccessToken.setPath("/");
        response.addCookie(cookieAccessToken);

        // 将用户的refreshToken存入cookie，名为"refresh-token"并添加到HTTP响应中
        Cookie cookieRefreshToken = new Cookie(AuthConstant.COOKIE_UAC_REFRESH_TOKEN, refreshToken);
        cookieRefreshToken.setMaxAge(expiresIn);
        cookieRefreshToken.setSecure(true);
        cookieRefreshToken.setPath("/");
        response.addCookie(cookieRefreshToken);

        HttpServletRequest request = HttpServletUtil.getCurrentRequest();
        if (request== null) {
            log.error("addRefreshToken request is null");
            throw new BusinessException(StatusCode.UAC_EXCEPTION);
        }
        String userId = AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID);
        String uacToken = AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN);

        // 将用户的X-dc-uid存入cookie，名为"X-dc-uid"并添加到HTTP响应中
        Cookie cookieAccountId = new Cookie(AuthConstant.COOKIE_UAC_ACCOUNT_ID, userId);
        cookieAccountId.setMaxAge(expiresIn);
        cookieAccountId.setSecure(true);
        cookieAccountId.setPath("/");
        response.addCookie(cookieAccountId);

        // 将用户的accessToken存入cookie，名为"X-dc-token"并添加到HTTP响应中
        Cookie cookieUacToken = new Cookie(AuthConstant.COOKIE_UAC_TOKEN, uacToken);
        cookieUacToken.setMaxAge(expiresIn);
        cookieUacToken.setSecure(true);
        cookieUacToken.setPath("/");
        response.addCookie(cookieUacToken);
    }

    //封装访问uac令牌校验接口所需请求头数据的方法(用于"X-Auth-Value"需要有加密的请求头)
    public HttpHeaders buildVerifyTokenHeaders() {
        // 构建请求头参数
        long timeMillis = Instant.now().toEpochMilli();
        String content = new StringBuilder()
                .append(uacPropertiesConfig.getAccessSecret())
                .append(",")
                .append(uacPropertiesConfig.getAppId())
                .append(",")
                .append(uacPropertiesConfig.getAccessKey())
                .append(",")
                .append(timeMillis)
                .toString();
        String tenantId = uacPropertiesConfig.getTenantId();
        String appId = uacPropertiesConfig.getAppId();
        //使用uac提供的加密算法进行加密
        String authValue = AesUtils.aesGcmEncrypt(content, uacPropertiesConfig.getEncryptionKey());
        String itpValue = "accessKey=" + uacPropertiesConfig.getAccessKey();
        // 创建 HttpHeaders
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Auth-Value", authValue);
        headers.set("X-Tenant-Id", tenantId);
        headers.set("X-App-Id", appId);
        headers.set("X-Itp-Value", itpValue);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        return headers;
    }
    //封装访问uac接口所需请求头数据的方法(用于"X-Auth-Value"无加密的请求头)
    public HttpHeaders uacRequestHeaderMethod(String uacToken, String accountId) {
        // 获取租户ID和应用ID
        String tenantId = uacPropertiesConfig.getTenantId();
        String appId = uacPropertiesConfig.getAppId();

        // 创建 HttpHeaders
        HttpHeaders headers = new HttpHeaders();
        // 设置必要的头部信息
        headers.set("X-Auth-Value", uacToken);
        headers.set("X-Tenant-Id", tenantId);
        headers.set("X-App-Id", appId);
        headers.set("X-Emp-No", accountId);

        // 设置内容类型和接受类型为JSON
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        // 返回构建好的HttpHeaders对象
        return headers;
    }


    @Override
    public List<UacUserInfoDto> searchUser(List<String> keys, String uacToken, String uid) {
        // 创建 HttpHeaders，设置必要的头部信息
        HttpHeaders headers = uacRequestHeaderMethod(uacToken, uid);
        // 构建请求体参数，包含租户ID、关键字和信息块
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("keyList", keys);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestMap, headers);
        String url = uacPropertiesConfig.getIssuer() + "/external/account/fuzzyquery.serv";
        UacBaseResponseDTO<List<List<UacUserInfoDto>>> uacBaseResponseDTO = null;
        try {
            uacBaseResponseDTO = restTemplate.exchange(url, HttpMethod.POST, request, new ParameterizedTypeReference<UacBaseResponseDTO<List<List<UacUserInfoDto>>>>() {
            }).getBody();
        } catch (Exception e) {
            log.error("request to UAC server error", e);
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }
        log.info("UAC response body search user: {}", JSON.toJSONString(uacBaseResponseDTO));
        if (uacBaseResponseDTO == null) {
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }
        if (!UacCodeEnum.CODE_0000.getCode().equals(uacBaseResponseDTO.getCode())) {
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }
        List<List<UacUserInfoDto>> lists = uacBaseResponseDTO.getData();
        if (CollectionUtils.isEmpty(lists)) {
            return Collections.emptyList();
        } else {
            return lists.get(0);
        }
    }

    @Override
    public UserRolePermissionMenuVo userVerify(String employeeId) {
        UserEntity entity = userRepository.selectUserById(employeeId);
        if (entity == null) {
            //抛出异常提示未授权
            throw new BusinessException(StatusCode.UN_AUTHORIZED);
        }
        UserRolePermissionMenuVo vo = new UserRolePermissionMenuVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        //权限码
        Map<String, Map<String, List<PermissionResourceVo>>> permissionVos = getUserPermissions(employeeId);
        vo.setPermissions(permissionVos);
        //角色
        List<RoleVo> roleVos = getUserRoles(employeeId);
        vo.setRoles(roleVos);
        //菜单
        List<MenuEntity> menuEntityList = menuRepository.selectUserOwnedMenuByUserId(employeeId);
        // 对菜单名称进行国际化处理
        List<MenuEntity> menuVoList = queryDepartmentMenu(employeeId, menuEntityList);
        localizeMenuNames(menuVoList);
        List<MenuListVo> menuTree = buildMenuTree(menuVoList);
        vo.setMenuListVos(menuTree);
        return vo;
    }

    @Override
    public List<UacUserResponseVo> batchSearchUser(List<String> employeeIds) {
        // 1. 创建请求头
        HttpHeaders headers = buildVerifyTokenHeaders();

        // 2. 构建请求体
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("idList", employeeIds);
        requestMap.put("idType", "T0001");

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestMap, headers);
        String url = uacPropertiesConfig.getIssuer() + "/external/account/batchquery.serv";

        // 3. 使用正确的响应类型
        ParameterizedTypeReference<UacBaseResponseDTO<List<UacUserResponseVo>>> responseType =
                new ParameterizedTypeReference<UacBaseResponseDTO<List<UacUserResponseVo>>>() {};

        UacBaseResponseDTO<List<UacUserResponseVo>> uacResponse;
        try {
            // 4. 发起请求并获取响应
            ResponseEntity<UacBaseResponseDTO<List<UacUserResponseVo>>> responseEntity =
                    restTemplate.exchange(url, HttpMethod.POST, request, responseType);

            uacResponse = responseEntity.getBody();
        } catch (Exception e) {
            log.error("request to UAC server error: {}", e.getMessage(), e);
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }

        // 5. 记录完整响应日志
        log.info("UAC response body: {}", JSON.toJSONString(uacResponse));

        // 6. 处理响应
        if (uacResponse == null) {
            log.error("UAC return null response");
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }

        if (!UacCodeEnum.CODE_0000.getCode().equals(uacResponse.getCode())) {
            log.error("UAC return error: code={}, message={}",
                    uacResponse.getCode(), uacResponse.getMessage());
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }

        // 7. 直接返回数据部分
        return uacResponse.getData();
    }

    private List<MenuEntity> queryDepartmentMenu(String userId,List<MenuEntity> menuVoList) {
        List<String> deptIdList = userRepository.queryDepartmentNode(userId);
        log.debug("user:{},deptIdList:{}",userId,deptIdList);
        if (CollectionUtils.isEmpty(deptIdList)) {
            return menuVoList;
        }
        List<MenuEntity> menuEntityList = menuRepository.queryOwnedMenuByDeptId(deptIdList);
        log.debug("user:{},menuEntityList:{}",userId,menuEntityList);
        if (CollectionUtils.isEmpty(menuEntityList)) {
            return menuVoList;
        }
        //用户可在多个部分，部门权限菜单可能有重复
        if (CollectionUtils.isNotEmpty(menuVoList)) {
            menuEntityList.addAll(menuVoList);
        }
        Map<String, MenuEntity> menuEntityMap = menuEntityList.stream().collect(Collectors.toMap(MenuEntity::getId, Function.identity(), (exist, rep) -> exist));
        List<MenuEntity> menuEntities = new ArrayList<>(menuEntityMap.values());
        //去重之后再排序
        return menuEntities.stream().sorted(Comparator.comparingInt(MenuEntity::getSort)).collect(Collectors.toList());
    }

    //用户角色列表
    List<RoleVo> getUserRoles(String employeeId) {
        List<RoleVo> roleVos = new ArrayList<>();
        List<UserRoleResourceEntity> roleResources = userRoleResourceRepository.selectByUserId(employeeId);
        Set<String> roleIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(roleResources)) {
            List<String> roleIdList = roleResources.stream().map(UserRoleResourceEntity::getRoleId).distinct().collect(Collectors.toList());
            roleIds.addAll(roleIdList);
        }
        //作为部门成员的角色
        List<String> departmentIds = userRepository.queryDepartmentNode(employeeId);
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            List<AuthDepartmentRoleResourceEntity> departmentRoleResourceEntities = authDepartmentRoleResourceRepository.queryDepartmentRoleByDeptIds(departmentIds);
            if (CollectionUtils.isNotEmpty(departmentRoleResourceEntities)) {
                Set<String> stringSet = departmentRoleResourceEntities.stream().map(AuthDepartmentRoleResourceEntity::getRoleId).collect(Collectors.toSet());
                roleIds.addAll(stringSet);
            }
        }
       if (CollectionUtils.isEmpty(roleIds)) {
           return Collections.emptyList();
       }
        Map<String, RoleEntity> roleEntityMap = roleRepository.findByRoleIds(roleIds).stream().collect(Collectors.toMap(RoleEntity::getId, Function.identity(), (exist, rep) -> exist));
        roleIds.forEach(roleId->{
            RoleVo roleVo = new RoleVo();
            RoleEntity roleEntity = roleEntityMap.get(roleId);
            if (roleEntity != null) {
                roleVo.setId(roleId);
                roleVo.setName(roleEntity.getName());
                roleVos.add(roleVo);
            }
        });
        return roleVos;
    }
    /* Started by AICoder, pid:bc93986d35o3784145ec081d902fab1ee879f4d8 */
    /**
     * 获取用户权限列表。
     *
     * @param employeeId 员工ID
     * @return 一个多层映射，外层键为资源类型（字符串形式），内层键为资源ID，值为对应的权限资源列表。
     */
    Map<String, Map<String, List<PermissionResourceVo>>> getUserPermissions(String employeeId) {
        // 获取该员工的权限资源列表
        List<PermissionResourceVo> permissionVos = permissionService.selectPermissionResourceByEmployeeId(employeeId);
        // 查询用户所属分组，根据分组查询分组对应的权限资源
        List<PermissionResourceVo> permissionResourceVos = queryByDepartment(employeeId);
        if (CollectionUtils.isEmpty(permissionResourceVos) && CollectionUtils.isEmpty(permissionVos)) {
            return Collections.emptyMap();
        }
        List<PermissionResourceVo> permissionResourceVoList = new ArrayList<>();
        permissionResourceVoList.addAll(permissionVos);
        permissionResourceVoList.addAll(permissionResourceVos);

        //根据组合键唯一去重权限码
        Set<Key> seen = new HashSet<>();
        return permissionResourceVoList.stream()
                .filter(vo -> seen.add(new Key(vo)))
                .collect(Collectors.groupingBy(
                        pr -> String.valueOf(pr.getType()), // 外层分组 key 为 type 的字符串形式 1：产品小类、2：项目、3：地区
                        Collectors.groupingBy(PermissionResourceVo::getEntityId) // 内层分组 key 为 entityId
                ));
        // 根据资源类型分组，然后再根据 resourceId 分组
        // Map<String, Map<String, List<PermissionResourceVo>>> result = uniqueList.stream()
        //    .collect(Collectors.groupingBy(
        //            pr -> String.valueOf(pr.getType()), // 外层分组 key 为 type 的字符串形式 1：产品小类、2：项目、3：地区
        //            Collectors.groupingBy(PermissionResourceVo::getEntityId) // 内层分组 key 为 entityId
        //    ));
        //    return result;
    }

    private List<PermissionResourceVo> queryByDepartment(String employeeId) {
      //TODO 这里需要查询用户作为分组成员，分组绑定的资源
        List<String> departmentIds = userRepository.queryDepartmentNode(employeeId);
        //TODO 查询用户所属分组，根据分组查询分组对应的权限资源
        List<PermissionResourceVo> permissionVos = permissionService.queryPermissionResourceByDepartment(departmentIds);
        if (CollectionUtils.isEmpty(permissionVos)) {
            return Collections.emptyList();
        }
        return permissionVos;
    }


    /* Ended by AICoder, pid:bc93986d35o3784145ec081d902fab1ee879f4d8 */

    /**
     * 对 menuVoList 中的对象的 name 属性进行国际化处理。
     *
     * @param menuVoList 菜单列表
     */
    public static void localizeMenuNames(List<MenuEntity> menuVoList) {
        if (CollectionUtils.isEmpty(menuVoList)) {
            return;
        }
        Locale currentLocale = I18nUtil.getLanguage();
        // 遍历菜单列表并对每个菜单项的名称进行国际化
        menuVoList.forEach(menu -> {
            String i18nJsonString = menu.getName();
            if (i18nJsonString != null && !i18nJsonString.isEmpty()) {
                String localizedMenuName = I18nUtil.getI18nFromString(i18nJsonString, currentLocale);
                menu.setName(localizedMenuName);
            }
        });
    }

    private static void removeCookie(HttpServletRequest request, HttpServletResponse response, String cookieName) {
        Cookie cookie = ServletUtil.getCookie(request, cookieName);
        if (cookie != null) {
            cookie.setMaxAge(0);
            cookie.setPath("/");
            response.addCookie(cookie);
        }
    }

    /**
     * 将 List<MenuVo> 转换为 List<MenuListVo> 并构建菜单树结构。
     *
     * @param menuVoList 基础对象的数据列表
     * @return 具有层级结构的菜单列表
     */
    public static List<MenuListVo> buildMenuTree(List<MenuEntity> menuVoList) {
        if (menuVoList == null || menuVoList.isEmpty()) {
            return Collections.emptyList();
        }
        // 创建一个 Map 存储所有 MenuVo 对象，以 id 作为 key
        Map<String, MenuEntity> menuMap = menuVoList.stream()
                .collect(Collectors.toMap(MenuEntity::getId, menu -> menu));

        // 创建一个 Map 存储所有 MenuListVo 对象，以 id 作为 key
        Map<String, MenuListVo> menuListMap = menuVoList.stream()
                .map(vo -> {
                    MenuListVo vo1 = new MenuListVo();
                    vo1.setId(vo.getId());
                    vo1.setName(vo.getName());
                    vo1.setMenuLevel(vo.getMenuLevel());
                    vo1.setUrl(vo.getUrl());
                    vo1.setChildrenShow(false);
                    return vo1;
                })
                .collect(Collectors.toMap(MenuListVo::getId, menu -> menu));

        // 找到所有根节点（即 parentId 为 null 或不在当前列表中的菜单）
        List<MenuListVo> rootMenus = menuVoList.stream()
                .filter(menu -> menu.getParentId() == null || !menuMap.containsKey(menu.getParentId()))
                .map(menu -> {
                    MenuListVo root = menuListMap.get(menu.getId());
                    root.setChildrenShow(true); // 设置顶级菜单的 childrenShow 为 true
                    return root;
                })
                .collect(Collectors.toList());

        // 递归地构建子菜单
        rootMenus.forEach(root -> buildChildren(root, menuVoList, menuListMap));

        return rootMenus;
    }

    /**
     * 递归地为给定的父菜单添加子菜单。
     *
     * @param parent         父菜单
     * @param menuEntityList 所有菜单项的列表
     * @param menuListMap    所有 MenuListVo 对象的映射
     */
    private static void buildChildren(MenuListVo parent, List<MenuEntity> menuEntityList, Map<String, MenuListVo> menuListMap) {
        // 查找所有直接子菜单
        List<MenuListVo> children = menuEntityList.stream()
                .filter(menu -> parent.getId().equals(menu.getParentId()))
                .map(menu -> menuListMap.get(menu.getId()))
                .collect(Collectors.toList());

        // 设置子菜单到父菜单中
        parent.setChildren(children);

        // 递归调用，为每个子菜单继续添加其子菜单
        children.forEach(child -> buildChildren(child, menuEntityList, menuListMap));
    }

    /* Started by AICoder, pid:d8534qc237fce2014513097090e53d3c4fd5955d */
    /**
     * 定义id、name、type、entityId、permissionCode属性组合唯一键
     */
    private static class Key {
        private final String id;
        private final String name;
        private final Integer type;
        private final String entityId;
        private final String permissionCode;

        public Key(PermissionResourceVo vo) {
            this.id = vo.getId();
            this.name = vo.getName();
            this.type = vo.getType();
            this.entityId = vo.getEntityId();
            this.permissionCode = vo.getPermissionCode();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Key key = (Key) o;
            return Objects.equals(id, key.id) &&
                    Objects.equals(name, key.name) &&
                    Objects.equals(type, key.type) &&
                    Objects.equals(entityId, key.entityId) &&
                    Objects.equals(permissionCode, key.permissionCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id, name, type, entityId, permissionCode);
        }
    }

    /* Ended by AICoder, pid:d8534qc237fce2014513097090e53d3c4fd5955d */
    /*
     * 获取用户头像信息
     * @param uacUserInfoList 用户信息列表
     * @param uacToken 登录用户token
     * @param accountId 登录用户工号/账号
     * */
//    private void assembleUserAvatar(String uacToken, String accountId, List<UacUserInfoDto> uacUserInfoList) {
//        // 构建请求头
//        HttpHeaders headers = uacRequestHeaderMethod(uacToken, accountId);
//
//        for (UacUserInfoDto item : uacUserInfoList) {
//            // 每次迭代创建新的body对象
//            Map<String, Object> body = new HashMap<>();
//            body.put("accountId", item.getAccountId());
//            // 创建 HttpEntity
//            HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
//            // 构建 URI
//            String uri = uacPropertiesConfig.getIssuer() + "/external/account/getavatar.serv";
//            ResponseEntity<Map> response = null;
//            try {
//                // 发起请求并获取响应
//                response = restTemplate.postForEntity(uri, request, Map.class);
//            } catch (RestClientException e) {
//                log.error("Failed to obtain profile picture for accountId: {}, cause: {}", item.getAccountId(), e.getMessage(), e);
//                setDefaultAvatar(item);
//            }
//            // 获取并检查响应体
//            Map<String, Object> responseBody = response.getBody();
//            if (responseBody == null || responseBody.isEmpty()) {
//                log.error("Response body is null or empty for accountId: {}", item.getAccountId());
//                setDefaultAvatar(item);
//                continue;
//            }
//            String code = String.valueOf(responseBody.get("code"));
//            if (UacCodeEnum.CODE_0000.getCode().equals(code)) {
//                Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
//                if (data != null && data.containsKey("smallAvatarUrl")) {
//                    // 获取到用户头像smallAvatarUrl
//                    String smallAvatarUrl = (String) data.get("smallAvatarUrl");
//                    item.setAvatar(smallAvatarUrl);
//                } else {
//                    log.error("Data avatar is null in the response for accountId: {}", item.getAccountId());
//                    setDefaultAvatar(item);
//                }
//            } else {
//                log.error("Unexpected response code: {} for accountId: {}", code, item.getAccountId());
//                setDefaultAvatar(item);
//            }
//
//        }
//    }
//
//    private void setDefaultAvatar(UacUserInfoDto item) {
//        item.setAvatar(uacPropertiesConfig.getUserAvatarUrl());
//    }

    /* Ended by AICoder, pid:7bea2h7824y18a514b620b2b7063f9522028b16d */

}
