package com.zte.uedm.dcdigital.application.scheduler;

import com.zte.uedm.dcdigital.domain.service.BusinessAssetDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 商机资产数据定时任务
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class BusinessAssetScheduler {

    @Autowired
    private BusinessAssetDomainService businessAssetDomainService;

    /**
     * 商机资产数据汇聚定时任务
     * 每天凌晨1点执行，汇聚前一天的数据到周表、月表、年表
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void aggregateBusinessAssetData() {
        log.info("Starting scheduled business asset data aggregation task");
        
        try {
            businessAssetDomainService.aggregateBusinessAssetData();
            log.info("Scheduled business asset data aggregation task completed successfully");
            
        } catch (Exception e) {
            log.error("Error in scheduled business asset data aggregation task", e);
        }
    }
}
