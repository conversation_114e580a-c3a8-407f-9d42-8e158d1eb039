package com.zte.uedm.dcdigital.domain.service.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessAssetMapper;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;

import java.lang.reflect.Method;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BusinessAssetDomainServiceImpl时间节点生成测试
 */
public class BusinessAssetTimeNodesTest {

    @InjectMocks
    private BusinessAssetDomainServiceImpl businessAssetDomainService;

    @Mock
    private BusinessAssetMapper businessAssetMapper;

    @Mock
    private ProjectService projectService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGenerateDayNodes() throws Exception {
        // 使用反射调用私有方法
        Method method = BusinessAssetDomainServiceImpl.class.getDeclaredMethod("generateDayNodes", String.class, String.class);
        method.setAccessible(true);

        // 测试正常情况
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) method.invoke(businessAssetDomainService, "20250720", "20250722");
        
        assertEquals(3, result.size());
        assertEquals("20250720", result.get(0));
        assertEquals("20250721", result.get(1));
        assertEquals("20250722", result.get(2));
    }

    @Test
    void testGenerateWeekNodes() throws Exception {
        Method method = BusinessAssetDomainServiceImpl.class.getDeclaredMethod("generateWeekNodes", String.class, String.class);
        method.setAccessible(true);

        // 测试跨周情况
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) method.invoke(businessAssetDomainService, "20250720", "20250727");
        
        assertFalse(result.isEmpty());
        // 验证格式为yyyyww
        for (String week : result) {
            assertEquals(6, week.length());
            assertTrue(week.matches("\\d{6}"));
        }
    }

    @Test
    void testGenerateMonthNodes() throws Exception {
        Method method = BusinessAssetDomainServiceImpl.class.getDeclaredMethod("generateMonthNodes", String.class, String.class);
        method.setAccessible(true);

        // 测试跨月情况
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) method.invoke(businessAssetDomainService, "20250120", "20250315");
        
        assertEquals(3, result.size());
        assertEquals("202501", result.get(0));
        assertEquals("202502", result.get(1));
        assertEquals("202503", result.get(2));
    }

    @Test
    void testGenerateYearNodes() throws Exception {
        Method method = BusinessAssetDomainServiceImpl.class.getDeclaredMethod("generateYearNodes", String.class, String.class);
        method.setAccessible(true);

        // 测试跨年情况
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) method.invoke(businessAssetDomainService, "20240601", "20260301");
        
        assertEquals(3, result.size());
        assertEquals("2024", result.get(0));
        assertEquals("2025", result.get(1));
        assertEquals("2026", result.get(2));
    }

    @Test
    void testValidateAndFormatTimeInput() throws Exception {
        Method method = BusinessAssetDomainServiceImpl.class.getDeclaredMethod("validateAndFormatTimeInput", String.class);
        method.setAccessible(true);

        // 测试正常格式
        String result = (String) method.invoke(businessAssetDomainService, "20250720");
        assertEquals("20250720", result);

        // 测试带分隔符的格式
        result = (String) method.invoke(businessAssetDomainService, "2025-07-20");
        assertEquals("20250720", result);

        // 测试异常格式
        assertThrows(Exception.class, () -> {
            method.invoke(businessAssetDomainService, "2025072");
        });

        assertThrows(Exception.class, () -> {
            method.invoke(businessAssetDomainService, "");
        });

        assertThrows(Exception.class, () -> {
            method.invoke(businessAssetDomainService, (String) null);
        });
    }

    @Test
    void testFormatTimePoint() throws Exception {
        Method method = BusinessAssetDomainServiceImpl.class.getDeclaredMethod("formatTimePoint", String.class, Integer.class);
        method.setAccessible(true);

        // 测试天格式化
        String result = (String) method.invoke(businessAssetDomainService, "20250720", 1);
        assertEquals("2025-07-20", result);

        // 测试周格式化
        result = (String) method.invoke(businessAssetDomainService, "202529", 2);
        assertEquals("2025年第29周", result);

        // 测试月格式化
        result = (String) method.invoke(businessAssetDomainService, "202507", 3);
        assertEquals("2025-07", result);

        // 测试年格式化
        result = (String) method.invoke(businessAssetDomainService, "2025", 4);
        assertEquals("2025年", result);
    }
}
