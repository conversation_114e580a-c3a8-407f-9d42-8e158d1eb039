package com.zte.uedm.dcdigital.domain.service.impl;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.process.ApprovalCommonVo;
import com.zte.uedm.dcdigital.common.bean.project.BrandGuidanceVo;
import com.zte.uedm.dcdigital.common.bean.project.ProjectProductSupportsVo;
import com.zte.uedm.dcdigital.common.bean.project.ShortBrandUpdateDto;
import com.zte.uedm.dcdigital.common.bean.system.ResourceVo;
import com.zte.uedm.dcdigital.common.bean.system.RoleVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.CodeStatusEnums;
import com.zte.uedm.dcdigital.domain.common.enums.ConfirmationModeEnums;
import com.zte.uedm.dcdigital.domain.common.enums.LectotypeTypeEnums;
import com.zte.uedm.dcdigital.domain.common.enums.MaterialStatusEnums;
import com.zte.uedm.dcdigital.domain.model.material.entity.MaterialEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.RequirementDashboardEntity;
import com.zte.uedm.dcdigital.domain.repository.MaterialRepository;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryExtraInfoRepository;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryRepository;
import com.zte.uedm.dcdigital.domain.repository.RequirementDashboardRepository;
import com.zte.uedm.dcdigital.domain.service.RequirementDashboardDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DemandMapper;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementLectotypeVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.LectotypeMaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.DashboardConditionQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.DashboardQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.RequirementDashboardAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.RequirementDashboardVo;
import com.zte.uedm.dcdigital.sdk.process.service.ProcessService;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class RequirementDashboardDomainServiceImpl implements RequirementDashboardDomainService {

    @Autowired
    private RequirementDashboardRepository dashboardRepository;

    @Autowired
    private ProjectService projectService;
    @Autowired
    private ProcessService processService;

    @Autowired
    private SystemService systemService;

    @Autowired
    private AuthService authService;
    @Autowired
    private DemandMapper demandMapper;

    @Autowired
    private ProductCategoryRepository productCategoryRepository;

    @Autowired
    private ProductCategoryExtraInfoRepository extraInfoRepository;

    @Autowired
    private MaterialRepository materialRepository;


    @Override
    public RequirementDashboardVo queryByProductAndProject(DashboardQueryDto queryDto) {
        //查询记录的看板信息
        RequirementDashboardEntity entity = dashboardRepository.queryByProductAndProject(queryDto);
        log.info("Query Kanban information based on queryDto:{} and Requirement Dashboard:{}",queryDto,entity);
        //产品小类 产品大类，产品次类的信息
        List<ProductCategoryVo> productCategoryVos = productCategoryRepository.batchQueryProductSubclasses(Collections.singletonList(queryDto.getProductCategoryId()));
        if (CollectionUtils.isEmpty(productCategoryVos)) {
            log.error("product subcategory is not exist:{}",queryDto.getProductCategoryId());
            return null;
        }
        ProductCategoryVo productCategoryVo = productCategoryVos.get(0);
        ProductCategoryEntity parentProduct = productCategoryRepository.queryById(productCategoryVo.getParentId());
        //引导记录的 短品牌及评分
        Map<String, BrandGuidanceVo> brandGuidanceVoMap = getBrandGuidanceVoMap(queryDto.getProjectId());
        BrandGuidanceVo brandGuidanceVo = brandGuidanceVoMap.get(queryDto.getProductCategoryId());
        if (brandGuidanceVo == null) {
            //引导记录无该产品小类
            log.error("Project brand guidance records without product:{}",queryDto.getProductCategoryId());
            return null;
        }
        //todo 通过产品小类及项目id查询选型单的信息(选型单有多个，取时间最早的)，

        //查询产品se，及采购商务信息
        // 系统自动获取，首先获取当前商机的产品支持SE（一个人），在未接受任务情况下产品支持SE为空，则直接从产品小类中获取产品SE信息（可能多个人）
        List<ProjectProductSupportsVo> supportsVoList = projectService.queryProjectProductSupport(queryDto.getProjectId(), Collections.singletonList(queryDto.getProductCategoryId()));
        log.debug("Search for business opportunity: {}, product subcategory: {}, products supporting SE：{}",queryDto.getProjectId(),queryDto.getProductCategoryId(),supportsVoList);
        Map<String, List<UserVo>> managerUserInfo = getManagerUserInfo(queryDto.getProductCategoryId());
        List<UserVo> productS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(supportsVoList)) {
            //已配有产品支持SE，否则返回空集合
            ProjectProductSupportsVo projectProductSupportsVo = supportsVoList.get(0);
            productS.add(projectProductSupportsVo.getSystemEngineer());
        } else {
            //尚未配置,从对应产品小类中取
            productS = managerUserInfo.getOrDefault(RoleCodeEnum.PRODUCT_SE.getCode(), new ArrayList<>());
        }
        UserVo procureBusiness = null;
        List<UserVo> procurementList = managerUserInfo.getOrDefault(RoleCodeEnum.PROCUREMENT_BUSINESS.getCode(), new ArrayList<>());
        if (CollectionUtils.isNotEmpty(procurementList)) {
            procureBusiness = procurementList.get(0);
        }
        RequirementDashboardVo requirementDashboardVo = buildRequirementDashboardVo(entity, productCategoryVo, parentProduct.getProductName(), brandGuidanceVo, productS, procureBusiness);
        //实际招标TS发起时间,实际招标TS关闭时间,实际发标时间,实际开标时间
        checkAndCompleteData(queryDto.getProjectId(),queryDto.getProductCategoryId(),requirementDashboardVo);
        return requirementDashboardVo;

    }
    private void checkAndCompleteData(String projectId, String productCategoryId,RequirementDashboardVo requirementDashboardVo){
        //通过产品小类及项目id获取最早的指定选型单记录及其对应的审批时间
        DemandManagementVo demandManagementVo = demandMapper.queryDemandManagement(projectId,productCategoryId);
        if (demandManagementVo == null) {
            //需求不存在
            log.error("Requirement does not exist");
            return;
        }
        String id = demandManagementVo.getId();
        if (StringUtils.isBlank(requirementDashboardVo.getCodeStatus())) {
            queryAndCompleteCodeStatus(requirementDashboardVo,id);
        } else {
            CodeStatusEnums byId = CodeStatusEnums.getById(requirementDashboardVo.getCodeStatus());
            String i18nFromString = I18nUtil.getI18nFromString(byId.getName());
            requirementDashboardVo.setCodeStatusName(i18nFromString);
        }
        if (needCompleteTime(requirementDashboardVo)) {
            queryAndCompleteTime(id,requirementDashboardVo);
        }
    }
    private void queryAndCompleteCodeStatus(RequirementDashboardVo requirementDashboardVo,String id){
        //查询最后一个选型单 不限类型
        DemandManagementLectotypeVo lastVo = demandMapper.queryLastByDemandId(id);
        if (lastVo == null) {
            //暂时没有选型单
            log.error("The selection form does not exist");
            return;
        }
        //查询最后选型单的最后一次物料上架审批状态
        String lectotypeId = lastVo.getLectotypeId();
        List<String> materialIds = demandMapper.getLectotypeMaterialByLtc(lectotypeId);
        if (CollectionUtils.isEmpty(materialIds)) {
            setDashboardVoPendSubmit(requirementDashboardVo);
            return;
        }
        log.info("completeOneState requirementDashboardVo:{},materialIds:{}",requirementDashboardVo,materialIds);
        completeOneState(requirementDashboardVo,materialIds);
    }

    private void completeOneState(RequirementDashboardVo requirementDashboardVo,List<String> materialIds){
        List<MaterialEntity> materialEntityList = materialRepository.queryMaterialByIds(materialIds);
        //选型单关联的物料,找最后一次物料上架审批的,并剔除撤回的
        List<MaterialEntity> approvalMaterialList = materialEntityList.stream()
                .filter(p -> StringUtils.isNotBlank(p.getApprovalId()))
                .filter(p-> !MaterialStatusEnums.DRAFT.getId().equals(p.getMaterialStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(approvalMaterialList)) {
            setDashboardVoPendSubmit(requirementDashboardVo);
            return;
        }
        //按照更新时间倒序，找最近的上架
        approvalMaterialList.stream().max(Comparator.comparing(MaterialEntity::getUpdateTime))
                .ifPresent(p -> {
                    String materialStatus = p.getMaterialStatus();
                    if (MaterialStatusEnums.AVAILABLE_APPROVAL.getId().equals(materialStatus)) {
                        requirementDashboardVo.setCodeStatus(CodeStatusEnums.AVAILABLE_APPROVAL.getId());
                        String i18nFromString = I18nUtil.getI18nFromString(CodeStatusEnums.AVAILABLE_APPROVAL.getName());
                        requirementDashboardVo.setCodeStatusName(i18nFromString);
                    } else {
                        //上架及后续物料状态视为已上架
                        requirementDashboardVo.setCodeStatus(CodeStatusEnums.AVAILABLE.getId());
                        String i18nFromString = I18nUtil.getI18nFromString(CodeStatusEnums.AVAILABLE.getName());
                        requirementDashboardVo.setCodeStatusName(i18nFromString);
                    }
                });
    }
    //设置待提交审批状态
    private void setDashboardVoPendSubmit(RequirementDashboardVo requirementDashboardVo){
        requirementDashboardVo.setCodeStatus(CodeStatusEnums.PENDING_SUBMIT.getId());
        String i18nFromString = I18nUtil.getI18nFromString(CodeStatusEnums.PENDING_SUBMIT.getName());
        requirementDashboardVo.setCodeStatusName(i18nFromString);
    }
    private void queryAndCompleteTime(String id ,RequirementDashboardVo requirementDashboardVo){
        //最早的招标类型的
        DemandManagementLectotypeVo earlyVo = demandMapper.queryEarliestByDemandId(id, LectotypeTypeEnums.ONE.type);
        if (earlyVo == null) {
            //暂时没有选型单
            log.error("The selection form does not exist");
            return;
        }
        //招标实际开标时间 取最早选型单的开标时间
        if (StringUtils.isBlank(requirementDashboardVo.getActualBidOpeningTime())) {
            requirementDashboardVo.setActualBidOpeningTime(earlyVo.getOpenBidTime());
        }
        String lectotypeId = earlyVo.getLectotypeId();
        ApprovalCommonVo approvalCommonVo = processService.getApprovalByLectotypeId(lectotypeId);
        if (approvalCommonVo == null) {
            log.error("Approval information does not exist");
            return;
        }
        if (StringUtils.isBlank(requirementDashboardVo.getTenderLaunchTime())) {
            requirementDashboardVo.setTenderLaunchTime(approvalCommonVo.getSubmitTime());
        }
        //审批完成时间取审批状态为完成的更新时间
        if (StringUtils.isBlank(requirementDashboardVo.getBiddingCloseTime()) && GlobalConstants.APPLICATION_APPROVED_STATUS.equals(approvalCommonVo.getStatus())) {
            requirementDashboardVo.setBiddingCloseTime(approvalCommonVo.getUpdateTime());
        }
    }

    private boolean needCompleteTime(RequirementDashboardVo requirementDashboardVo){
        return StringUtils.isAnyBlank(requirementDashboardVo.getTenderLaunchTime(),requirementDashboardVo.getActualBidOpeningTime(),requirementDashboardVo.getBiddingCloseTime());
    }

    private RequirementDashboardVo buildRequirementDashboardVo(RequirementDashboardEntity entity, ProductCategoryVo productCategoryVo, String parentProductName,
                                                               BrandGuidanceVo brandGuidanceVo,List<UserVo> productS,UserVo procureBusiness) {
        RequirementDashboardVo vo = new RequirementDashboardVo();
        if (entity != null) {
            BeanUtils.copyProperties(entity, vo);
            String[] resourceConfirmationMode = entity.getResourceConfirmationMode();
            if (resourceConfirmationMode != null && resourceConfirmationMode.length > 0) {
                List<String> list = Arrays.asList(resourceConfirmationMode);
                log.debug("Resource confirmation mode:{}", list);
                vo.setResourceConfirmationMode(list);
                vo.setResourceConfirmationModeList(getResourceConfirmationModeName(resourceConfirmationMode));
            }
        }
        vo.setSubcategoryId(productCategoryVo.getId());
        vo.setSubcategory(productCategoryVo.getProductName());
        //次小类及采购商务
        vo.setProcureBusiness(procureBusiness);
        vo.setMaterialType(productCategoryVo.getMaterialType());
        vo.setProductSE(productS);
        //产品大类
        vo.setProductCategory(productCategoryVo.getParentId());
        vo.setProductCategoryName(parentProductName);
        //品牌引导的信息
        vo.setShortBrand(brandGuidanceVo.getShortBrand());
        vo.setGuideBrand(brandGuidanceVo.getGuideBrand());
        vo.setSelectionScore(brandGuidanceVo.getSelectionScore());
        if (vo.getStatus() == null) {
            vo.setStatus(GlobalConstants.ZERO);
        }
        return vo;
    }

    public String[] getResourceConfirmationModeName(String[] resourceConfirmationMode) {
        return Arrays.stream(resourceConfirmationMode)
                .map(code -> {
                    ConfirmationModeEnums enumByCode = ConfirmationModeEnums.getEnumByCode(code);
                    if (enumByCode != null) {
                        return I18nUtil.getI18nFromString(enumByCode.getName());
                    }
                    return code;
                })
                .toArray(String[]::new);
    }

    @Override
    public PageVO<RequirementDashboardVo> queryByCondition(DashboardConditionQueryDto queryDto) {
        Map<String, BrandGuidanceVo> brandGuidanceVoMap = getBrandGuidanceVoMap(queryDto.getProjectId());
        if (brandGuidanceVoMap.isEmpty()) {
            log.error("Project:{} brand guidance records do not exist",queryDto.getProjectId());
            return new PageVO<>(0,Collections.emptyList());
        }
        List<String> productCategoryIdList = new ArrayList<>(brandGuidanceVoMap.keySet());
        //在状态为取消时需要保证已记录数据，
        PageVO<RequirementDashboardVo> pageVO;
        if (GlobalConstants.ONE.equals(queryDto.getStatus())) {
            //取消状态需要单独查询
            pageVO = dashboardRepository.queryByCancelCondition(queryDto,productCategoryIdList);
        } else {
            //查询默认状态
            //TODO 联合查询分页
            pageVO = dashboardRepository.queryByProjectAndCondition(queryDto,productCategoryIdList);
        }

        List<RequirementDashboardVo> list = pageVO.getList();
        if (CollectionUtils.isEmpty(list)) {
            log.error("Product subcategories for brand guide records do not exist");
            return new PageVO<>(0,Collections.emptyList());
        }
        //父级分类
        List<String> parentIdList = list.stream().map(RequirementDashboardVo::getProductCategory).distinct().collect(Collectors.toList());
        Map<String, String> parentNameMap = productCategoryRepository.queryByCategoryIds(parentIdList).stream().collect(Collectors.toMap(ProductCategoryEntity::getId, ProductCategoryEntity::getProductName));

        // 通过产品小类及项目id查询选型单的信息，
        List<String> categoryIds = list.stream().map(RequirementDashboardVo::getSubcategoryId).collect(Collectors.toList());
        //TODO 系统自动获取，首先获取当前商机的产品支持SE（一个人），在未接受任务情况下产品支持SE为空，则直接从产品小类中获取产品SE信息（可能多个人）
        //首先获取当前商机的产品支持SE
        Map<String, UserVo> projectSupportMap = batchQueryProjectSupport(queryDto.getProjectId(), categoryIds);
        //产品支持SE为空，则直接从产品小类中获取产品SE信息（可能多个人）
        Map<String, List<UserVo>> engineerUserVoMap = queryProductSoftwareEngineer(categoryIds);
        //查询采购商务信息
        Map<String, UserVo> businessUserVoMap = queryProcureBusiness(categoryIds);
        list.forEach(p->completeRequirementDashboardVo(p,parentNameMap.get(p.getProductCategory()),brandGuidanceVoMap.get(p.getSubcategoryId()),
                engineerUserVoMap.get(p.getSubcategoryId()),businessUserVoMap.get(p.getSubcategoryId()),queryDto.getStatus(),projectSupportMap.get(p.getSubcategoryId())));
        //筛选出需要查询补全数据的
        List<RequirementDashboardVo> dashboardVoList = batchQueryAndCompleteData(list, queryDto.getProjectId(), categoryIds);
        return new PageVO<>(pageVO.getTotal(),dashboardVoList);
    }
    private void completeRequirementDashboardVo(RequirementDashboardVo dashboardVo, String parentProductName, BrandGuidanceVo brandGuidanceVo,
                                                List<UserVo> productS,UserVo procureBusiness,Integer status,UserVo projectSupport) {
        //次小类及采购商务
        dashboardVo.setProcureBusiness(procureBusiness);
        if (projectSupport != null) {
            dashboardVo.setProductSE(Collections.singletonList(projectSupport));
        } else {
            dashboardVo.setProductSE(productS);
        }
        //产品大类
        dashboardVo.setProductCategoryName(parentProductName);
        //品牌引导的信息
        dashboardVo.setShortBrand(brandGuidanceVo.getShortBrand());
        dashboardVo.setGuideBrand(brandGuidanceVo.getGuideBrand());
        dashboardVo.setSelectionScore(brandGuidanceVo.getSelectionScore());
        dashboardVo.setStatus(status);
    }
    private List<RequirementDashboardVo> batchQueryAndCompleteData(List<RequirementDashboardVo> requirementDashboardVos, String projectId,List<String> categoryIds) {

        //需要补全代码状态的
        List<RequirementDashboardVo> completeStatusList = new ArrayList<>();
        //需要补全时间的
        List<RequirementDashboardVo> completeTimeList = new ArrayList<>();
        requirementDashboardVos.forEach(p ->{
            CodeStatusEnums codeStatusEnums = CodeStatusEnums.getById(p.getCodeStatus());
            if (codeStatusEnums != null) {
                String i18nFromString = I18nUtil.getI18nFromString(codeStatusEnums.getName());
                p.setCodeStatusName(i18nFromString);
            } else {
                completeStatusList.add(p);
            }
            if (needCompleteTime(p)) {
                completeTimeList.add(p);
            }
            String[] resourceConfirmationModeList = p.getResourceConfirmationModeList();
            if (resourceConfirmationModeList != null && resourceConfirmationModeList.length > 0){
                p.setResourceConfirmationMode(Arrays.asList(p.getResourceConfirmationModeList()));
                p.setResourceConfirmationModeList(getResourceConfirmationModeName(resourceConfirmationModeList));
            }
        });
        //通过产品小类及项目id获取最早的指定选型单记录及其对应的审批时间
        List<DemandManagementVo> demandManagementVos = demandMapper.queryDemandByProjectAndCategoryIds(projectId,categoryIds);
        if (CollectionUtils.isEmpty(demandManagementVos)) {
            //需求不存在
            log.info("demandManagementVos does not exist");
            return requirementDashboardVos;
        }
        List<String> demandIds = demandManagementVos.stream().map(DemandManagementVo::getId).collect(Collectors.toList());
        //产品小类对应需求单
        Map<String, DemandManagementVo> managementVoMap = demandManagementVos.stream().collect(Collectors.toMap(DemandManagementVo::getProductCategoryId, Function.identity(), (exist, replace) -> exist));
        Map<String,RequirementDashboardVo> completeStatusMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(completeStatusList)) {
            log.debug("completeStatusList:{}",completeStatusList);
            batchCompleteCodeStatus(completeStatusList,demandIds,managementVoMap);
            completeStatusMap = completeStatusList.stream().collect(Collectors.toMap(RequirementDashboardVo::getSubcategoryId, Function.identity(), (exist, replace) -> exist));
        }
        Map<String,RequirementDashboardVo> completeTimeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(completeTimeList)) {
            batchCompleteCodeTime(completeTimeList,demandIds,managementVoMap);
            completeTimeMap = completeTimeList.stream().collect(Collectors.toMap(RequirementDashboardVo::getSubcategoryId, Function.identity(), (exist, replace) -> exist));
        }
        Map<String, RequirementDashboardVo> finalCompleteStatusMap = completeStatusMap;
        log.debug("finalCompleteStatusMap:{}",finalCompleteStatusMap);
        Map<String, RequirementDashboardVo> finalCompleteTimeMap = completeTimeMap;
        log.debug("finalCompleteTimeMap:{}",finalCompleteTimeMap);
        requirementDashboardVos.forEach(p->{
            String subcategoryId = p.getSubcategoryId();
            if (finalCompleteStatusMap.containsKey(subcategoryId)) {
                RequirementDashboardVo requirementDashboardVo = finalCompleteStatusMap.get(subcategoryId);
                log.debug("requirementDashboardVo:{}",requirementDashboardVo);
                p.setCodeStatus(requirementDashboardVo.getCodeStatus());
                p.setCodeStatusName(requirementDashboardVo.getCodeStatusName());
            }
            if (finalCompleteTimeMap.containsKey(subcategoryId)) {
                RequirementDashboardVo requirementDashboardVo = finalCompleteTimeMap.get(subcategoryId);
                p.setTenderLaunchTime(requirementDashboardVo.getTenderLaunchTime());
                p.setBiddingCloseTime(requirementDashboardVo.getBiddingCloseTime());
                p.setActualBidOpeningTime(requirementDashboardVo.getActualBidOpeningTime());
            }
        });
        //已补全的数据
        return requirementDashboardVos;
    }

    private Map<String,UserVo> batchQueryProjectSupport(String projectId,List<String> categoryIds) {
        //获取商机（项目）产品支持SE
        List<ProjectProductSupportsVo> supportsVoList = projectService.queryProjectProductSupport(projectId, categoryIds);
        Map<String,UserVo> userVoMap = new HashMap<>();
        if (CollectionUtils.isEmpty(supportsVoList)) {
            return userVoMap;
        }
        log.info("The queried project product supports SE information：{}",supportsVoList);
        supportsVoList.forEach(p->{
            userVoMap.put(p.getProductCategoryId(),p.getSystemEngineer());
        });
        return userVoMap;
    }
    private void batchCompleteCodeTime(List<RequirementDashboardVo> completeTimeList, List<String> demandIds,Map<String, DemandManagementVo> managementVoMap) {
        //查询需求对应的招标选型单
        List<DemandManagementLectotypeVo> lectotypeVoList = demandMapper.queryLectotypeByDemandIdList(demandIds, LectotypeTypeEnums.ONE.type);
        if (CollectionUtils.isEmpty(lectotypeVoList)) {
            //暂时没有选型单
            log.error("The selection form does not exist");
            return;
        }

        Map<String, List<DemandManagementLectotypeVo>> lectoypeMap = lectotypeVoList.stream().collect(Collectors.groupingBy(DemandManagementLectotypeVo::getDemandId));
        //每个需求单对应的最早的选型单
        Map<String, DemandManagementLectotypeVo> earliestMap = new HashMap<>();
        //每个最早选型单的id
        List<String> lectotypeIds = new ArrayList<>();
        lectoypeMap.forEach((key, value) -> {
            // 使用流直接找到最早的记录并处理
            Optional<DemandManagementLectotypeVo> earliest = value.stream()
                    .min(Comparator.comparing(DemandManagementLectotypeVo::getCreateTime));
            // 如果存在最早的记录，则进行处理
            earliest.ifPresent(vo -> {
                earliestMap.put(key, vo);
                lectotypeIds.add(vo.getLectotypeId());
            });
        });

        List<ApprovalCommonVo> approvalByLectotypeIdList = processService.getApprovalByLectotypeIdList(lectotypeIds);
        //每个选型单对应的审批信息
        Map<String, ApprovalCommonVo> approvalCommonVoMap = approvalByLectotypeIdList.stream().collect(Collectors.toMap(ApprovalCommonVo::getBussesDataJson, Function.identity(), (exist, replace) -> exist));
        completeTimeList.forEach(dashboardVo -> {
            //对应需求
            DemandManagementVo demandManagementVo = managementVoMap.get(dashboardVo.getSubcategoryId());
            if (demandManagementVo == null) {
                return;
            }
            //需求对应选型单
            DemandManagementLectotypeVo lectotypeVo = earliestMap.get(demandManagementVo.getId());
            if (lectotypeVo == null) {
                return;
            }
            //选型单对应审批单
            ApprovalCommonVo approvalCommonVo = approvalCommonVoMap.get(lectotypeVo.getLectotypeId());
            completeTime(dashboardVo,approvalCommonVo,lectotypeVo);
        });
    }

    private void batchCompleteCodeStatus(List<RequirementDashboardVo> requirementDashboardVos, List<String> demandIds,Map<String, DemandManagementVo> managementVoMap) {
        //需要补全的
        //查询选型单
        log.debug("The code status that needs to be completed is: {}, and the requirement sheet is: {}",requirementDashboardVos,demandIds);
        List<DemandManagementLectotypeVo> lectotypeVos = demandMapper.queryLectotypeByDemandIdList(demandIds, null);
        if (CollectionUtils.isEmpty(lectotypeVos)) {
            log.error("The selection form has not been created yet");
            return;
        }
        Map<String, List<DemandManagementLectotypeVo>> listMap = lectotypeVos.stream().collect(Collectors.groupingBy(DemandManagementLectotypeVo::getDemandId));
        log.debug("Selection sheet corresponding to the demand sheet：{}",listMap);
        //每个需求对应的最晚选型单
        Map<String, DemandManagementLectotypeVo> lastMap = new HashMap<>();
        //每个需求对应的最晚选型单的id
        List<String> lectotypeIds = new ArrayList<>();
        listMap.forEach((key, value) -> {
            // 使用流直接找到最后的的记录并处理
            Optional<DemandManagementLectotypeVo> earliest = value.stream()
                    .max(Comparator.comparing(DemandManagementLectotypeVo::getCreateTime));
            // 如果存在最后的记录，则进行处理
            earliest.ifPresent(vo -> {
                lastMap.put(key, vo);
                lectotypeIds.add(vo.getLectotypeId());
            });
        });
        log.debug("The latest selection sheet corresponding to each demand order：{}",lastMap);

        List<LectotypeMaterialVo> lectotypeMaterialVos = demandMapper.queryRelationMaterialByIds(lectotypeIds);
        //每个选型单对应的物料
        Map<String, List<LectotypeMaterialVo>> stringListMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(lectotypeMaterialVos)) {
            stringListMap = lectotypeMaterialVos.stream().collect(Collectors.groupingBy(LectotypeMaterialVo::getLectotypeId));
        }
        log.debug("The materials corresponding to each selection sheet：{}",stringListMap);
        Map<String, List<LectotypeMaterialVo>> finalStringListMap = stringListMap;
        requirementDashboardVos.forEach(dashboardVo -> {
            //对应的需求单
            DemandManagementVo demandManagementVo = managementVoMap.get(dashboardVo.getSubcategoryId());
            log.debug("Requirement sheet corresponding to product subcategory:{}",demandManagementVo);
            if (demandManagementVo == null) {
                return;
            }
            //需求对应的选型单
            String id = demandManagementVo.getId();
            DemandManagementLectotypeVo lectotypeVo = lastMap.get(id);
            log.debug("Selection sheet corresponding to the requirements:{}",lectotypeVo);
            if (lectotypeVo==null) {
                return;
            }
            //选型单对应的物料
            List<LectotypeMaterialVo> materialVoList = finalStringListMap.getOrDefault(lectotypeVo.getLectotypeId(),Collections.emptyList());
            log.debug("Materials corresponding to the selection sheet:{}",materialVoList);
            if (CollectionUtils.isEmpty(materialVoList)) {
                setDashboardVoPendSubmit(dashboardVo);
                return;
            }
            List<String> stringList = materialVoList.stream().map(LectotypeMaterialVo::getMaterialId).collect(Collectors.toList());
            log.debug("completeOneState dashboardVo:{},materialIds:{}",dashboardVo,stringList);
            completeOneState(dashboardVo,stringList);
        });
    }

    private void completeTime(RequirementDashboardVo dashboardVo,ApprovalCommonVo approvalCommonVo,DemandManagementLectotypeVo lectotypeVo) {

        if (StringUtils.isBlank(dashboardVo.getActualBidOpeningTime())) {
            dashboardVo.setActualBidOpeningTime(lectotypeVo.getOpenBidTime());
        }
        if (approvalCommonVo != null) {
            if (StringUtils.isBlank(dashboardVo.getTenderLaunchTime())) {
                dashboardVo.setTenderLaunchTime(approvalCommonVo.getSubmitTime());
            }
            //审批完成时间取审批状态为完成的更新时间
            if (StringUtils.isBlank(dashboardVo.getBiddingCloseTime()) && GlobalConstants.APPLICATION_APPROVED_STATUS.equals(approvalCommonVo.getStatus())) {
                dashboardVo.setBiddingCloseTime(approvalCommonVo.getUpdateTime());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editDashboard(RequirementDashboardAddDto dashboardAddDto) {
        RequirementDashboardEntity dashboardEntity ;
        String userId = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();
        boolean isUpdate = dashboardAddDto.getId() != null;
        if (isUpdate) {
            dashboardEntity = dashboardRepository.queryBoardById(dashboardAddDto.getId());
            log.debug("Product dashboard information that needs to be modified：{}",dashboardEntity);
            if (dashboardEntity == null) {
                log.error("Requirement dashboard record does not exist, id:{}",dashboardAddDto.getId());
                throw new BusinessException(StatusCode.DATA_NOT_FOUND);
            }
        } else {
            dashboardEntity = new RequirementDashboardEntity();
            dashboardEntity.setId(UUID.randomUUID().toString());
            dashboardEntity.setCreateBy(userId);
            dashboardEntity.setCreateTime(currentTime);
        }

        dashboardEntity.setUpdateBy(userId);
        dashboardEntity.setUpdateTime(currentTime);
        dashboardEntity.setProductCategoryId(dashboardAddDto.getProductCategoryId());
        dashboardEntity.setProjectId(dashboardAddDto.getProjectId());
//        List<RoleVo> roleVos = systemService.queryRoleByUserIdAndResourceId(userId, dashboardAddDto.getProductCategoryId());
//        log.debug("The role of the current user in the current product subcategory:{}",roleVos);
//        if (CollectionUtils.isEmpty(roleVos)) {
//            log.error("The user does not have data update permission, user:{}",userId);
//            throw new BusinessException(StatusCode.UN_AUTHORIZED);
//        }
        //todo 这里前后端权限校验有问题
        //permissionCheckAndComplete(dashboardEntity,dashboardAddDto,userId,roleVos);
        //直接进行属性赋值
        RequirementDashboardEntity addDashboardEntity = convertToEntity(dashboardEntity, dashboardAddDto);
        rpcUpdateShortBrand(dashboardAddDto);
        log.info("Product dashboard information to be modified：{}",addDashboardEntity);
        dashboardRepository.updateOrInsert(isUpdate, addDashboardEntity);
    }

    /* Started by AICoder, pid:wbb30x1121ufe361448d087110c982256a18db8f */
    /**
     * 将RequirementDashboardAddDto对象转换为RequirementDashboardEntity对象。
     *
     * @param entity 要转换的实体对象
     * @param dto    要转换的DTO对象
     * @return 转换后的实体对象
     */
    private RequirementDashboardEntity convertToEntity(RequirementDashboardEntity entity, RequirementDashboardAddDto dto) {
        // 简单属性复制
        entity.setProductCategoryId(dto.getProductCategoryId());
        entity.setProjectId(dto.getProjectId());
        entity.setDemandConfirmationTime(dto.getDemandConfirmationTime());
        entity.setExpectBidOpenTime(dto.getExpectBidOpenTime());
        entity.setStatus(dto.getStatus() != null ? dto.getStatus() : 0); // 默认正常状态
        entity.setTenderLaunchTime(dto.getTenderLaunchTime());
        entity.setBiddingCloseTime(dto.getBiddingCloseTime());
        entity.setActualBidOpeningTime(dto.getActualBidOpeningTime());
        entity.setBiddingBrand(dto.getBiddingBrand());
        entity.setCodeStatus(dto.getCodeStatus());
        entity.setRemarks(StringUtils.isBlank(dto.getRemarks()) ? StringUtils.EMPTY : dto.getRemarks());

        // 处理resourceConfirmationMode, DTO中是List<String>, 实体中是String[]
        if (dto.getResourceConfirmationMode() != null && !dto.getResourceConfirmationMode().isEmpty()) {
            entity.setResourceConfirmationMode(getResourceConfirmationMode(dto.getResourceConfirmationMode()));
        }

        return entity;
    }

    /* Ended by AICoder, pid:wbb30x1121ufe361448d087110c982256a18db8f */
    private void rpcUpdateShortBrand(RequirementDashboardAddDto dashboardAddDto) {

        //TODO rpc调用更新修改后 自动生成一条引导记录
        ShortBrandUpdateDto updateDto = new ShortBrandUpdateDto();
        updateDto.setProjectId(dashboardAddDto.getProjectId());
        updateDto.setProductCategoryId(dashboardAddDto.getProductCategoryId());
        updateDto.setShortBrand(dashboardAddDto.getShortBrand());
        updateDto.setGuideBrand(dashboardAddDto.getGuideBrand());
        log.debug("Update brand guidance records:{}",updateDto);
        projectService.updateGuidedShortBrand(updateDto);
    }

    private void permissionCheckAndComplete(RequirementDashboardEntity dashboardEntity, RequirementDashboardAddDto dashboardAddDto, String userId, List<RoleVo> roleVos) {
        //用户拥有的角色集合
        Set<String> roleCodes = roleVos.stream().map(RoleVo::getCode).collect(Collectors.toSet());
        //方案经理 配置在项目上,查询用户在项目上的权限
        List<RoleVo> projectRoleList = systemService.queryRoleByUserIdAndResourceId(userId, dashboardAddDto.getProjectId());
        log.debug("The role of the current user in the current project:{}",projectRoleList);
        boolean isSchemeManager = false;
        if (CollectionUtils.isNotEmpty(projectRoleList)) {
            log.debug("User owned project role permissions:{}",projectRoleList);
            isSchemeManager = projectRoleList.stream().anyMatch(roleVo -> RoleCodeEnum.SCHEME_SE.getCode().equals(roleVo.getCode()));
        }
        // 方案经理特定字段更新
        if (isSchemeManager) {
            updateSchemeManagerFields(dashboardEntity, dashboardAddDto);
        }
        //产品S
        boolean isProductSE = roleCodes.contains(RoleCodeEnum.PRODUCT_SE.getCode());
        //产品经理
        boolean isProductManager = roleCodes.contains(RoleCodeEnum.PRODUCT_MANAGER.getCode());
        //材料助理
        boolean isMaterialAssistant = roleCodes.contains(RoleCodeEnum.MATERIAL_ASSISTANT.getCode());
        // 公共字段更新
        if (isProductSE || isSchemeManager) {
            updateCommonFields(dashboardEntity, dashboardAddDto);
        }

        // 产品经理、产品SE和材料助理共同可填写内容
        if (isProductManager || isProductSE || isMaterialAssistant) {
            updateSharedFields(dashboardEntity, dashboardAddDto);
        }

        handleCodeStatus(dashboardEntity,dashboardAddDto.getCodeStatus(),isProductSE,isMaterialAssistant);
        handleBiddingBrand(dashboardEntity,dashboardAddDto.getBiddingBrand(),isProductSE);
        //商机上配置的所有角色、商机的支持SE 及对应产品经理 可修改
        handleRemarks(dashboardEntity,dashboardAddDto.getRemarks(),isProductManager,projectRoleList);
    }

    //处理投标品牌
    private void handleBiddingBrand(RequirementDashboardEntity dashboardEntity, String biddingBrand,boolean isProductSE) {
        // 特定于产品SE的字段
        if (isProductSE) {
            dashboardEntity.setBiddingBrand(biddingBrand);
        }
    }
    //处理代码状态
    private void handleCodeStatus(RequirementDashboardEntity dashboardEntity, String codeStatus,boolean isProductSE, boolean isMaterialAssistant) {
        if (isProductSE || isMaterialAssistant) {
            dashboardEntity.setCodeStatus(codeStatus);
        }
    }
    // 处理备注信息
    private void handleRemarks(RequirementDashboardEntity entity, String remarks,boolean isProductManager,List<RoleVo> projectRoles) {
        //商机上配置的所有角色、商机的支持SE 及对应产品经理 可修改
        if (isProductManager || CollectionUtils.isNotEmpty(projectRoles)) {
            entity.setRemarks(StringUtils.isBlank(remarks) ? StringUtils.EMPTY : remarks);
        }

    }
    // 更新公共资源确认模式需求确认时间等信息
    private void updateCommonFields(RequirementDashboardEntity entity, RequirementDashboardAddDto dto) {
        entity.setDemandConfirmationTime(dto.getDemandConfirmationTime());
    }

    // 更新方案经理特有字段
    private void updateSchemeManagerFields(RequirementDashboardEntity entity, RequirementDashboardAddDto dto) {
        entity.setStatus(dto.getStatus());
        entity.setResourceConfirmationMode(getResourceConfirmationMode(dto.getResourceConfirmationMode()));
        entity.setExpectBidOpenTime(dto.getExpectBidOpenTime());
    }

    // 更新产品经理、产品SE和材料助理共同可填写的内容
    private void updateSharedFields(RequirementDashboardEntity entity, RequirementDashboardAddDto dto) {
        entity.setTenderLaunchTime(dto.getTenderLaunchTime());
        entity.setBiddingCloseTime(dto.getBiddingCloseTime());
        entity.setActualBidOpeningTime(dto.getActualBidOpeningTime());
    }

    private String[] getResourceConfirmationMode(List<String> resourceConfirmationMode) {
        if (CollectionUtils.isNotEmpty(resourceConfirmationMode)) {
            return resourceConfirmationMode.toArray(new String[0]);
        }
        return null;
    }

        //获取项目品牌引导信息
    private Map<String, BrandGuidanceVo> getBrandGuidanceVoMap(String projectId) {
        //todo 增加引导品牌信息查询
        List<BrandGuidanceVo> brandGuidanceVos = projectService.queryGuidedSubcategory(projectId);
        if (CollectionUtils.isEmpty(brandGuidanceVos)) {
            //引导记录无
            log.error("Project:{} brand guidance records do not exist",projectId);
            return new HashMap<>();
        }
        log.info("Brand guidance information:{} queried through projectId:{}",brandGuidanceVos,projectId);
        return brandGuidanceVos.stream().collect(Collectors.toMap(BrandGuidanceVo::getProductCategoryId, Function.identity(), (exist, replace) -> exist));
    }

    private Map<String,UserVo> queryProcureBusiness(List<String> productCategoryIds) {

        //查询采购商务
        List<ResourceVo> resources = systemService.getResourceByIdAndRoleCode(productCategoryIds, RoleCodeEnum.PROCUREMENT_BUSINESS.getCode());
        log.debug("systemService getResourceByIdAndRoleCode product manager resourceByIdAndRoleCode:{}",resources);
        Map<String, ResourceVo> resourceIdToVoMap = resources.stream()
                .collect(Collectors.toMap(ResourceVo::getEntityId, Function.identity(), (existing, replacement) -> existing));
        //批量查询产品小类的采购商务
        Map<String, UserVo> result = new HashMap<>();
        productCategoryIds.forEach(id -> addProcurementBusinessIfValid(id, resourceIdToVoMap, result));
        return result;
    }
    private void addProcurementBusinessIfValid(String id, Map<String, ResourceVo> resourceIdToVoMap, Map<String, UserVo> result) {
        ResourceVo resourceVo = resourceIdToVoMap.get(id);
        if (!isResourceVoValid(resourceVo)) {
            addUserVoFromResourceVo(resourceVo, result, id);
        }
    }
    private void addUserVoFromResourceVo(ResourceVo resourceVo, Map<String, UserVo> resultMap, String id) {
        Map<String, List<UserVo>> userRoleMap = resourceVo.getUserRoleMap();
        List<UserVo> managers = userRoleMap.getOrDefault(RoleCodeEnum.PROCUREMENT_BUSINESS.getCode(),Collections.emptyList());
        if (CollectionUtils.isNotEmpty(managers)) {
            resultMap.put(id, managers.get(0)); // 假设每个id只有一个manager
        }
    }

    private Map<String,List<UserVo>> queryProductSoftwareEngineer(List<String> productCategoryIds) {

        //查询产品经理
        List<ResourceVo> resources = systemService.getResourceByIdAndRoleCode(productCategoryIds, RoleCodeEnum.PRODUCT_SE.getCode());
        log.info("systemService getResourceByIdAndRoleCode product manager resourceByIdAndRoleCode:{}",resources);
        Map<String, ResourceVo> resourceIdToVoMap = resources.stream()
                .collect(Collectors.toMap(ResourceVo::getEntityId, Function.identity(), (existing, replacement) -> existing));
        //批量查询产品小类的产品SE
        Map<String, List<UserVo>> userVoMap = new HashMap<>();
        productCategoryIds.forEach(p->{
            ResourceVo managerResourceVo = resourceIdToVoMap.get(p);
            if (!isResourceVoValid(managerResourceVo)) {
                Map<String, List<UserVo>> userRoleMap = managerResourceVo.getUserRoleMap();
                List<UserVo> managers = userRoleMap.getOrDefault(RoleCodeEnum.PRODUCT_SE.getCode(),Collections.emptyList());
                userVoMap.put(p, managers);
            }
        });
       return userVoMap;
    }

    private Map<String,List<UserVo>> getManagerUserInfo(String productCategoryId) {
        Map<String,List<UserVo>> roleMap = new HashMap<>();
        ResourceVo resourceVo = systemService.getResourceById(productCategoryId);
        log.debug("systemService getResourceById resourceVo:{}",resourceVo);
        if (isResourceVoValid(resourceVo)){
            return roleMap;
        }
        return resourceVo.getUserRoleMap();
    }

    private boolean isResourceVoValid(ResourceVo resourceVo) {
        // 修正逻辑，仅当resourceVo不为空且包含有效用户角色时才返回true
        return resourceVo == null || resourceVo.getUserRoleMap() == null || resourceVo.getUserRoleMap().isEmpty();
    }
}

