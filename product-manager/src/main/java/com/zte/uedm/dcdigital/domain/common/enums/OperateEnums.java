/* Started by AICoder, pid:7904bi1b65a712f1477e0a5920488c947f413b6a */
package com.zte.uedm.dcdigital.domain.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作类型枚举，定义了系统中可能的操作类型及其多语言描述。
 * <AUTHOR>
 */
public enum OperateEnums {
    /**
     * 提交操作
     */
    SUBMIT("1", "{\"zh-CN\":\"提交\",\"en-US\":\"Submit\"}"),

    /**
     * 暂存操作
     */
    TEMPORARILY_STORE("2", "{\"zh-CN\":\"暂存\",\"en-US\":\"Temporarily store\"}"),

    /**
     * 取消变更操作
     */
    CANCEL_CHANGE("3", "{\"zh-CN\":\"取消变更\",\"en-US\":\"Cancel Change\"}"),

    /**
     * 物料上架操作
     */
    LISTING("4", "{\"zh-CN\":\"上架\",\"en-US\":\"Listing\"}"),

    /**
     * 物料下架操作
     */
    DE_LIST("5", "{\"zh-CN\":\"下架\",\"en-US\":\"De-list\"}"),


    INVALID("6","{\"zh-CN\":\"无效\",\"en-US\":\"invalid\"}");

    private String id;
    private String name;

    // 使用静态映射来快速查找枚举值
    private static final Map<String, OperateEnums> OPERATE_ENUMS_MAP = new HashMap<>();

    static {
        for (OperateEnums operateEnum : values()) {
            OPERATE_ENUMS_MAP.put(operateEnum.id, operateEnum);
        }
    }

    /**
     * 获取枚举的ID。
     *
     * @return 枚举ID
     */
    public String getId() {
        return this.id;
    }

    /**
     * 获取枚举的名称（多语言格式）。
     *
     * @return 枚举名称
     */
    public String getName() {
        return this.name;
    }

    // 私有构造函数，防止外部实例化
    OperateEnums(String id, String name) {
        this.id = id;
        this.name = name;
    }

    /**
     * 根据ID获取对应的枚举值。
     *
     * @param id 枚举ID
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static OperateEnums getById(String id) {
        return OPERATE_ENUMS_MAP.getOrDefault(id,INVALID);
    }

    /**
     * 检查给定的ID是否在枚举范围内。
     *
     * @param id 枚举ID
     * @return true表示在范围内，false表示不在范围内
     */
    public static boolean isInRange(String id) {
        return OPERATE_ENUMS_MAP.containsKey(id);
    }
}
/* Ended by AICoder, pid:7904bi1b65a712f1477e0a5920488c947f413b6a */