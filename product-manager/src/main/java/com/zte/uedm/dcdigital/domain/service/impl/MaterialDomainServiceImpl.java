/* Started by AICoder, pid:u6595k8c15312b1149000b3f5064134bc5f44d89 */
package com.zte.uedm.dcdigital.domain.service.impl;
/* Started by AICoder, pid:qa9b3n7157ca73314e9d0acca2c90a2a44939c35 */
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.JsonUtils;
import com.zte.uedm.component.kafka.producer.service.KafkaSenderService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo;
import com.zte.uedm.dcdigital.common.bean.dto.ApprovalInformationDto;
import com.zte.uedm.dcdigital.common.bean.dto.MaterialDto;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.enums.system.PermissionEnum;
import com.zte.uedm.dcdigital.common.bean.process.ApprovalCommonVo;
import com.zte.uedm.dcdigital.common.bean.process.MaterialToApprovalDto;
import com.zte.uedm.dcdigital.common.bean.process.NewApprovalDto;
import com.zte.uedm.dcdigital.common.bean.product.MaterialWithExtendInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.OthInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.PdmInfoVo;
import com.zte.uedm.dcdigital.common.bean.project.ProjectDetailInfoVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.enums.ApprovalTypeEnums;
import com.zte.uedm.dcdigital.common.enums.DocumentRelateResourceTypeEnum;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;

import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.*;
import com.zte.uedm.dcdigital.domain.common.statuscode.PdmStatusCode;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductCategoryStatusCode;

import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.utils.ExcelImportBuilder;
import com.zte.uedm.dcdigital.domain.common.utils.JtExcelFieldIn;
import com.zte.uedm.dcdigital.domain.common.utils.SalesCodeUtils;
import com.zte.uedm.dcdigital.domain.gateway.PdmApiService;
import com.zte.uedm.dcdigital.domain.model.material.entity.*;
import com.zte.uedm.dcdigital.domain.model.material.event.MaterialReadListener;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.domain.repository.*;
import com.zte.uedm.dcdigital.domain.model.material.vobj.TemplateObj;
import com.zte.uedm.dcdigital.domain.service.MaterialDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.MaterialConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DemandMapper;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVersionVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.TemplateImportVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.*;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.RequirementDashboardAddDto;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.process.rpc.DemandProcessRpc;
import com.zte.uedm.dcdigital.sdk.process.service.ProcessService;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import com.zte.uedm.function.security.api.CryptoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


/**
 * 物料领域服务实现类。
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MaterialDomainServiceImpl implements MaterialDomainService {

    /**
     * 物料仓库，用于与数据库交互。
     */
    @Autowired
    private MaterialRepository materialRepository;

    @Autowired
    private MaterialHistoryRepository materialHistoryRepository;

    @Autowired
    private MaterialTemporaryRepository materialTemporaryRepository;

    @Autowired
    private PdmInfoRepository pdmInfoRepository;

    @Autowired
    private OthInfoRepository othSalesCodeRepository;

    @Autowired
    private AuthService authService;

    @Autowired
    private SystemService systemService;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private ProductGroupRepository productGroupRepository;

    @Autowired
    private ProductCategoryRepository productCategoryRepository;

    @Autowired
    private ProcessService processService;

    @Autowired
    private PdmApiService pdmApiService;

    @Autowired
    private PermissionUtil permissionUtil;

    @Autowired
    private DemandProcessRpc demandProcessRpc;

    @Autowired
    private DemandMapper demandMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProcurementPriceHistoryRepository priceHistoryRepository;

    @Autowired
    private KafkaSenderService kafkaSenderService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawByApprovalId(String approvalId) {
        //审批撤回，当前临时表数据
        List<MaterialTemporaryEntity> tempEntities = materialTemporaryRepository.queryMaterialTemporaryByApprovalId(approvalId);
        List<MaterialEntity> currentList;
        if (CollectionUtils.isEmpty(tempEntities)) {
            //临时表没有数据的，是草稿物料在进行上架
            currentList = materialRepository.queryByApprovalId(approvalId);
            if (CollectionUtils.isEmpty(currentList)) {
                log.error("The material associated with the approvalId:{} does not exist. Please confirm if the approvalId is correct",approvalId);
                throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_EXIST);
            }
        } else {
            //当前主表数据
            List<String> materialIds = tempEntities.stream().map(MaterialTemporaryEntity::getMaterialId).collect(Collectors.toList());
            currentList = materialRepository.queryMaterialByIds(materialIds);
        }
        //需要更新主表物料的状态
        List<MaterialEntity> modifiedList = new ArrayList<>();
        //草稿撤回 需要删除临时表的物料
        List<String> deleteList = new ArrayList<>();
        //如多状态回退，分开处理
        Map<String, List<MaterialEntity>> groupedMaterials = currentList.stream().collect(Collectors.groupingBy(MaterialEntity::getMaterialStatus));
        groupedMaterials.forEach((status, entities) -> batchRollbackStatus(status, entities, modifiedList, deleteList));
        //回退主表状态
        materialRepository.batchUpdateMaterials(modifiedList);
        //删除临时表信息
        materialTemporaryRepository.deleteByMaterialIds(deleteList);

    }

    public void batchRollbackStatus(String materialStatus,List<MaterialEntity> materialEntities,List<MaterialEntity> modifiedList,List<String> deleteList) {
        //只有三种状态可以撤回 上架审批中，下架审批中，变更审批中
        if (MaterialStatusEnums.AVAILABLE_APPROVAL.getId().equals(materialStatus)) {
            //上架审批中 只有草稿及已下架可进行上架 撤回就是草稿 或是已下架，已下架有版本信息
            for (MaterialEntity materialEntity : materialEntities) {
                //版本不为空且不为0
                String version = materialEntity.getVersion();
                if (StringUtils.isNotBlank(version) && !version.equals(GlobalConstants.DRAFT_VERSION)) {
                    //已下架
                    materialEntity.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE.getId());
                } else {
                    //草稿
                    materialEntity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());
                }
            }
            List<String> stringList = materialEntities.stream().map(MaterialEntity::getId).collect(Collectors.toList());
            deleteList.addAll(stringList);
        }
        if (MaterialStatusEnums.CHANGE_APPROVAL.getId().equals(materialStatus)) {
            //变更审批中 撤回就是 变更中
            materialEntities.forEach(p->p.setMaterialStatus(MaterialStatusEnums.LISTING.getId()));
        }
        if (MaterialStatusEnums.UNAVAILABLE_APPROVAL.getId().equals(materialStatus)) {
            //下架审批中 撤回就是已上架
            materialEntities.forEach(p->p.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId()));
            List<String> stringList = materialEntities.stream().map(MaterialEntity::getId).collect(Collectors.toList());
            deleteList.addAll(stringList);
        }
        modifiedList.addAll(materialEntities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByApprovalInformation(ApprovalInformationDto informationDto) {
        //审批通过处理
        String approvalId = informationDto.getApprovalId();
        log.info("Start processing the approvalId:{} approved materials.",informationDto);
        //当前临时表数据
        List<MaterialTemporaryEntity> tempEntities = materialTemporaryRepository.queryMaterialTemporaryByApprovalId(approvalId);
        log.debug("Temporary data MaterialTemporaryEntity:{}",tempEntities);
        //当前主表数据
        List<MaterialEntity> currentList;
        boolean draftFlag = false;
        if (CollectionUtils.isEmpty(tempEntities)) {
            //临时表没有数据的，是草稿物料在进行上架
            currentList = materialRepository.queryByApprovalId(approvalId);
            if (CollectionUtils.isEmpty(currentList)) {
                log.error("The material associated with the approvalId:{} does not exist. Please confirm if the approvalId is correct",approvalId);
                throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_EXIST);
            }
            draftFlag = true;
        } else {
            //当前主表数据
            List<String> materialIds = tempEntities.stream().map(MaterialTemporaryEntity::getMaterialId).collect(Collectors.toList());
            currentList = materialRepository.queryMaterialByIds(materialIds);
        }

        //审批状态2-审批结束
        List<MaterialDto> material = informationDto.getMaterial();
        //审批挟带的物料信息
        Map<String, MaterialDto> materialMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(material)) {
            log.info("material information in the approval process:{}",material);
            materialMap = material.stream().collect(Collectors.toMap(MaterialDto::getId, p -> p));
        }
        //主表需要更新的
        List<MaterialEntity> mainMaterialList = new ArrayList<>();
        //历史表需要新增的
        List<MaterialHistoryEntity> historyMaterialList = new ArrayList<>();

        handleApprovedMaterialInformation(materialMap,draftFlag, currentList,
                tempEntities,informationDto,mainMaterialList,historyMaterialList);

        if (MaterialStatusEnums.WAIT_SUPPLEMENTARY_COST.getId().equals(informationDto.getMaterialStatus())) {
            //表示OTH销售代码上架 产品经理审批通过，状态为已上架（待补充成本）
            mainMaterialList.forEach(p->p.setMaterialStatus(MaterialStatusEnums.WAIT_SUPPLEMENTARY_COST.getId()));
        }
        log.info("Updated material data mainMaterialList:{}",mainMaterialList);
        materialRepository.batchUpdateMaterials(mainMaterialList);
        log.info("Updated material data historyMaterialList:{}",historyMaterialList);
        materialHistoryRepository.batchAddHistoryMaterial(historyMaterialList);
        //审批通过 临时表需要删除
        if (CollectionUtils.isNotEmpty(tempEntities)) {
            List<String> tempIds = tempEntities.stream().map(MaterialTemporaryEntity::getId).collect(Collectors.toList());
            materialTemporaryRepository.deleteByIds(tempIds);
            //临时表仅保留待关联的文档id，审批通过后关联
            //TODO 待后续提供批量操作接口替换
            tempEntities.forEach(p->{
                //清除原有关联关系
                documentService.deleteByResourceId(p.getMaterialId());
                //临时表中文档id为空，则不需要建立文档关联关系
                if (p.getDocumentIds() != null && p.getDocumentIds().length >GlobalConstants.ZERO) {
                    documentService.relateDocuments(p.getMaterialId(), Arrays.asList(p.getDocumentIds()), DocumentRelateResourceTypeEnum.MATERIAL.getCode());
                }
            });
        }
        if(informationDto.getApprovalType().equals(ApprovalTypeEnums.AVAILABLE_APPROVAL.getId())) {
            othCodeUpd(material);
        }
    }

    /* Started by AICoder, pid:65e0a9799fa6c6214571083c50286a578d5097bd */
    /**
     * 根据物料ID查询临时表物料对应的PDM ID以及正式表对应的PDM ID，然后比较PDM ID是否一致。
     * 对于PDM ID不一致的价格历史记录成本变更原因。
     *
     * @param currentList 主表数据
     * @param mainUpdateMaterialList 临时表数据
     * @param priceHistoryEntityList 价格历史实体列表
     */
    private void synchronizePdmCostByDifferences(List<MaterialEntity> currentList, List<MaterialEntity> mainUpdateMaterialList, List<ProcurementPriceHistoryEntity> priceHistoryEntityList) {
        // 比较临时表与正式表物料的PDM ID，筛选出PDM ID有变化的物料

        // 将临时表转换为 Map<materialId, MaterialEntity>
        Map<String, MaterialEntity> tempMap = mainUpdateMaterialList.stream()
                .collect(Collectors.toMap(MaterialEntity::getId, Function.identity()));

        List<String> tempMaterialIds = mainUpdateMaterialList.stream().map(MaterialEntity::getId).collect(Collectors.toList());
        //查询正式表中的数据
        List<MaterialEntity> mainList = materialRepository.queryMaterialByIds(tempMaterialIds);
        Map<String, MaterialEntity> mainMap = mainList.stream()
                .collect(Collectors.toMap(MaterialEntity::getId, Function.identity()));



        //收集临时表物料pdmId
        List<String> tempPdmIds = mainUpdateMaterialList.stream()
                .map(MaterialEntity::getPdmInfoId)
                .filter(id -> id != null && !id.isEmpty()) // 过滤掉 null 和 ""
                .collect(Collectors.toList());


        //收集pdmId为key,销售代码为value的map集合
        Map<String, String> pdmSalesCodeMap = pdmInfoRepository.selectPdmInfoByIds(tempPdmIds)
                .stream()
                .collect(Collectors.toMap(
                        PdmInfoEntity::getId,      // 提取 id 作为键
                        PdmInfoEntity::getSalesCode // 提取 salesCode 作为值
                ));

        //根据pdmId查询销售代码
        List<String> tmpSalesCodeList = pdmInfoRepository.selectPdmInfoByIds(tempPdmIds).stream().map(PdmInfoEntity::getSalesCode).collect(Collectors.toList());
        Map<String, String> tempMaterialCostInfo = getMaterialCostInfo(tmpSalesCodeList);
        //比较价格是否有差异，有差异就记录成本历史
        mainUpdateMaterialList.forEach(p -> {
            if (StringUtils.isNotBlank(p.getPdmInfoId())) {
            //pdm成本
            String pdmCost=tempMaterialCostInfo.getOrDefault(pdmSalesCodeMap.get(p.getPdmInfoId()), "");
            //原物料成本
            String oldPdmCost=mainMap.get(p.getId()).getCost();
            if (!pdmCost.equals(oldPdmCost)) {
                p.setCost(pdmCost);
            }
            }
        });
        //同步成本历史表
        //根据变更的物料id集合构造成本历史数据
        priceHistoryEntityList.addAll(mainUpdateMaterialList.stream().map(p -> buildPriceHistory(authService.getUserId(), DateTimeUtils.getCurrentTime(), p.getId(), p.getCost())).collect(Collectors.toList()));
        // 遍历物料历史列表，对于PDM ID有变动的物料记录变更原因
            priceHistoryEntityList.forEach(p -> {
                p.setRemark(GlobalConstants.NUMBER_ONE);
            });
    }

    /* Ended by AICoder, pid:65e0a9799fa6c6214571083c50286a578d5097bd */

    private void othCodeUpd(List<MaterialDto> material){
        log.info("material:{}",material);
        if(StringUtils.isNotBlank(material.get(0).getOthCode())){
            for (MaterialDto materialDto:material){
                if(StringUtils.isBlank(materialDto.getOthCode())){
                    continue;
                }
                MaterialVo materialVo=  materialRepository.multiTableQueryById(materialDto.getId());
                if(null!=materialVo){
                    if(StringUtils.isNotBlank(materialVo.getOthInfoId())){
                        OthInfoEntity othInfoEntity = new OthInfoEntity();
                        othInfoEntity.setId(materialVo.getOthInfoId());
                        othInfoEntity.setSalesCode(materialDto.getOthCode());
                        othSalesCodeRepository.editOthInfo(othInfoEntity);
                    }else {
                        OthInfoEntity othInfoEntity = new OthInfoEntity();
                        othInfoEntity.setId(UUID.randomUUID().toString());
                        othInfoEntity.setName(materialVo.getName());
                        othInfoEntity.setSalesCode(materialDto.getOthCode());
                        othSalesCodeRepository.addOthInfo(othInfoEntity);
                        MaterialEntity entity = new MaterialEntity();
                        entity.setId(materialVo.getId());
                        entity.setOthInfoId(othInfoEntity.getId());
                        materialRepository.editMaterial(entity);
                    }
                }


            }
        }

    }

    /* Started by AICoder, pid:g62d7o9e30d8cb61455c0a9500dbc86b78794a32 */
    /* Started by AICoder, pid:48093616eae56f6146a909d6d0732d8f72d12c67 */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pdmSync(String salesCode,String materialId) {

        PdmInfoEntity pdmInfoEntity = pdmInfoRepository.queryBySalesCode(salesCode);
        if (pdmInfoEntity == null) {
            //配置的pdm信息不存在
            log.error("pdm information:{} does not exist.",salesCode);
            throw new BusinessException(PdmStatusCode.NOT_PDM_CODE);
        }
        log.info("material start pdm sales code:{} sync.",pdmInfoEntity.getSalesCode());
        updatePdmAndRelatedMaterial(pdmInfoEntity,materialId);
    }

    private void updatePdmAndRelatedMaterial(PdmInfoEntity pdmInfoEntity,String materialId) {
        String salesCode = pdmInfoEntity.getSalesCode();
        //调用pdm接口查询pdm信息
        List<com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo> pdmInfoList = pdmApiService.getMaterialListByCode(Collections.singletonList(salesCode));
        log.info("pdm sync pdmInfoList:{}",pdmInfoList);
        if (CollectionUtils.isEmpty(pdmInfoList)) {
            log.error("The pdm information pdmApiService getMaterialListByCode:{} is empty.",salesCode);
            return;
        }
        //销售代码唯一
        com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo pdmVo = pdmInfoList.get(GlobalConstants.ZERO);
        //只有涉及生产代码、销售状态的变化才会更新对应物料
        String newSalesStatus = pdmVo.getSalesStatus();
        String newProductionCode = pdmVo.getProductionCode();
        //更新属性

        //如果物料成本有变化
//        boolean costChange = false;
//        //调用pdm接口查询成本信息
//        List<String> salesCodeList=new ArrayList<>();
//        salesCodeList.add(salesCode);
//        Map<String, String> materialCostInfo = getMaterialCostInfo(salesCodeList);
//        String newCost = materialCostInfo.getOrDefault(salesCode, "");
//        //根据物料id查询物料信息
//        MaterialEntity materialEntity = materialRepository.queryMaterialById(materialId);
//        String previousCost = materialEntity.getCost();
        //当本地成本与PDM成本不一致时，需要更新成本信息(不需要同步成本信息)
//        if (previousCost==null||!previousCost.equals(newCost)){
//            costChange=true;
//        }
        //  如果查询pdm生产代码、销售状态有变化的
        if (!StringUtils.isAllBlank(newSalesStatus,newProductionCode)) {
            String salesStatus = pdmInfoEntity.getSalesStatus();
            String productionCode = pdmInfoEntity.getProductionCode();
            boolean salesChange = false;
            if (StringUtils.isNotBlank(newSalesStatus)) {
                salesChange = !newSalesStatus.equals(salesStatus);
                pdmInfoEntity.setSalesStatus(newSalesStatus);
            }
            boolean codeChange = false;
            if (StringUtils.isNotBlank(newProductionCode)) {
                codeChange = !newProductionCode.equals(productionCode);
                pdmInfoEntity.setProductionCode(newProductionCode);
            }
            //  如果pdm生产代码、销售状态有任一变化，则更新pdm信息实体的名称
            if (salesChange || codeChange) {
                List<MaterialEntity> materialEntities = materialRepository.queryByPdmInfoIdAndStatus(pdmInfoEntity.getId(),MaterialStatusEnums.AVAILABLE.getId());
                //批量设置物料成本信息
                updateMaterial(materialEntities,pdmInfoEntity.getSalesStatus(),pdmInfoEntity.getProductionCode());
            }
        }
        //根据销售代码获取pdm成本已发生变化,物料版本+1、记录物料历史、记录成本变更历史
//        if (costChange){
//            materialCostChange(materialEntity,newCost);
//        }
        //更新pdm
        pdmInfoRepository.editPdmInfo(pdmInfoEntity);
    }

    private void updateMaterial(List<MaterialEntity> materialEntities,String salesStatus,String productionCode) {
        //限已上架的物料
        if (CollectionUtils.isNotEmpty(materialEntities)) {
            List<MaterialHistoryEntity> historyEntityList = new ArrayList<>();
            materialEntities.forEach(material ->{
                // 更新物料版本号
                Integer newVersion = Integer.parseInt(material.getVersion()) + GlobalConstants.ONE;
                material.setVersion(String.valueOf(newVersion));
                //构建历史表数据
                MaterialHistoryEntity historyEntity = mainCovertToHistory(material, null, null,new HashMap<>());
                historyEntity.setChangeReason(GlobalConstants.PDM);
                historyEntity.setSalesStatus(salesStatus);
                historyEntity.setProductionCode(productionCode);
                historyEntityList.add(historyEntity);
            });
            // 更新物料和新增历史记录
            materialRepository.batchUpdateMaterials(materialEntities);
            materialHistoryRepository.batchAddHistoryMaterial(historyEntityList);
        }
    }

    /* Started by AICoder, pid:11a6dm293be022f141a009a24069233e49438b43 */
    /**
     * 物料成本变更
     *
     * @param materialEntity 原物料信息
     * @param newCost        变更后的成本
     */
    private void materialCostChange(MaterialEntity materialEntity, String newCost) {
        // 1: 更新物料成本、版本
        materialEntity.setCost(newCost);
        int currentVersion = Integer.parseInt(materialEntity.getVersion());
        materialEntity.setVersion(String.valueOf(currentVersion + GlobalConstants.ONE));
        materialRepository.updateMaterial(materialEntity);

        // 2: 记录物料历史
        MaterialHistoryEntity materialHistoryEntity = new MaterialHistoryEntity();
        BeanUtils.copyProperties(materialEntity, materialHistoryEntity);
        String uuid = UUID.randomUUID().toString();
        materialHistoryEntity.setId(uuid);
        materialHistoryEntity.setMaterialId(materialEntity.getId());
        // 因为PDM手动同步不涉及上架流程，所以不记录流程ID
        materialHistoryEntity.setApprovalId("");
        materialHistoryEntity.setApprovalTime("");
        materialHistoryRepository.addHistoryMaterial(materialHistoryEntity);

        // 3: 记录成本历史
        ProcurementPriceHistoryEntity priceHistoryEntity = buildPriceHistory(
                authService.getUserId(),
                DateTimeUtils.getCurrentTime(),
                materialEntity.getId(),
                newCost
        );
        priceHistoryRepository.addPriceHistory(priceHistoryEntity);
    }

    /* Ended by AICoder, pid:11a6dm293be022f141a009a24069233e49438b43 */

    private MaterialEntity queryAndCheckMaterial(String id) {
        //权限控制，调整为根据物料id同步
        MaterialEntity materialEntity = materialRepository.queryMaterialById(id);
        if (materialEntity == null) {
            log.error("The material queried by the material id:{} does not exist",id);
            throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_EXIST);
        }
        if (!MaterialStatusEnums.AVAILABLE.getId().equals(materialEntity.getMaterialStatus())) {
            //pdm同步仅限 已上架的物料
            log.error("Current material status:{} does not support pdm synchronization.",materialEntity.getMaterialStatus());
            throw new BusinessException(ProductCategoryStatusCode.OPERATION_TYPE_NOT_SUPPORTED);
        }
        return materialEntity;
    }
    /* Ended by AICoder, pid:48093616eae56f6146a909d6d0732d8f72d12c67 */
    /* Ended by AICoder, pid:vefd3g4d54qe3ed1437c08ecb0d4776f33725037 */
    /* Ended by AICoder, pid:g62d7o9e30d8cb61455c0a9500dbc86b78794a32 */

    /* Started by AICoder, pid:w40famb039s64e8149bc080e32567e6aca00e27e */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlingMaterialOperation(String id, String operation,Integer applyOth) {
        //物料状态在主表中
        MaterialEntity materialEntity = materialRepository.queryMaterialById(id);
        if (materialEntity == null) {
            log.error("The material queried by the material id:{} does not exist",id);
            throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_EXIST);
        }
        ProductGroupEntity groupEntity = productGroupRepository.queryProductGroupDetail(materialEntity.getGroupId());
        if(groupEntity!=null){
            permissionUtil.checkPermission(groupEntity.getProductCategoryId(), PermissionEnum.PRODUCT_MATERIAL_PROCESS_OPERATION);
        }
        PdmInfoEntity pdmInfoEntity = pdmInfoRepository.queryById(materialEntity.getPdmInfoId());
        String salesCode = pdmInfoEntity != null ? pdmInfoEntity.getSalesCode() : "";
        //物料状态
        String currentStatus = materialEntity.getMaterialStatus();
        OperateEnums operateEnum = OperateEnums.getById(operation);
        switch (operateEnum) {
            case SUBMIT:
                handleSubmit(materialEntity, currentStatus, pdmInfoEntity,applyOth);
                break;
            case DE_LIST:
                handleDeList(materialEntity, currentStatus, pdmInfoEntity);
                break;
            case LISTING:
                handleListing(materialEntity, currentStatus, pdmInfoEntity,applyOth);
                break;
            case CANCEL_CHANGE:
                handleCancelChange(materialEntity, currentStatus);
                break;
            default:
                log.warn("Unsupported operation: {}", operation);
                throw new BusinessException(ProductCategoryStatusCode.OPERATE_ERROR);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchHandlingMaterial(List<String> ids, String operation, Integer applyOth) {
        //物料状态在主表
        List<MaterialEntity> materialEntities = materialRepository.queryMaterialByIds(ids);
        if (CollectionUtils.isEmpty(materialEntities)) {
            log.error("Material ids:{} corresponding materials does not exist, please confirm.",ids);
            throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_EXIST);
        }
        String groupId = materialEntities.stream().map(MaterialEntity::getGroupId).distinct().findFirst().orElse(null);
        ProductGroupEntity groupEntity = productGroupRepository.queryProductGroupDetail(groupId);
        if(groupEntity!=null){
            permissionUtil.checkPermission(groupEntity.getProductCategoryId(), PermissionEnum.PRODUCT_MATERIAL_BATCH_OPERATION);
        }
        List<String> pdmIds = materialEntities.stream().map(MaterialEntity::getPdmInfoId).collect(Collectors.toList());
        List<PdmInfoEntity> list = pdmInfoRepository.selectPdmInfoByIds(pdmIds);
        Map<String, PdmInfoEntity> idMap = list.stream().collect(Collectors.toMap(PdmInfoEntity::getId, pdmInfoEntity -> pdmInfoEntity));
        OperateEnums operateEnum = OperateEnums.getById(operation);
        switch (operateEnum) {
            case SUBMIT:
                batchChangeCommits(materialEntities,idMap,applyOth);
                break;
            case DE_LIST:
                batchRemoval(materialEntities,idMap);
                break;
            case LISTING:
                batchListing(materialEntities,idMap,ids,applyOth);
                break;
            default:
                log.warn("Unsupported batch operation: {}", operation);
                throw new BusinessException(ProductCategoryStatusCode.OPERATE_ERROR);
        }
    }

    private void batchChangeCommits(List<MaterialEntity> materialEntities,Map<String, PdmInfoEntity> idMap, Integer applyOth) {
        //批量提交 变更 审批
        //只有变更中的 可以提交 筛选数据
        List<MaterialEntity> entityList = materialEntities.stream().filter(p -> MaterialStatusEnums.LISTING.getId().equals(p.getMaterialStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(entityList)) {
            List<String> materialIds = entityList.stream().map(MaterialEntity::getId).collect(Collectors.toList());
            //审批查询的是临时表数据
            List<MaterialTemporaryEntity> temporaryEntities = materialTemporaryRepository.queryMaterialByIds(materialIds);
            //TODO 发起变更审批
            String approvalId = batchApproval(entityList,idMap,ApprovalTypeEnums.CHANGE_APPROVAL.getId(),applyOth);
            //更新主表 状态
            entityList.forEach(p->p.setMaterialStatus(MaterialStatusEnums.CHANGE_APPROVAL.getId()));
            materialRepository.batchUpdateMaterials(entityList);
            List<String> tempIds = temporaryEntities.stream().map(MaterialTemporaryEntity::getId).collect(Collectors.toList());
            //更新临时表的审批id
            materialTemporaryRepository.batchUpdateApprovalIdById(tempIds,approvalId);
        }
    }

    private void batchRemoval(List<MaterialEntity> materialEntities,Map<String, PdmInfoEntity> idMap) {
        //批量 提起 下架
        //只有已上架的才能下架
        List<MaterialEntity> entityList = materialEntities.stream().filter(p -> MaterialStatusEnums.AVAILABLE.getId().equals(p.getMaterialStatus())).collect(Collectors.toList());
        //过滤数据
        if (CollectionUtils.isNotEmpty(entityList)) {
            // 发起下架审批
            String approvalId = batchApproval(entityList,idMap,ApprovalTypeEnums.UNAVAILABLE_APPROVAL.getId(),null);
            List<MaterialTemporaryEntity> temporaryEntities = new ArrayList<>();
            //修改主表状态
            for (MaterialEntity materialEntity : entityList) {
                materialEntity.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE_APPROVAL.getId());
                MaterialTemporaryEntity temporaryEntity = buildMaterialTemporary(materialEntity, approvalId);
                temporaryEntities.add(temporaryEntity);
            }
            materialRepository.batchUpdateMaterials(entityList);
            //新增临时信息 保存审批信息
            materialTemporaryRepository.batchAddMaterialTemporary(temporaryEntities);
        }
    }

    private void batchListing(List<MaterialEntity> materialEntities,Map<String, PdmInfoEntity> idMap,List<String> ids, Integer applyOth) {
        //未完善pdm销售代码的不能上架
        List<MaterialEntity> collect = materialEntities.stream().filter(p -> StringUtils.isBlank(p.getPdmInfoId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)&&applyOth==0) {
            log.error("Materials without pdm sales code cannot be listed.");
            throw new BusinessException(ProductCategoryStatusCode.MATERIAL_WITHOUT_PDM);
        }
        if (applyOth==1) {
            if(CollectionUtils.isEmpty(collect)||materialEntities.size()!=collect.size()){
                log.error("The application for OTH shelving materials cannot have PDM sales codes");
                throw new BusinessException(ProductCategoryStatusCode.MATERIAL_OTH);
            }
        }
        //批量提交 上架 只有已下架和草稿允许上架
        List<MaterialEntity> entityList = materialEntities.stream()
                .filter(p ->(MaterialStatusEnums.UNAVAILABLE.getId().equals(p.getMaterialStatus())
                        || MaterialStatusEnums.DRAFT.getId().equals(p.getMaterialStatus())))
                .collect(Collectors.toList());
        //todo 选型单关联的物料 仅在选型单状态为：已开标 时才可提交上架审批
        List<LectotypeMaterialVo> lectotypeVos = materialRepository.querySelectionFormByMaterialIds(ids);
        if (CollectionUtils.isNotEmpty(lectotypeVos)) {
            //批量上架的物料中有部分选型单关联的物料
            Set<String> stringSet = new HashSet<>();
            for (LectotypeMaterialVo le:lectotypeVos ) {
                if(LectotypeTypeEnums.ONE.type.equals(le.getLectotypeType())&&!LectotypeStatusEnums.FIVE.type.equals(le.getLectotypeStatus())){
                    stringSet.add(le.getMaterialId());
                }
            }
            //List<LectotypeMaterialVo> lectotypeMaterialVos = lectotypeVos.stream().filter(p -> !LectotypeStatusEnums.FIVE.type.equals(p.getLectotypeStatus())).collect(Collectors.toList());
            if (stringSet.size()>0) {
                //选型单状态不对的物料 不能提交
                log.error("The selection form status does not support the listing of associated materials:{}",stringSet);
                throw new BusinessException(ProductCategoryStatusCode.UNSUPPORTED_OPERATION_TYPE);
            }
        }
        if (CollectionUtils.isNotEmpty(entityList)) {
            // 发起 上架 审批
            String approvalId = batchApproval(entityList,idMap,ApprovalTypeEnums.AVAILABLE_APPROVAL.getId(),applyOth);
            //主表需要更新
            List<MaterialEntity> updateList = new ArrayList<>();
            //已下架 需要更新到临时表
            List<MaterialTemporaryEntity> tempList = new ArrayList<>();
            //草稿
            String currentTime = DateTimeUtils.getCurrentTime();
            for (MaterialEntity entity : entityList) {
                if (MaterialStatusEnums.DRAFT.getId().equals(entity.getMaterialStatus())) {
                    //草稿
                    entity.setApprovalId(approvalId);
                    entity.setUpdateTime(currentTime);
                } else {
                    //已下架的 发起审批 需要存一份到临时表
                    MaterialTemporaryEntity temporaryEntity = buildMaterialTemporary(entity, approvalId);
                    tempList.add(temporaryEntity);
                }
                entity.setMaterialStatus(MaterialStatusEnums.AVAILABLE_APPROVAL.getId());
                updateList.add(entity);
            }
            materialRepository.batchUpdateMaterials(updateList);
            //已下架 审批信息存临时表
            temd(tempList,idMap);
        }
    }

    private void temd(List<MaterialTemporaryEntity> tempList,Map<String, PdmInfoEntity> idMap){
        if(null!=idMap&&idMap.size()>0) {
            materialTemporaryRepository.batchAddMaterialTemporary(tempList);
        }
    }

    private void handleCancelChange(MaterialEntity materialEntity, String currentStatus) {
        //取消变更 限变更中
        if (MaterialStatusEnums.LISTING.getId().equals(currentStatus)) {
            //更新主表状态
            materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
            materialRepository.editMaterial(materialEntity);
            //删除临时表
            materialTemporaryRepository.deleteByMaterialIds(Collections.singletonList(materialEntity.getId()));
        }
    }

    private void inspectionMaterial(MaterialEntity materialEntity,PdmInfoEntity pdmInfoEntity,Integer applyOth){

        //物料上架前检查，非18销售代码的物料不允许上架
        String pdmInfoId = materialEntity.getPdmInfoId();

        if (applyOth==0) {
            if(StringUtils.isBlank(pdmInfoId)){
                log.error("Materials without pdm sales code cannot be listed.");
                //物料尚未绑定pdm代码，不能上架
                throw new BusinessException(ProductCategoryStatusCode.MATERIAL_WITHOUT_PDM);
            }
            if (pdmInfoEntity == null) {
                //绑定的18销售代码不存在，请完善后再上架
                log.error("The material binding pdm sales code:{} does not exist.",pdmInfoId);
                throw new BusinessException(PdmStatusCode.INVALID_PDM_DATA);
            }

        }
        if(applyOth==1&&StringUtils.isNotBlank(pdmInfoId)&&pdmInfoEntity != null){
            log.error("The application for OTH shelving materials cannot have PDM sales codes");
            throw new BusinessException(ProductCategoryStatusCode.MATERIAL_OTH);
        }

    }
    private void handleListing(MaterialEntity materialEntity, String currentStatus,PdmInfoEntity pdmInfoEntity,Integer applyOth) {
        //上架前校验
        inspectionMaterial(materialEntity,pdmInfoEntity,applyOth);

        //TODO 增加选型单物料上架前选型单状态检查
        List<LectotypeMaterialVo> lectotypeVos = materialRepository.querySelectionFormByMaterialIds(Collections.singletonList(materialEntity.getId()));
        if (CollectionUtils.isNotEmpty(lectotypeVos)) {
            //批量上架的物料中有部分选型单关联的物料 去除选型单状态不对的物料
            LectotypeMaterialVo lectotypeVo = lectotypeVos.get(GlobalConstants.ZERO);
            if (LectotypeTypeEnums.ONE.type.equals(lectotypeVo.getLectotypeType()) && !LectotypeStatusEnums.FIVE.type.equals(lectotypeVo.getLectotypeStatus())) {
                log.error("The selection form status:{} does not support the listing of associated materials",lectotypeVo.getLectotypeStatus());
                throw new BusinessException(ProductCategoryStatusCode.UNSUPPORTED_OPERATION_TYPE);
            }
        }

        //上架 限草稿和已下架
        String approvalId = getApprovalId(materialEntity.getId(),materialEntity,pdmInfoEntity,ApprovalTypeEnums.AVAILABLE_APPROVAL.getId(),applyOth);
        //设置物料状态
        materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE_APPROVAL.getId());
        //草稿
        if (MaterialStatusEnums.DRAFT.getId().equals(currentStatus)) {
            //更新主表状态，草稿的审批id更新到草稿
            materialEntity.setApprovalId(approvalId);
            String currentTime = DateTimeUtils.getCurrentTime();
            materialEntity.setUpdateTime(currentTime);
            materialRepository.editMaterial(materialEntity);
        }
        //已下架
        if (MaterialStatusEnums.UNAVAILABLE.getId().equals(currentStatus)) {
            //更新到临时表
            if(null!=pdmInfoEntity&&StringUtils.isNotBlank(pdmInfoEntity.getId())) {
                MaterialTemporaryEntity temporary = buildMaterialTemporary(materialEntity, approvalId);
                materialTemporaryRepository.addTempMaterial(temporary);
                //已下架物料仅更新主表状态，不能覆盖原审批id
                materialRepository.editMaterial(materialEntity);
            }else {
                materialEntity.setApprovalId(approvalId);
                materialRepository.editMaterial(materialEntity);
            }

        }
    }

    private void handleDeList(MaterialEntity materialEntity, String currentStatus,PdmInfoEntity pdmInfoEntity) {
        //下架 限已上架的物料
        if (MaterialStatusEnums.AVAILABLE.getId().equals(currentStatus)) {
            //todo 发起下架审批
            String approvalId = getApprovalId(materialEntity.getId(),materialEntity,pdmInfoEntity,ApprovalTypeEnums.UNAVAILABLE_APPROVAL.getId(),null);
            //新增到临时表
            if(null!=pdmInfoEntity) {
                MaterialTemporaryEntity temporary = buildMaterialTemporary(materialEntity, approvalId);
                materialTemporaryRepository.addTempMaterial(temporary);
            }else {
                materialEntity.setApprovalId(approvalId);
            }
            //更新主表状态
            materialEntity.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE_APPROVAL.getId());
            materialRepository.editMaterial(materialEntity);
        }
    }

    private void handleSubmit(MaterialEntity materialEntity, String currentStatus,PdmInfoEntity pdmInfoEntity,Integer applyOth) {
        //变更提交处理 限变更中的物料
        if (MaterialStatusEnums.LISTING.getId().equals(currentStatus)) {
            MaterialTemporaryEntity temporaryEntity = materialTemporaryRepository.queryTempMaterialByMaterialId(materialEntity.getId());
            if (temporaryEntity == null) {
                log.error("The temporary material does not exist. Please confirm the material information.");
                throw new BusinessException(ProductCategoryStatusCode.TEMPORARY_NOT_EXIST);
            }
            // 发起变更审批,审批变更中的信息
            MaterialEntity material = MaterialConvert.INSTANCE.temporaryEntityToMaterialEntity(temporaryEntity);
            //转换后id错位需要调整
            material.setId(materialEntity.getId());
            PdmInfoEntity pdmInfoEntity1 = pdmInfoRepository.queryById(temporaryEntity.getPdmInfoId());
            String approvalId = getApprovalId(materialEntity.getId(),material,pdmInfoEntity1,
                    ApprovalTypeEnums.CHANGE_APPROVAL.getId(),applyOth);
            temporaryEntity.setApprovalId(approvalId);
            //更新临时表
            materialTemporaryRepository.updateTempMaterial(temporaryEntity);
            //更新主表
            materialEntity.setMaterialStatus(MaterialStatusEnums.CHANGE_APPROVAL.getId());
            materialRepository.editMaterial(materialEntity);
        }
    }
    //通过主物料信息构建临时物料
    private MaterialTemporaryEntity buildMaterialTemporary(MaterialEntity materialEntity,String approvalId) {
        MaterialTemporaryEntity temporary = MaterialConvert.INSTANCE.materialEntityToTemporaryEntity(materialEntity);
        temporary.setId(UUID.randomUUID().toString());
        temporary.setApprovalId(approvalId);
        temporary.setMaterialId(materialEntity.getId());
        return temporary;
    }
    /* Ended by AICoder, pid:w40famb039s64e8149bc080e32567e6aca00e27e */
    public void handleApprovedMaterialInformation(Map<String, MaterialDto> materialMap,boolean draftFlag,
                                                  List<MaterialEntity> currentList, List<MaterialTemporaryEntity> tempEntities, ApprovalInformationDto informationDto,
                                                  List<MaterialEntity> mainMaterialList, List<MaterialHistoryEntity> historyMaterialList
    ) {
        //审批通过
        //发起人及审批结束时间
        String submitter = informationDto.getSubmitter();
        String approvalTime = informationDto.getApprovalTime();
        if (GlobalConstants.REMOVAL_APPROVAL.equals(informationDto.getApprovalType())) {
            //下架审批通过 只更新主表状态及审批id
            currentList = currentList.stream().peek(p -> {
                p.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE.getId());
                p.setApprovalId(informationDto.getApprovalId());
            }).collect(Collectors.toList());
            mainMaterialList.addAll(currentList);
            return;
        }
        //价格历史表数据
        List<ProcurementPriceHistoryEntity> priceHistoryEntityList = new ArrayList<>();
        if (draftFlag){
            //草稿上架审批通过 更新主表 历史表
            //审批后挟带的信息更新到主表中
            //是不是成本总监补充成本
            boolean costSup = false;
            if (GlobalConstants.SUPPLEMENTARY_COST.equals(informationDto.getSupplementaryCost())){
                costSup = true;
            }

            String currentTime = DateTimeUtils.getCurrentTime();
            boolean finalCostSup = costSup;
            currentList = currentList.stream().peek(p->{
                //状态
                p.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
                //版本变更 oth审批通过就有版本
               String version = StringUtils.isBlank(p.getVersion()) ? String.valueOf(GlobalConstants.ZERO) : p.getVersion();
                String newVersion = String.valueOf(Integer.parseInt(version) + 1);
                p.setVersion(newVersion);
                //p.setVersion(String.valueOf(GlobalConstants.ONE));
                MaterialDto materialDto = materialMap.get(p.getId());
                if (finalCostSup && materialDto != null && materialDto.getCost() != null) {
                    String cost = p.getCost();
                    if (!materialDto.getCost().equals(cost)) {
                        priceHistoryEntityList.add(buildPriceHistory(submitter,currentTime,p.getId(),materialDto.getCost()));
                    }
                    p.setCost(materialDto.getCost());
                }
            }).collect(Collectors.toList());

            //构建历史表数据
            List<String> stringList = currentList.stream().map(MaterialEntity::getPdmInfoId).collect(Collectors.toList());
            // OTH代码上架也需要记录历史 需要记录oth销售代码
            Map<String, PdmInfoEntity> pdmInfoEntityMap = pdmInfoRepository.selectPdmInfoByIds(stringList).stream().collect(Collectors.toMap(PdmInfoEntity::getId, Function.identity()));
            //同步pdm成本
            synchronizePdmCosts(currentList,priceHistoryEntityList);
            List<MaterialHistoryEntity> historyList = currentList.stream()
                    .map(p -> mainCovertToHistory(p, submitter, approvalTime,pdmInfoEntityMap)).collect(Collectors.toList());
            historyList.forEach(p->updateOthCodeSalesStatus(p,materialMap));
            historyMaterialList.addAll(historyList);
            priceHistoryRepository.batchAddHistory(priceHistoryEntityList);
            //构建 主表需要更新的数据,通过临时表信息转换
            mainMaterialList.addAll(currentList);
            return;
        }

        //变更审批通过 已下架再上架审批通过
        //构建 主表需要更新的数据,通过临时表信息转换
        List<MaterialEntity> mainUpdateMaterialList = buildMainMaterial(tempEntities, materialMap);
        //同步pdm成本
        synchronizePdmCostByDifferences(currentList, mainUpdateMaterialList, priceHistoryEntityList);
        priceHistoryRepository.batchAddHistory(priceHistoryEntityList);
        mainMaterialList.addAll(mainUpdateMaterialList);
        //构建历史表数据
        List<String> stringList = mainUpdateMaterialList.stream().map(MaterialEntity::getPdmInfoId).collect(Collectors.toList());
        Map<String, PdmInfoEntity> pdmInfoEntityMap = pdmInfoRepository.selectPdmInfoByIds(stringList).stream().collect(Collectors.toMap(PdmInfoEntity::getId, Function.identity()));
        List<MaterialHistoryEntity> historyList = mainUpdateMaterialList.stream()
                .map(p -> mainCovertToHistory(p, submitter, approvalTime,pdmInfoEntityMap)).collect(Collectors.toList());
        historyList.forEach(p->updateOthCodeSalesStatus(p,materialMap));
        historyMaterialList.addAll(historyList);
    }

    private void synchronizePdmCosts(List<MaterialEntity> currentList,List<ProcurementPriceHistoryEntity> priceHistoryEntityList){
        //currentList过滤筛选出18代码(根据pdmId是否为空过滤)然后补充对应价格
        List<String> pdmIdList = currentList.stream()
                .filter(material -> StringUtils.isNotBlank(material.getPdmInfoId()))
                .map(MaterialEntity::getPdmInfoId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pdmIdList)||pdmIdList.size() == 0){
            //对于非18代码的数据不进行pdm成本同步
            return;
        }
        //收集pdmId为key,销售代码为value的map集合
        Map<String, String> pdmSalesCodeMap = pdmInfoRepository.selectPdmInfoByIds(pdmIdList)
                .stream()
                .collect(Collectors.toMap(
                        PdmInfoEntity::getId,      // 提取 id 作为键
                        PdmInfoEntity::getSalesCode // 提取 salesCode 作为值
                ));
        //收集销售代码
        List<String> pdmSalesCodeList = pdmInfoRepository.selectPdmInfoByIds(pdmIdList)
                .stream()
                .map(PdmInfoEntity::getSalesCode)
                .collect(Collectors.toList());
        //根据销售代码从pdm获取物料成本
        Map<String, String> materialCostInfo = getMaterialCostInfo(pdmSalesCodeList);
        //遍历currentList根据赋予pdm成本价格
        currentList.forEach(p -> {
            if (StringUtils.isNotBlank(p.getPdmInfoId())) {
                String salesCode = pdmSalesCodeMap.get(p.getPdmInfoId());
                p.setCost(materialCostInfo.getOrDefault(salesCode,""));
            }
        });
        //根据物料id集合构造成本历史数据
        priceHistoryEntityList.addAll(currentList.stream().map(p -> buildPriceHistory(authService.getUserId(), DateTimeUtils.getCurrentTime(), p.getId(), p.getCost())).collect(Collectors.toList()));
        //记录变更原因(物料创建成本从Pdm获取)
        priceHistoryEntityList.forEach(h->{
            h.setRemark(GlobalConstants.NUMBER_TWO);
        });
    }
    private void updateOthCodeSalesStatus(MaterialHistoryEntity historyEntity, Map<String, MaterialDto> materialMap) {
        MaterialDto materialDto = materialMap.get(historyEntity.getMaterialId());
        if (materialDto != null && StringUtils.isNotBlank(materialDto.getOthCode())) {
            historyEntity.setSalesStatus(GlobalConstants.OTH_SALES_CODE);
        }
    }
    private void assemblyHistoryCost(Map<String, PdmInfoEntity> pdmInfoEntityMap) {
        //收集销售代码
        List<String> salesCodeList = pdmInfoEntityMap.values().stream()
                .map(PdmInfoEntity::getSalesCode)
                .collect(Collectors.toList());
    }
    private ProcurementPriceHistoryEntity buildPriceHistory(String submitter, String currentTime, String id, String cost) {
        ProcurementPriceHistoryEntity priceHistoryEntity=new ProcurementPriceHistoryEntity();
        priceHistoryEntity.setId(UUID.randomUUID().toString());
        priceHistoryEntity.setPrice(cost);
        priceHistoryEntity.setPriceCategory(PriceCategoryEnums.COST_PRICE.getCode());
        //因为变更物料成本没有选型单id,这里存物料id
        priceHistoryEntity.setLectotypeId(id);
        priceHistoryEntity.setLectotypeType(id);
        priceHistoryEntity.setCreateBy(submitter);
        priceHistoryEntity.setCreateTime(currentTime);
        priceHistoryEntity.setUpdateBy(submitter);
        priceHistoryEntity.setUpdateTime(currentTime);
        return priceHistoryEntity;
    }

    //更新临时表信息
    public List<MaterialEntity> buildMainMaterial(List<MaterialTemporaryEntity> tempEntities, Map<String, MaterialDto> materialMap) {
        //主表数据：currentList与mainUpdateMaterialList临时表数据（比较pdmId是否变化，成本是否变化）
        //查询数据库主表的最新数据
        List<MaterialEntity> lastMaterialEntities = materialRepository.queryMaterialByIds(tempEntities.stream().map(MaterialTemporaryEntity::getMaterialId).collect(Collectors.toList()));
        //类型转换 key为物料id，value为物料对象
        Map<String, MaterialEntity> lastMaterialDtoMap =  lastMaterialEntities.stream().collect(Collectors.toMap(MaterialEntity::getId, materialEntity -> materialEntity));
        List<MaterialEntity> mainUpdateMaterialList = new ArrayList<>();
        //更新临时表信息
        for (MaterialTemporaryEntity temporaryEntity : tempEntities) {
            MaterialEntity materialEntity = MaterialConvert.INSTANCE.temporaryEntityToMaterialEntity(temporaryEntity);
            //转换后id错位需要调整
            materialEntity.setId(temporaryEntity.getMaterialId());
            //这里的版本从主表取保证版本一致性(否则会出现物料变更中，用户进行成本变更导致版本不一致的问题)
            String mainVersion=lastMaterialDtoMap.get(temporaryEntity.getMaterialId()).getVersion();
            int newVersion = StringUtils.isBlank(mainVersion) ? GlobalConstants.ONE : Integer.parseInt(mainVersion) + 1;
            //状态
            materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
            //版本变更
            materialEntity.setVersion(String.valueOf(newVersion));
            //审批id
            materialEntity.setApprovalId(temporaryEntity.getApprovalId());
            MaterialDto materialDto = materialMap.get(materialEntity.getId());
            if (materialDto != null) {
                if (materialDto.getCost() != null) {
                    materialEntity.setCost(materialDto.getCost());
                }
            }
            mainUpdateMaterialList.add(materialEntity);
        }
        return mainUpdateMaterialList;
    }

    //审批通过后 历史表记录一份当前主表信息
    public MaterialHistoryEntity mainCovertToHistory(MaterialEntity materialEntity,String submitter,String approvalTime,Map<String, PdmInfoEntity> pdmInfoEntityMap){
        MaterialHistoryEntity historyEntity = MaterialConvert.INSTANCE.mainEntityToMaterialHistoryEntity(materialEntity);
        //转换后历史表id需要生成，物料表需要填写
        historyEntity.setId(UUID.randomUUID().toString());
        historyEntity.setMaterialId(materialEntity.getId());
        historyEntity.setSubmitter(submitter);
        historyEntity.setApprovalTime(approvalTime);
        historyEntity.setChangeReason(GlobalConstants.APPROVED);
        //pdm同步字段变更需要记录
        PdmInfoEntity pdmInfoEntity = pdmInfoEntityMap.get(materialEntity.getPdmInfoId());
        if (pdmInfoEntity != null) {
            historyEntity.setSalesStatus(pdmInfoEntity.getSalesStatus());
            historyEntity.setProductionCode(pdmInfoEntity.getProductionCode());
        }
        return historyEntity;
    }

    @Override
    public String addMaterial(MaterialAddDto addDto, String pdmId, String othInfoId) {

        ProductGroupEntity groupEntity = productGroupRepository.queryProductGroupDetail(addDto.getGroupId());
        if(groupEntity!=null){
            permissionUtil.checkPermission(groupEntity.getProductCategoryId(), PermissionEnum.PRODUCT_MATERIAL_ADD);
        }
        MaterialEntity entity = new MaterialEntity();
        String id = UUID.randomUUID().toString();
        entity.setId(id);
        entity.setName(addDto.getName());
        entity.setBrand(addDto.getBrand());
        entity.setSupplier(addDto.getSupplier());
        entity.setPurchaseMode(addDto.getPurchaseMode());
        entity.setExpirationDate(addDto.getExpirationDate());
        entity.setWarrantyPeriod(addDto.getWarrantyPeriod());
        entity.setGroupId(addDto.getGroupId());
        entity.setPdmInfoId(pdmId);
        entity.setUnit(addDto.getUnit());
        entity.setDescription(addDto.getDescription());
        entity.setService(addDto.getService());
        entity.setSpecificationModel(addDto.getSpecificationModel());
        entity.setRecommendedLevel(addDto.getRecommendedLevel());
        entity.setOthInfoId(othInfoId);
        String userId = authService.getUserId();
        entity.setCreateBy(userId);
        entity.setUpdateBy(userId);
        String currentTime = DateTimeUtils.getCurrentTime();
        entity.setCreateTime(currentTime);
        entity.setUpdateTime(currentTime);
        entity.setNameEn(addDto.getNameEn());
        entity.setUnitEn(addDto.getUnitEn());
        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setSalesCode(addDto.getSalesCode());
        pdmInfoEntity.setProductionCode(addDto.getProductionCode());
        pdmInfoEntity.setSalesStatus(addDto.getSalesStatus());
        //根据销售代码从PDM获取物料成本
        if (StringUtils.isNotBlank(addDto.getSalesCode())) {
            List<String> salasesCodeList = new ArrayList<>();
            salasesCodeList.add(addDto.getSalesCode());
            Map<String, String> materialCostInfo = getMaterialCostInfo(salasesCodeList);
            entity.setCost(materialCostInfo.getOrDefault(addDto.getSalesCode(), ""));
        }
        if (OperateEnums.SUBMIT.getId().equals(addDto.getOperate())) {
            if (StringUtils.isNotBlank(pdmId)||addDto.getApplyOth()==1) {
                //提交审批
                String approvalId = getApprovalId(entity.getId(),entity,pdmInfoEntity,ApprovalTypeEnums.AVAILABLE_APPROVAL.getId(),addDto.getApplyOth());
                entity.setApprovalId(approvalId);
                entity.setMaterialStatus(MaterialStatusEnums.AVAILABLE_APPROVAL.getId());
            } else {
                //非pdm的物料不能上架
                log.error("Materials without pdm sales code cannot be submit.");
                throw new BusinessException(ProductCategoryStatusCode.MATERIAL_WITHOUT_PDM);
            }
        }
        else {
            entity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());
        }
        materialRepository.addMaterial(entity);

        //TODO 如果成本信息不为空,记录一次成本变更历史
        //materialHistoryRepository.addHistoryMaterial();
        //priceHistoryRepository.addPriceHistory();
        return id;
    }


    //调用pdm接口根据销售代码获取物料成本信息
    private Map<String,String> getMaterialCostInfo(List<String> salesCodeList){
        if (CollectionUtils.isEmpty(salesCodeList)) {
            return Collections.emptyMap();
        }
        List<com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo> materialListCost = pdmApiService.getMaterialListCostByCode(salesCodeList);
        if (CollectionUtils.isNotEmpty(materialListCost)) {
            //根据销售代码拼接Map集合(key为销售代码,value为成本)
            return materialListCost.stream().collect(Collectors.toMap(com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo::getSalesCode, com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo::getCost));
        }else {
            return Collections.emptyMap();
        }
    }
    @Override
    public String addPdmInfo(MaterialAddDto addDto) {

        // 从数据库查询或创建一个新的实体对象
        PdmInfoEntity pdmInfoEntity = Optional.ofNullable(pdmInfoRepository.queryBySalesCode(addDto.getSalesCode()))
                .orElse(new PdmInfoEntity());
        // 设置公共属性
        pdmInfoEntity.setSalesCode(addDto.getSalesCode());
        pdmInfoEntity.setName(addDto.getName());
        pdmInfoEntity.setSalesStatus(addDto.getSalesStatus());
        pdmInfoEntity.setProductionCode(addDto.getProductionCode());
        pdmInfoEntity.setUnit(addDto.getUnit());
        pdmInfoEntity.setDescription(addDto.getDescription());
        // 如果是新实体，则设置 ID 并保存为新增记录；否则更新现有记录
        if (StringUtils.isBlank(pdmInfoEntity.getId())) {
            String pdmId = UUID.randomUUID().toString();
            pdmInfoEntity.setId(pdmId);
            pdmInfoRepository.addPdmInfo(pdmInfoEntity);
            return pdmId;
        } else {
            pdmInfoRepository.editPdmInfo(pdmInfoEntity);
            return pdmInfoEntity.getId();
        }
    }

    @Override
    public void relateDocuments(String materialId, List<String> documentIds) {
        if (CollectionUtils.isEmpty(documentIds)) {
            documentService.deleteByResourceId(materialId);
            return;
        }
        documentService.relateDocuments(materialId,documentIds, DocumentRelateResourceTypeEnum.MATERIAL.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaterial(String id) {
        MaterialEntity entity = materialRepository.queryMaterialById(id);
        if (entity == null) {
            return;
        }
        ProductGroupEntity groupEntity = productGroupRepository.queryProductGroupDetail(entity.getGroupId());
        if(groupEntity!=null){
            permissionUtil.checkPermission(groupEntity.getProductCategoryId(), PermissionEnum.PRODUCT_MATERIAL_DELETE);
        }
        String materialStatus = entity.getMaterialStatus();
        if (!MaterialStatusEnums.DRAFT.getId().equals(materialStatus) &&
                !MaterialStatusEnums.UNAVAILABLE.getId().equals(materialStatus)) {
            throw new BusinessException(ProductCategoryStatusCode.UNSUPPORTED_DELETE);
        }
        //todo 校验是否被使用
        materialRepository.deleteMaterial(id);
        if (MaterialStatusEnums.UNAVAILABLE.getId().equals(materialStatus)) {
            //已下架的，需要同步删除可能存在的临时表数据
            materialTemporaryRepository.deleteByMaterialIds(Collections.singletonList(id));
        }
        if (StringUtils.isNotBlank(entity.getOthInfoId())) {
            othSalesCodeRepository.deleteOthSalesCodeById(entity.getOthInfoId());
        }
        //删除文档关联关系
        documentService.deleteByResourceId(id);
    }

    /* Started by AICoder, pid:taa7bhdd44hd5f9144cd08e5505cbf23bef4d1fc */
    private String getApprovalId(String id, MaterialEntity entity, PdmInfoEntity pdmInfoEntity, String type, Integer approvalOth) {
        // 优化：提前处理可能的空值，减少嵌套层级
        ProductGroupEntity productGroupEntity = productGroupRepository.selectProductGroupById(entity.getGroupId());

        //补充生产代码

        NewApprovalDto approvalDto = new NewApprovalDto();
        approvalDto.setType(Integer.parseInt(type));
        approvalDto.setApprovalOth(approvalOth);
        MaterialToApprovalDto dto = new MaterialToApprovalDto();
        BeanUtils.copyProperties(entity,dto);
        dto.setId(id);
        if (productGroupEntity != null) {
            dto.setGroupName(productGroupEntity.getName());
            approvalDto.setProductCategory(productGroupEntity.getProductCategoryId());
        }
        log.debug("getApprovalId pdmInfoEntity:{}",pdmInfoEntity);
        approvalDto.setApproval18Code(GlobalConstants.ONE);
        if (pdmInfoEntity != null && StringUtils.isNotBlank(pdmInfoEntity.getSalesCode())) {
            dto.setSalesCode(pdmInfoEntity.getSalesCode());
            dto.setProductCode(pdmInfoEntity.getProductionCode());
            dto.setSaleStatus(pdmInfoEntity.getSalesStatus());
            approvalDto.setApproval18Code(GlobalConstants.ZERO);
        }
        approvalDto.setMaterials(Collections.singletonList(dto));
        log.debug("processService createApproval approvalDto:{}",approvalDto);
        return processService.createApproval(approvalDto);
    }
    /* Ended by AICoder, pid:taa7bhdd44hd5f9144cd08e5505cbf23bef4d1fc */
    /* Started by AICoder, pid:q4fc4l316dmbc50147ff0919d019122af1897798 */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editMaterial(MaterialEditDto editDto, String othInfoId) {
        // 查询材料实体
        MaterialEntity entity = materialRepository.queryMaterialById(editDto.getId());
        if (entity == null) {
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        ProductGroupEntity groupEntity = productGroupRepository.queryProductGroupDetail(entity.getGroupId());
        if(groupEntity!=null){
            permissionUtil.checkPermission(groupEntity.getProductCategoryId(), PermissionEnum.PRODUCT_MATERIAL_EDIT);
        }
        String materialStatus = entity.getMaterialStatus();
        String operate = editDto.getOperate();
        String userId = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();
        String pdmInfoId = StringUtils.EMPTY;
        if (StringUtils.isBlank(othInfoId)) {
            // 更新PDM信息
            pdmInfoId = updatePdmInfo(editDto);
        }else {
            entity.setOthInfoId(othInfoId);
        }

        OperateEnums operateEnums = OperateEnums.getById(operate);
        // 根据操作类型进行处理
        switch (operateEnums) {
            case TEMPORARILY_STORE:
                temporarilyStore(materialStatus, entity, editDto, pdmInfoId,othInfoId, userId, currentTime);
                break;
            case SUBMIT:
                if (StringUtils.isBlank(pdmInfoId)&&editDto.getApplyOth()==0) {
                    throw new BusinessException(ProductCategoryStatusCode.MATERIAL_WITHOUT_PDM);
                }
                submit(materialStatus, entity, editDto, pdmInfoId, userId, currentTime);
                break;
            default:
                throw new BusinessException(ProductCategoryStatusCode.OPERATION_TYPE_NOT_SUPPORTED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editMaterial1(MaterialEditDto editDto, String othInfoId) {
        // 查询材料实体
        MaterialEntity entity = materialRepository.queryMaterialById(editDto.getId());
        if(null==entity){
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        String materialStatus = entity.getMaterialStatus();
        String userId = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();
        String pdmInfoId = StringUtils.EMPTY;
        if (StringUtils.isBlank(othInfoId)) {
            // 更新PDM信息
            pdmInfoId = updatePdmInfo(editDto);
        }else {
            entity.setOthInfoId(othInfoId);
        }

        MaterialStatusEnums status = MaterialStatusEnums.getById(materialStatus);

        switch (status) {
            case DRAFT:
                BeanUtils.copyProperties(editDto, entity);
                entity.setPdmInfoId(pdmInfoId);
                entity.setDescription(editDto.getDescription());
                entity.setUpdateBy(userId);
                entity.setUpdateTime(currentTime);
                //空值设置
                entity.setOthInfoId(othInfoId);
                materialRepository.updateDraftMaterialRelation(entity.getId(),pdmInfoId,othInfoId);
                materialRepository.editMaterial(entity);
                break;

            case LISTING:
            case UNAVAILABLE:
                if(StringUtils.isNotBlank(pdmInfoId)) {
                    createAndSaveTempEntity(editDto, pdmInfoId, entity.getApprovalId(), userId, currentTime, entity);
                }else {
                    BeanUtils.copyProperties(editDto, entity);
                    entity.setPdmInfoId(pdmInfoId);
                    entity.setDescription(editDto.getDescription());
                    entity.setUpdateBy(userId);
                    entity.setUpdateTime(currentTime);
                    //空值设置
                    entity.setOthInfoId(othInfoId);
                    materialRepository.updateDraftMaterialRelation(entity.getId(),pdmInfoId,othInfoId);
                    materialRepository.editMaterial(entity);
                }
                break;

            case AVAILABLE:
                // 更新正式表
                updateMainEntity(entity, entity.getApprovalId(), MaterialStatusEnums.LISTING.getId(), userId, currentTime);
                // 存入临时表
                if(StringUtils.isNotBlank(pdmInfoId)) {
                    createAndSaveTempEntity(editDto, pdmInfoId, entity.getApprovalId(), userId, currentTime, entity);
                }
                break;

            default:
                throw new BusinessException(ProductCategoryStatusCode.PARENT_PRODUCT_ERROR);
        }
    }

    /* Started by AICoder, pid:9114659d60zee3a14b960bfa703d68243fb6159c */
    @Override
    /* Started by AICoder, pid:t5aa70756fxf322142cd0bd210e54e60a107bbe5 */

    public List<MaterialVersionVo> queryMaterialVersionById(String id) {
        // 查询版本记录，版本记录存储在历史表中
        List<MaterialHistoryEntity> materialHistoryEntities = materialHistoryRepository.queryMaterialVersionById(id);

        // 如果没有找到任何版本记录，返回空列表
        if (CollectionUtils.isEmpty(materialHistoryEntities)) {
            log.error("No version information found for material ID: {}", id);
            return Collections.emptyList();
        }

        // 提取去重后的用户ID（排除null）
        List<String> userIds = materialHistoryEntities.stream()
                .map(MaterialHistoryEntity::getUpdateBy)
                .filter(Objects::nonNull)
                .distinct() // 去重提升查询效率
                .collect(Collectors.toList());

        // 批量查询用户信息（空ID列表时不查询）
        Map<String, UserVo> userMap;
        if (!userIds.isEmpty()) {
            List<UserVo> userVos = systemService.getUserinfoByIds(userIds);
            userMap = userVos.stream()
                    .collect(Collectors.toMap(
                            UserVo::getId,
                            Function.identity(),
                            (existing, replacement) -> existing));
        } else {
            userMap = Collections.emptyMap();
        }

        // 将查询到的历史记录转换为版本视图对象列表
        return materialHistoryEntities.stream()
                .map(entity -> {
                    MaterialVersionVo vo = convertToVersionVo(entity);
                    injectUserInfo(vo, userMap);
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 实体转视图对象
     */
    private MaterialVersionVo convertToVersionVo(MaterialHistoryEntity entity) {
        MaterialVersionVo vo = new MaterialVersionVo();
        vo.setInitiator(entity.getUpdateBy()); // 存储用户ID
        vo.setVersion(entity.getVersion());
        vo.setApprovalId(entity.getApprovalId());
        vo.setApprovalTime(entity.getApprovalTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    /**
     * 注入用户显示信息
     */
    private void injectUserInfo(MaterialVersionVo vo, Map<String, UserVo> userMap) {
        if (vo.getInitiator() == null) return;

        UserVo user = userMap.get(vo.getInitiator());
        if (user != null) {
            vo.setInitiator(user.getDisplayText()); // 替换为显示文本
        } else {
            log.info("User info not found, userId: {}", vo.getInitiator());
            vo.setInitiator("N/A"); // 设置默认值避免空指针
        }
    }
    /* Ended by AICoder, pid:t5aa70756fxf322142cd0bd210e54e60a107bbe5 */
    /* Started by AICoder, pid:n2a9162cd9vfbf0144df0892e006483e3fd80df7 */
    @Override
    public MaterialDetailVo queryMaterialDetails(String id) {
        //联合查询 物料信息
        MaterialVo materialVo = materialRepository.multiTableQueryById(id);
        if (materialVo == null) {
            log.error("Material id:{} corresponding material does not exist.",id);
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        //
        String userId = authService.getUserId();
        //TODO 待系统提供权限判断 是否展示成本及交等信息
        List<String> resourceIds = systemService.getEntityIdsByUserId(userId, GlobalConstants.ONE);
        if (!checkUserPermissions(userId,materialVo.getProductId(), resourceIds)) {
            log.info("user not permission to query material detail");
            materialVo.setCost(null);
            materialVo.setDeliveryDays(null);
        }
        //历史遗留问题，之前有的物料成本数据加密了有的未加密现在需要根据情况解密这个字符串
//        String cost = materialVo.getCost();
//        if (StringUtils.isNotBlank(cost)&&cost.length()>GlobalConstants.TWENTY) {
//            try {
//                // 尝试解密
//                String decryptedCost = CryptoUtil.decrypt(cost);
//                // 验证是否为有效数值
//                new BigDecimal(decryptedCost);
//                materialVo.setCost(decryptedCost);
//            } catch (Exception e) {
//                // 解密失败或结果无效，展示原值
//                log.info("Failed to decrypt cost, using original value. cost: {}");
//            }
//        }
        //枚举中英文转换展示名称
        materialVo.setMaterialStatus(materialVo.getMaterialStatus());
        materialVo.setMaterialStatusName(getMaterialStatusName(materialVo.getMaterialStatus()));
        materialVo.setPurchaseModeId(materialVo.getPurchaseMode());
        materialVo.setPurchaseMode(getPurchaseModeName(materialVo.getPurchaseMode()));
        //关联的文档
        List<DocumentInfoVo> documentInfoVos;
        //检查关联文档 临时表的关联尚未保存
        String[] documentIds = materialVo.getDocumentIds();
        log.info("documentIds:{}",documentIds);
        if (documentIds != null && documentIds.length > 0) {
            //表示临时表修改了 关联文档 需要展示更新后的文档
            documentInfoVos = documentService.queryByIds(Arrays.asList(documentIds));
            log.info("documentService queryByIds documentInfoVos:{}",documentInfoVos);
        } else {
            //展示 变更前的关联文档
            String materialId = materialVo.getId();
            documentInfoVos = documentService.queryByResourceId(materialId);
            log.info("documentService queryByResourceId documentInfoVos:{}",documentInfoVos);
        }
        MaterialDetailVo materialDetailVo = new MaterialDetailVo();
        materialDetailVo.setMaterial(materialVo);
        materialDetailVo.setDocumentInfoList(documentInfoVos);
        return materialDetailVo;
    }

    private String getPurchaseModeName(String purchaseMode) {
        PurchaseModeEnums byId = PurchaseModeEnums.getById(purchaseMode);
        if (byId != null){
            return I18nUtil.getI18nFromString(byId.getName());
        }
       return purchaseMode;
    }

    private String getMaterialStatusName(String materialStatus) {
        MaterialStatusEnums byId = MaterialStatusEnums.getById(materialStatus);
        if (byId != null) {
            return I18nUtil.getI18nFromString(byId.getName());
        }
        return materialStatus;
    }

    /* Started by AICoder, pid:y8f6b39dd8e12a2140e30acac03cf52d66117c84 */
    @Override
    public List<String> queryAllSalesStatus() {
        return pdmInfoRepository.queryAllSalesStatus();
    }

    @Override
    public PageVO<MaterialVo> pagingQueryByCondition(MaterialConditionQueryDto dto) {
        //默认分页参数
        Integer pageNum = GlobalConstants.ONE;
        Integer pageSize = GlobalConstants.TEN;
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            pageNum = dto.getPageNum();
            pageSize = dto.getPageSize();
        }
        List<String> groupIds = getGroupCondition(dto.getGroupId(),dto.getProductCategoryId());
        if (CollectionUtils.isEmpty(groupIds)) {
            return new PageVO<>();
        }
        dto.setGroupId(groupIds);
        PageHelper.startPage(pageNum,pageSize);
        List<MaterialVo> materialVos = materialRepository.queryMaterialByCondition(dto);
        PageInfo<MaterialVo> pageInfo = new PageInfo<>(materialVos);
        List<MaterialVo> list = pageInfo.getList();
        List<String> materialIds = list.stream().map(MaterialVo::getId).collect(Collectors.toList());
        Map<String,String> lectotypeStatusMap = getLectotypeStatusMap(materialIds);
        list.forEach(ma->{
            ma.setMaterialStatusName(getMaterialStatusName(ma.getMaterialStatus()));
            ma.setPurchaseMode(getPurchaseModeName(ma.getPurchaseMode()));
            String lectotypeStatus = lectotypeStatusMap.getOrDefault(ma.getId(), StringUtils.EMPTY);
            ma.setLectotypeStatus(lectotypeStatus);
        });

        return new PageVO<>(pageInfo.getTotal(), list);
    }

    @Override
    public List<MaterialVo> queryMaterialByCondition(MaterialConditionQueryDto dto) {
        //上架 下架 提交变更 查询全部
        if (OperateEnums.SUBMIT.getId().equals(dto.getOperation())) {
            //提交变更 只能对变更中的物料操作
            dto.setMaterialStatus(Collections.singletonList(MaterialStatusEnums.LISTING.getId()));
        } else if (OperateEnums.DE_LIST.getId().equals(dto.getOperation())) {
            //下架 只能对已上架的物料操作
            dto.setMaterialStatus(Collections.singletonList(MaterialStatusEnums.AVAILABLE.getId()));
        } else {
            //上架 查询已下架 或 草稿物料
            List<String> materialStatus = new ArrayList<>();
            materialStatus.add(MaterialStatusEnums.DRAFT.getId());
            materialStatus.add(MaterialStatusEnums.UNAVAILABLE.getId());
            dto.setMaterialStatus(materialStatus);
        }
        List<String> groupIds = getGroupCondition(dto.getGroupId(),dto.getProductCategoryId());
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        dto.setGroupId(groupIds);
        List<MaterialVo> materialVos = materialRepository.queryMaterialByCondition(dto);
        //log.info("materialIds materialVos:{}",JSON.toJSONString(materialVos));
        //TODO 选型单物料需要检查选型单状态 判断是否可以上架
        if (OperateEnums.LISTING.getId().equals(dto.getOperation())) {
            List<String> stringList = materialVos.stream().map(MaterialVo::getId).collect(Collectors.toList());
            List<LectotypeMaterialVo> lectotypeVos = materialRepository.querySelectionFormByMaterialIds(stringList);
            log.info("materialIds lectotypeVos:{}",JSON.toJSONString(lectotypeVos));
            if (CollectionUtils.isNotEmpty(lectotypeVos)) {
                Set<String> stringSet = new HashSet<>();
                for (LectotypeMaterialVo le:lectotypeVos ) {
                    if(LectotypeTypeEnums.ONE.type.equals(le.getLectotypeType())&&!LectotypeStatusEnums.FIVE.type.equals(le.getLectotypeStatus())){
                        stringSet.add(le.getMaterialId());
                    }
                }
                log.info("materialIds stringSet:{}",JSON.toJSONString(stringSet));
                //批量上架的物料中有部分选型单关联的物料 去除选型单状态不对的物料
               // Set<String> stringSet = lectotypeVos.stream().filter(p -> !LectotypeStatusEnums.FIVE.type.equals(p.getLectotypeStatus())).map(LectotypeMaterialVo::getMaterialId).collect(Collectors.toSet());
                materialVos = materialVos.stream().filter(p -> !stringSet.contains(p.getId())).collect(Collectors.toList());
            }
        }
        //log.info("materialIds lectotypeVos:{}",JSON.toJSONString(materialVos));
        return materialVos.stream()
                //.filter(p->StringUtils.isNotBlank(p.getPdmInfoId()))
                .peek(ma->{
                    ma.setMaterialStatusName(getMaterialStatusName(ma.getMaterialStatus()));
                    ma.setPurchaseMode(getPurchaseModeName(ma.getPurchaseMode()));
                    ma.setLectotypeStatus(StringUtils.EMPTY);
                }).collect(Collectors.toList());

    }
    /* Ended by AICoder, pid:n2a9162cd9vfbf0144df0892e006483e3fd80df7 */

    /* Started by AICoder, pid:m9caa0fda29c30e14b4e0abca00ade18c5093b2c */
    /**
     * 设置查询分组ID参数。
     *
     * @param groupIds   分组ID列表，用于指定需要查询的分组。
     * @param categoryId 产品类别ID，当未提供分组ID时，用于查询该类别下的所有分组。
     * @return 包含所有符合条件的分组ID的列表。
     * 逻辑说明：
     * - 如果 `groupIds` 为空或未提供，则查询当前类别下所有的分组。
     * - 如果 `groupIds` 不为空，则获取这些分组节点及其子节点的所有分组。
     */
    private List<String> getGroupCondition(List<String> groupIds, String categoryId) {
        if (CollectionUtils.isEmpty(groupIds)) {
            // 没传分组ID，查询当前小类下所有叶子节点分组
            return productGroupRepository.queryingLeafNodeGroups(categoryId);
        }
        // 分组不为空，获取当前分组节点下所有的分组
        return productGroupRepository.selectCurrentNodesAllChildNode(groupIds);
    }
    /* Ended by AICoder, pid:m9caa0fda29c30e14b4e0abca00ade18c5093b2c */
    /**
     * 检查用户是否有该资源的权限
     * @param userId 用户id
     * @param resourceId 资源id
     * @param resourceIds 用户有权限的资源id集
     * @return true-有权限，false->无权限
     */
    private boolean checkUserPermissions(String userId, String resourceId,List<String> resourceIds) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            log.info("The user:{} does not have permission for the current resource:{}",userId,resourceId);
            return false;
        }
        HashSet<String> hashSet = new HashSet<>(resourceIds);
        return hashSet.contains(resourceId);
    }
    /* Ended by AICoder, pid:y8f6b39dd8e12a2140e30acac03cf52d66117c84 */

    /* Started by AICoder, pid:b6398e6ddc1d9bb143630a4c805db25de0f53f2a */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSubmit(List<MaterialBatchAddDto> list) {
        String groupId = list.stream().map(MaterialBatchAddDto::getGroupId).distinct().findFirst().orElse(null);
        ProductGroupEntity groupEntity = productGroupRepository.queryProductGroupDetail(groupId);
        if(groupEntity!=null){
            permissionUtil.checkPermission(groupEntity.getProductCategoryId(), PermissionEnum.PRODUCT_MATERIAL_BATCH_SUBMIT);
        }
        //  区分pdm和非pdm的销售代码 使用Stream API的partitioningBy进行分区
        Map<Boolean, List<MaterialBatchAddDto>> partitioned = list.stream()
                .collect(Collectors.partitioningBy(
                        p -> StringUtils.isNotBlank(p.getSalesCode()) &&
                                SalesCodeUtils.isPdmSalesCode(p.getSalesCode())
                ));
        // 获取PDM销售代码和非PDM销售代码的材料批次添加DTO列表
        List<MaterialBatchAddDto> pdmList = partitioned.getOrDefault(true,Collections.emptyList());
        List<MaterialBatchAddDto> nonPdmList = partitioned.getOrDefault(false,Collections.emptyList());
        //todo 非18代码
        // 1. 根据销售ID列表查询PDM信息
        List<String> salesCodeList = pdmList.stream()
                .map(MaterialBatchAddDto::getSalesCode)
                .collect(Collectors.toList());

        //本地已保存的销售代码
        List<PdmInfoEntity> existPdmInfoList = getExistPdmInfo(salesCodeList);
        List<PdmInfoEntity> pdmInfoEntityList = queryPdmInfoBySalesCodeList(salesCodeList);
        if (CollectionUtils.isNotEmpty(existPdmInfoList)) {
            keepOriginalId(pdmInfoEntityList,existPdmInfoList);
        }
        //不再同步pdm的名称
        Map<String, PdmInfoEntity> pdmInfoEntityMap = pdmInfoEntityList.stream()
                .collect(Collectors.toMap(PdmInfoEntity::getSalesCode, pdmInfoEntity -> pdmInfoEntity));

        Map<String, PdmInfoEntity> idMap = pdmInfoEntityList.stream().collect(Collectors.
                toMap(PdmInfoEntity::getId, pdmInfoEntity -> pdmInfoEntity));
        //根据销售代码获取成本信息
        Map<String,String> costMap= getMaterialCostInfo(salesCodeList);
        // 3. 获取当前时间和用户ID
        String currentTime = DateTimeUtils.getCurrentTime();
        String userId = authService.getUserId();
        List<MaterialEntity> othDraftMaterialList = getOthDraftMaterial(nonPdmList, currentTime, userId);
        // 4. 构建物料实体列表
        List<MaterialEntity> materialEntityList = pdmList.stream()
                .map(dto -> {
                    PdmInfoEntity pdmInfoEntity = pdmInfoEntityMap.get(dto.getSalesCode());
                    if (pdmInfoEntity == null) {
                        log.error("Material sales code:{} corresponding PDM information does not exist.", dto.getSalesCode());
                        throw new BusinessException(StatusCode.DATA_NOT_FOUND);
                    }
                    MaterialEntity entity = new MaterialEntity();
                    // PDM同步内容变更
                    entity.setName(dto.getMaterialName());
                    entity.setId(UUID.randomUUID().toString());
                    entity.setMaterialStatus(MaterialStatusEnums.AVAILABLE_APPROVAL.getId());
                    entity.setPdmInfoId(pdmInfoEntity.getId());
                    entity.setGroupId(dto.getGroupId());
                    entity.setBrand(dto.getBrand());
                    entity.setSupplier(dto.getSupplier());
                    String modeEnumsId = getPurchaseModeEnumsId(dto.getPurchaseMode());
                    entity.setPurchaseMode(modeEnumsId);
                    String formatDateStr = getFormatDateStr(dto.getExpirationDate());
                    entity.setExpirationDate(formatDateStr);
                    entity.setWarrantyPeriod(dto.getWarrantyPeriod());
                    entity.setSpecificationModel(dto.getSpecificationModel());
                    entity.setService(dto.getService());
                    entity.setRecommendedLevel(dto.getRecommendedLevel());
                    entity.setUnit(dto.getUnit());
                    entity.setDescription(dto.getDescription());
                    entity.setCreateBy(userId);
                    entity.setUpdateBy(userId);
                    entity.setCreateTime(currentTime);
                    entity.setUpdateTime(currentTime);
                    //从pdm同步成本信息
                    entity.setCost(costMap.getOrDefault(dto.getSalesCode(),""));
                    return entity;
                })
                .collect(Collectors.toList());

        // 5. 获取审批ID
        String approvalId = batchApproval(materialEntityList,idMap, ApprovalTypeEnums.AVAILABLE_APPROVAL.getId(),null);
        materialEntityList.forEach(materialEntity -> materialEntity.setApprovalId(approvalId));

        // 6. 批量新增PDM信息和物料信息
        pdmInfoRepository.batchAdd(pdmInfoEntityList);
        materialEntityList.addAll(othDraftMaterialList);
        materialRepository.batchAdd(materialEntityList);
    }

    private void keepOriginalId(List<PdmInfoEntity> pdmInfoEntityList, List<PdmInfoEntity> existPdmInfoList) {
        Map<String, PdmInfoEntity> pdmInfoEntityMap = existPdmInfoList.stream().collect(Collectors.toMap(PdmInfoEntity::getSalesCode, Function.identity()));
        List<String> existPdmList = existPdmInfoList.stream().map(PdmInfoEntity::getId).collect(Collectors.toList());
        //PDM信息删除前需要保留原先存在的id
        //先删后增
        pdmInfoRepository.deletePdmInfoByIds(existPdmList);
        pdmInfoEntityList.forEach(pdmInfoEntity -> {
            PdmInfoEntity exist = pdmInfoEntityMap.get(pdmInfoEntity.getSalesCode());
            if (exist != null) {
                // 物料信息中存在，则更新ID 兼顾已有
                pdmInfoEntity.setId(exist.getId());
            }
        });
    }

    /* Started by AICoder, pid:o0a7f83c15ab76314d0a0933c1d1bf1d7a891184 */
    private String getPurchaseModeEnumsId( String purchaseMode) {
        PurchaseModeEnums modeEnums = PurchaseModeEnums.getByCnName(purchaseMode);
        //不是约定的采购模式
        if (modeEnums == null) {
            log.error("unknown purchase mode:{}",purchaseMode);
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        return modeEnums.getId();
    }

    /**
     * 批量导入时间格式转换 yyyy/MM/dd->yyyy-MM-dd
     * @param expirationDate 日期
     * @return 转换后的日期
     */
    private String getFormatDateStr( String expirationDate) {
        if (StringUtils.isBlank(expirationDate)) {
            return null;
        }
        LocalDate localDate = LocalDate.parse(expirationDate, DateTimeUtils.DATE_DAY_RIGHT_FORMATTER);
        if (localDate == null) {
            return null;
        }
        return localDate.format(DateTimeUtils.DATE_FORMATTER);
    }
    //非18销售代码只能保存为草稿
    private List<MaterialEntity> getOthDraftMaterial(List<MaterialBatchAddDto> list,String currentTime,String userId) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        //导入时已校验。提交不再校验
        //TODO 物料名称唯一（非18代码）
        //todo 销售代码填写则销售代码也唯一
        List<String> salesCodeList = new ArrayList<>();
        list.forEach(p->{
            if (StringUtils.isNotBlank(p.getSalesCode())) {
                salesCodeList.add(p.getSalesCode());
            }
        });
        List<MaterialEntity> materialEntityList = new ArrayList<>();
        List<OthInfoEntity> othSalesCodeEntityList = new ArrayList<>();
        //组装oth信息，物料信息
        Map<String, OthInfoEntity> salesCodeEntityMap = othSalesCodeRepository.queryOthSalesCodeBySalesCodeList(salesCodeList).stream().collect(Collectors.toMap(OthInfoEntity::getSalesCode, Function.identity(), (existing, replacement) -> existing));
        for (MaterialBatchAddDto materialBatchAddDto : list) {
            MaterialEntity entity = new MaterialEntity();
            OthInfoEntity othSalesCodeEntity = new OthInfoEntity();
            othSalesCodeEntity.setId(UUID.randomUUID().toString());
            String salesCode = materialBatchAddDto.getSalesCode();
            if (StringUtils.isNotBlank(salesCode)) {
                OthInfoEntity codeEntity = salesCodeEntityMap.get(salesCode);
                if (codeEntity != null) {
                    othSalesCodeEntity.setId(codeEntity.getId());
                }
            }
            othSalesCodeEntity.setName(materialBatchAddDto.getMaterialName());
            othSalesCodeEntity.setSalesCode(salesCode);
            othSalesCodeEntity.setSalesStatus(GlobalConstants.OTH_SALES_CODE);
            othSalesCodeEntity.setProductionCode(materialBatchAddDto.getProductionCode());
            othSalesCodeEntity.setUnit(materialBatchAddDto.getUnit());
            othSalesCodeEntity.setDescription(materialBatchAddDto.getDescription());
            othSalesCodeEntityList.add(othSalesCodeEntity);

            entity.setName(materialBatchAddDto.getMaterialName());
            entity.setId(UUID.randomUUID().toString());
            entity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());
            entity.setGroupId(materialBatchAddDto.getGroupId());
            entity.setBrand(materialBatchAddDto.getBrand());
            entity.setSupplier(materialBatchAddDto.getSupplier());
            entity.setOthInfoId(othSalesCodeEntity.getId());
            //TODO 时间格式转换及枚举转换
            String modeEnumsId = getPurchaseModeEnumsId(materialBatchAddDto.getPurchaseMode());
            entity.setPurchaseMode(modeEnumsId);
            String formatDateStr = getFormatDateStr(materialBatchAddDto.getExpirationDate());
            entity.setExpirationDate(formatDateStr);
            entity.setWarrantyPeriod(materialBatchAddDto.getWarrantyPeriod());
            entity.setSpecificationModel(materialBatchAddDto.getSpecificationModel());
            entity.setService(materialBatchAddDto.getService());
            entity.setRecommendedLevel(materialBatchAddDto.getRecommendedLevel());
            entity.setUnit(materialBatchAddDto.getUnit());
            entity.setDescription(materialBatchAddDto.getDescription());
            entity.setCreateBy(userId);
            entity.setUpdateBy(userId);
            entity.setCreateTime(currentTime);
            entity.setUpdateTime(currentTime);
            materialEntityList.add(entity);
        }
        List<String> stringList = salesCodeEntityMap.values().stream().map(OthInfoEntity::getId).collect(Collectors.toList());
        othSalesCodeRepository.batchDeleteByIds(stringList);
        othSalesCodeRepository.batchAdd(othSalesCodeEntityList);
        return materialEntityList;
    }
    /* Ended by AICoder, pid:o0a7f83c15ab76314d0a0933c1d1bf1d7a891184 */
    /* Started by AICoder, pid:37e43329edmf535140640ae5e0397c2b56292882 */
    private List<PdmInfoEntity> getExistPdmInfo(List<String> salesCodeList) {
        List<PdmInfoEntity> pdmInfoEntities =  pdmInfoRepository.queryPdmInfoBySalesCodeList(salesCodeList);
        if (CollectionUtils.isEmpty(pdmInfoEntities)) {
            return Collections.emptyList();
        }
        // 获取销售代码与物料的关联
        Map<String, String> relatedMaterials = getRelatedMaterials(
                pdmInfoEntities.stream().map(PdmInfoEntity::getSalesCode).collect(Collectors.toList())
        );

        // 检查是否关联物料
        pdmInfoEntities.forEach(pdmInfoEntity -> {
            String material = relatedMaterials.get(pdmInfoEntity.getSalesCode());
            if (StringUtils.isNotBlank(material)) {
                throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_UNIQUE);
            }
        });
        return pdmInfoEntities;
    }
    /* Ended by AICoder, pid:37e43329edmf535140640ae5e0397c2b56292882 */
    /* Started by AICoder, pid:fde9ae70c272775141f70872004ccf38d527ec37 */
    private List<PdmInfoEntity> queryPdmInfoBySalesCodeList(List<String> salesCodeList) {
        // 定义最终结果列表
        List<PdmInfoEntity> results = new ArrayList<>();
        // pdm支持每批次查询100条数据
        final int batchSize = 100;

        // 计算需要的批次数量
        int numBatches = (int) Math.ceil((double) salesCodeList.size() / batchSize);

        // 分批次查询PDM信息
        for (int i = 0; i < numBatches; i++) {
            // 计算当前批次的起始和结束索引
            int fromIndex = i * batchSize;
            int toIndex = Math.min(fromIndex + batchSize, salesCodeList.size());

            // 获取当前批次的ID子列表
            List<String> batchIds = salesCodeList.subList(fromIndex, toIndex);

            // 调用第三方接口查询当前批次的数据
            List<com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo> materialListByCode = pdmApiService.getMaterialListByCode(batchIds);
            log.debug("Sales code information queried from PDM：{}",materialListByCode);
            // 将当前批次的结果转换并添加到结果列表中
            for (com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo materialVo : materialListByCode) {
                PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
                pdmInfoEntity.setSalesCode(materialVo.getSalesCode());
                pdmInfoEntity.setName(materialVo.getPartCnName());
                pdmInfoEntity.setProductionCode(materialVo.getProductionCode());
                pdmInfoEntity.setUnit(materialVo.getUnit());
                pdmInfoEntity.setSalesStatus(materialVo.getSalesStatus());
                pdmInfoEntity.setDescription(materialVo.getDescription());
                pdmInfoEntity.setId(UUID.randomUUID().toString());
                results.add(pdmInfoEntity);
            }
        }

        // 返回最终的结果列表
        return results;
    }
    /* Ended by AICoder, pid:fde9ae70c272775141f70872004ccf38d527ec37 */

    /* Started by AICoder, pid:yce1862eb2zfd6e1403f0a2910742041ece77466 */
    private String batchApproval(List<MaterialEntity> materialEntityList,
                                 Map<String, PdmInfoEntity> pdmInfoEntityMap,
                                 String type, Integer applyOth) {
        if (CollectionUtils.isEmpty(materialEntityList)) {
            log.info("No sales code for PDM, no approval required");
            return null;
        }
        // 提取所有 groupId 并查询对应的 ProductGroupEntity
        Set<String> groupIds = materialEntityList.stream()
                .map(MaterialEntity::getGroupId)
                .collect(Collectors.toSet());

        List<ProductGroupEntity> productGroupEntities = productGroupRepository.selectByIds(new ArrayList<>(groupIds));
        log.debug("productGroupEntites===={}",productGroupEntities);
        Map<String, ProductGroupEntity> productGroupEntityMap = productGroupEntities.stream()
                .collect(Collectors.toMap(ProductGroupEntity::getId, Function.identity()));

        // 创建审批 DTO 并设置类型
        NewApprovalDto approvalDto = new NewApprovalDto();
        approvalDto.setType(Integer.parseInt(type));
        approvalDto.setApprovalOth(applyOth);
        approvalDto.setApproval18Code(GlobalConstants.ONE);
        // 转换 MaterialEntity 列表为 MaterialToApprovalDto 列表
        List<MaterialToApprovalDto> materials = materialEntityList.stream().map(entity -> {
            MaterialToApprovalDto dto = new MaterialToApprovalDto();
            dto.setId(entity.getId());
            dto.setName(entity.getName());
            dto.setPurchaseMode(entity.getPurchaseMode());
            dto.setExpirationDate(entity.getExpirationDate());
            dto.setWarrantyPeriod(entity.getWarrantyPeriod());
            dto.setGroupId(entity.getGroupId());

            // 设置销售代码（如果存在）
            PdmInfoEntity pdmInfoEntity = pdmInfoEntityMap.get(entity.getPdmInfoId());
            if (pdmInfoEntity != null && StringUtils.isNotBlank(pdmInfoEntity.getSalesCode())) {
                dto.setSalesCode(pdmInfoEntity.getSalesCode());
                dto.setProductCode(pdmInfoEntity.getProductionCode());
                dto.setSaleStatus(pdmInfoEntity.getSalesStatus());
                approvalDto.setApproval18Code(GlobalConstants.ZERO);
            }

            // 设置产品组名称和产品类别（如果存在）
            ProductGroupEntity productGroupEntity = productGroupEntityMap.get(entity.getGroupId());
            if (productGroupEntity != null) {
                dto.setGroupName(productGroupEntity.getName());
                approvalDto.setProductCategory(productGroupEntity.getProductCategoryId());
            }

            return dto;
        }).collect(Collectors.toList());

        approvalDto.setMaterials(materials);

        log.debug("create approval approvalDto===={}",approvalDto);
        // 创建并返回审批结果
        return processService.createApproval(approvalDto);
    }
    /* Ended by AICoder, pid:yce1862eb2zfd6e1403f0a2910742041ece77466 */

    @Override
    public List<DocumentCitedVo> selectByIds(List<String> ids) {
        return materialRepository.selectByIds(ids);
    }

    /* Ended by AICoder, pid:b6398e6ddc1d9bb143630a4c805db25de0f53f2a */

    private MaterialVersionVo getMaterialVersionVo(MaterialHistoryEntity historyEntity) {
        // 创建并填充版本视图对象
        MaterialVersionVo versionVo = new MaterialVersionVo();
        versionVo.setVersion(historyEntity.getVersion());
        versionVo.setInitiator(historyEntity.getSubmitter());
        versionVo.setApprovalId(historyEntity.getApprovalId());
        versionVo.setApprovalTime(historyEntity.getApprovalTime());
        return versionVo;
    }
    /* Ended by AICoder, pid:9114659d60zee3a14b960bfa703d68243fb6159c */
    /* Ended by AICoder, pid:q4fc4l316dmbc50147ff0919d019122af1897798 */

    /* Started by AICoder, pid:0444dv1c3c093941432a0a27700a617cb48613a2 */
    @Override
    public List<TemplateImportVo> importTemplate(FormDataBodyPart part,String categoryId) {
        ProductCategoryEntity productCategoryEntity = productCategoryRepository.queryById(categoryId);
        if (productCategoryEntity == null) {
            log.error("Product Category:{} does not exist.",categoryId);
            throw new BusinessException(ProductCategoryStatusCode.PRODUCT_CATEGORY_NOT_EXIST);
        }
        ProductGroupQueryDto queryDto = new ProductGroupQueryDto();
        queryDto.setProductCategoryId(categoryId);
        List<ProductGroupEntity> groupEntityList = productGroupRepository.queryPorductGroups(queryDto);
        //通过分组路径找到对应分组
        Map<String, ProductGroupEntity> groupEntityMap = groupEntityList.stream()
                .collect(Collectors.toMap(ProductGroupEntity::getPathName, Function.identity(), (existing, replacement) -> existing));
        //获取所有的父id，用于叶子节点判断
        Set<String> parentIds = groupEntityList.stream().map(ProductGroupEntity::getParentId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        String productName = productCategoryEntity.getProductName();
        MaterialReadListener materialReadListener = new MaterialReadListener(productName);
        try (InputStream inputStream = part.getValueAs(InputStream.class)) {
            EasyExcel.read(inputStream, materialReadListener)
                    .head(TemplateObj.class)
                    .charset(StandardCharsets.UTF_8)
                    .sheet()
                    .sheetName(GlobalConstants.MATERIAL_SHEET_NAME)
                    .doRead();
        } catch (Exception e) {
            log.error("Reading Excel file fail", e);
            throw new BusinessException(ProductCategoryStatusCode.READING_EXCEL_FAIL);
        }
        return checkAllData(materialReadListener,parentIds,groupEntityMap);
    }

    public List<TemplateImportVo> checkAllData(MaterialReadListener materialReadListener,Set<String> parentIds,Map<String, ProductGroupEntity> groupEntityMap) {
        List<TemplateImportVo> pdmSalesCodeList = materialReadListener.getPdmSalesCodeList();
        log.info("TemplateImportVo  successPdmList size========={}",pdmSalesCodeList.size());
        List<TemplateImportVo> othSalesCodeList = materialReadListener.getOthSalesCodeList();
        log.info("TemplateImportVo  othSalesCodeList size========={}",othSalesCodeList.size());
        if (pdmSalesCodeList.size() + othSalesCodeList.size() > GlobalConstants.MAX_IMPORT_NUMBER) {
            throw new BusinessException(ProductCategoryStatusCode.PARAM_NUM_EXCEED_LIMIT);
        }
        List<TemplateImportVo> allData = new ArrayList<>(pdmSalesCodeList.size() + othSalesCodeList.size());
        List<TemplateImportVo> importVoList = obtainCheckedData(pdmSalesCodeList, groupEntityMap,parentIds,
                materialReadListener.getPdmSalesCodes(),materialReadListener.getPdmRepeatMap());
        allData.addAll(importVoList);
        List<TemplateImportVo> templateImportVos = obtainOthCheckedData(othSalesCodeList,groupEntityMap,parentIds,
                materialReadListener.getOthSalesCodes(), materialReadListener.getSalesCodeRepeatMap(),
                materialReadListener.getNameRepeatMap(),materialReadListener.getOthNameList());
        allData.addAll(templateImportVos);
        //按照分组路径排序
        return allData;
    }

    /* Started by AICoder, pid:41715jc4bbaade614b98099d2132ba30f510196b */
    /**
     * 检查非18代码的数据
     * @param othSalesCodeList excel数据
     * @return 校验后的数据
     */
    public List<TemplateImportVo> obtainOthCheckedData(List<TemplateImportVo> othSalesCodeList,Map<String, ProductGroupEntity> groupEntityMap,
                                                       Set<String> parentIds, List<String> othSalesCodes,Map<String,List<TemplateImportVo>> salesCodeRepeatMap,
                                                       Map<String, List<TemplateImportVo>> nameRepeatMap, List<String> othNameList) {
        if (CollectionUtils.isEmpty(othSalesCodeList)) {
            return Collections.emptyList();
        }
        //所有非空的物料名称 通过名称查询的非pdm物料
        Map<String, MaterialEntity> othMaterialNameMap = getOthMaterialName(othNameList);
        //oth销售代码填写了，则销售代码唯一
        //非pdm代码物料名称是否重复
        Map<String, String> materialEntityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(othSalesCodes)) {
            List<OthInfoEntity> othInfoEntityList = othSalesCodeRepository.queryOthSalesCodeBySalesCodeList(othSalesCodes);
            if (CollectionUtils.isNotEmpty(othInfoEntityList)) {
                //非pdm的销售代码唯一
                Map<String, OthInfoEntity> othInfoEntityMap = othInfoEntityList.stream().collect(Collectors.toMap(OthInfoEntity::getId, Function.identity()));
                List<String> othIdList = othInfoEntityList.stream().map(OthInfoEntity::getId).collect(Collectors.toList());
                //非pdm销售代码关联的物料
                materialEntityMap = getOthMaterialByOthInfoIds(othIdList,othInfoEntityMap);
            }
        }
        Locale language = I18nUtil.getLanguage();
        Map<String, String> othSlesCodeRelatedMaterialMap = materialEntityMap;
        return othSalesCodeList.stream().parallel().unordered()
                .map(templateImportVo -> checkOthTemplateData(templateImportVo, othMaterialNameMap, othSlesCodeRelatedMaterialMap,
                        nameRepeatMap,salesCodeRepeatMap,groupEntityMap,parentIds, language)).collect(Collectors.toList());
    }

    private TemplateImportVo checkOthTemplateData(TemplateImportVo templateImportVo, Map<String, MaterialEntity> othMaterialNameMap, Map<String, String> othInfoIdRelatedMaterialMap,
                                                  Map<String, List<TemplateImportVo>> nameRepeatMap,Map<String,List<TemplateImportVo>> salesCodeRepeatMap,
                                                  Map<String, ProductGroupEntity> groupEntityMap, Set<String> parentIds, Locale language) {
        List<TemplateImportVo> templateImportVos = nameRepeatMap.get(templateImportVo.getMaterialName());
        if (templateImportVos != null && templateImportVos.size() > 1) {
            //物料名称重复
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.DUPLICATE_MATERIAL_NAME,language));
        }
        //检查非pdm销售代码 是否已使用
        checkOthSalesCode(templateImportVo,salesCodeRepeatMap,othInfoIdRelatedMaterialMap,language);
        //检查分组 同一小类下分组路径唯一
        checkGroupData(templateImportVo,groupEntityMap,parentIds,language);
        //检查物料名称是否已使用
        MaterialEntity materialEntity = othMaterialNameMap.get(templateImportVo.getMaterialName());
        if (materialEntity != null ) {
            //物料名称已使用
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.MATERIAL_NAME_BEEN_USED,language));
        }

        if (!templateImportVo.isFail()) {
            templateImportVo.setCheckResult(TemplateCheckResultEnum.PASS.getCode());
        }
        return templateImportVo;
    }

    private void checkOthSalesCode(TemplateImportVo templateImportVo , Map<String, List<TemplateImportVo>> salesCodeRepeatMap, Map<String, String> othInfoIdRelatedMaterialMap, Locale language) {
        String salesCode = templateImportVo.getSalesCode();
        if (StringUtils.isNotBlank(salesCode)) {
            List<TemplateImportVo> importVos = salesCodeRepeatMap.get(salesCode);
            if (importVos != null && importVos.size() > 1) {
                //销售代码重复
                setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                        I18nUtil.getI18n(GlobalConstants.SALES_CODE_REPEAT,language));
            }
            String materialName = othInfoIdRelatedMaterialMap.get(salesCode);
            if (StringUtils.isNotBlank(materialName)) {
                String[] args = new String[] {materialName};
                setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                        I18nUtil.getI18nWithArgs(GlobalConstants.SALES_CODE_USED,args,language));
            }
        }
    }


    private Map<String,MaterialEntity> getOthMaterialName(List<String> nameList) {
        //非pdm销售代码
        List<MaterialEntity> materialEntityList = materialRepository.queryMaterialByOthName(nameList,MaterialStatusEnums.DRAFT.getId());
        return materialEntityList.stream().collect(Collectors.toMap(MaterialEntity::getName,Function.identity()));
    }

    private Map<String,String> getOthMaterialByOthInfoIds(List<String> othIdList,Map<String, OthInfoEntity> othInfoEntityMap) {
        Map<String,String> entityMap = new HashMap<>();
        //仅查主表
        List<MaterialEntity> materialEntities = materialRepository.batchQueryByOthIds(othIdList,MaterialStatusEnums.DRAFT.getId());
        if (CollectionUtils.isEmpty(materialEntities)) {
            return entityMap;
        }
        materialEntities.forEach(p->{
            OthInfoEntity othInfoEntity = othInfoEntityMap.get(p.getOthInfoId());
            if (othInfoEntity != null) {
                entityMap.put(othInfoEntity.getSalesCode(),p.getName());
            }
        });
        return entityMap;
    }
    /* Ended by AICoder, pid:41715jc4bbaade614b98099d2132ba30f510196b */
    /**
     * 对18开头的pdm销售代码进行校验
     * @param pdmSalesCodeList pdm的
     * @param groupEntityMap 分组数据
     * @return 校验后的数据
     */
    public List<TemplateImportVo> obtainCheckedData(List<TemplateImportVo> pdmSalesCodeList, Map<String, ProductGroupEntity> groupEntityMap, Set<String> parentIds,
                                                     List<String> pdmSalesCodes, Map<String, List<TemplateImportVo>> pdmRepeatMap) {

        //pdm的为空不需要处理
        if (CollectionUtils.isEmpty(pdmSalesCodeList)) {
            return Collections.emptyList();
        }
        log.info("TemplateImportVo  successPdmList size========={}",pdmSalesCodeList.size());
        log.debug("TemplateImportVo  repeatMap========={}",pdmRepeatMap);
        //TODO pdm销售代码不再唯一，与品牌联合唯一；
        Map<String, String> relatedMaterials = getRelatedMaterials(pdmSalesCodes);
        log.debug("TemplateImportVo  relatedMaterials========={}",relatedMaterials);
        //从PDM系统查询的pdm信息
        Map<String, PdmInfoEntity> pdmInfoEntityMap = queryPdmInfoBySalesCodeList(pdmSalesCodes).stream()
                .collect(Collectors.toMap(PdmInfoEntity::getSalesCode, Function.identity(), (existing, replacement) -> existing));
        //路径唯一
        log.debug("TemplateImportVo  groupEntityMap========={}",groupEntityMap);
        Locale language = I18nUtil.getLanguage();
        //返回全部的数据
        return pdmSalesCodeList.stream().parallel().unordered()
                .map(templateImportVo -> checkTemplateData(templateImportVo, relatedMaterials, pdmInfoEntityMap,
                        groupEntityMap, pdmRepeatMap,parentIds, language)).collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:0444dv1c3c093941432a0a27700a617cb48613a2 */
    /* Started by AICoder, pid:pdb26wddb431ddf1404009fb708a2c7fed2744ac */
    private TemplateImportVo checkTemplateData(TemplateImportVo templateImportVo, Map<String, String> relatedMaterials,
                                               Map<String, PdmInfoEntity> pdmInfoEntityMap, Map<String, ProductGroupEntity> groupEntityMap,
                                               Map<String,List<TemplateImportVo>> repeatMap, Set<String> parentIds, Locale language) {
        checkGroupData(templateImportVo,groupEntityMap,parentIds,language);
        checkPdmSalesCode(templateImportVo,relatedMaterials,pdmInfoEntityMap,repeatMap,language);
        if (!templateImportVo.isFail()) {
            templateImportVo.setCheckResult(TemplateCheckResultEnum.PASS.getCode());
        }
        return templateImportVo;
    }

    private void checkGroupData(TemplateImportVo templateImportVo, Map<String, ProductGroupEntity> groupEntityMap, Set<String> parentIds, Locale language) {
        String groupPathName = templateImportVo.getGroupPathName();
        String productName = templateImportVo.getProductName();
        ProductGroupEntity productGroupEntity = groupEntityMap.get(groupPathName);
        if (productGroupEntity == null) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18nWithArgs(GlobalConstants.GROUP_NOT_EXIST,new String[]{productName,groupPathName},language));
            return;
        }
        if (parentIds.contains(productGroupEntity.getId())) {
            //当前分组不是叶子节点不能挂载物料
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.NON_LEAF_NODE_GROUP,language));
        }
        templateImportVo.setGroupId(productGroupEntity.getId());
    }

    private void checkPdmSalesCode(TemplateImportVo templateImportVo, Map<String, String> relatedMaterials,
                                   Map<String, PdmInfoEntity> pdmInfoEntityMap,
                                   Map<String, List<TemplateImportVo>> repeatMap, Locale language) {
        String salesCode = templateImportVo.getSalesCode();
        PdmInfoEntity pdmInfoEntity = pdmInfoEntityMap.get(salesCode);
        if (pdmInfoEntity == null) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.SALES_CODE_NOT_EXIST,language));
        }
        //唯一键
        String unique = salesCode + templateImportVo.getBrand();
        List<TemplateImportVo> templateObjList = repeatMap.get(unique);
        if (templateObjList != null && templateObjList.size() > 1) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.PDM_MATERIAL_REPEAT,language));
        }
        //校验销售代码是否已使用
        String materialName = relatedMaterials.get(unique);
        if (StringUtils.isNotBlank(materialName)) {
            String[] args = new String[] {materialName};
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18nWithArgs(GlobalConstants.SALES_CODE_USED,args,language));
        }
    }
    /* Ended by AICoder, pid:pdb26wddb431ddf1404009fb708a2c7fed2744ac */
    /* Started by AICoder, pid:r6f79g89a5yd1b314709081ca0ba95570fa35041 */

    /**
     * 销售代码关联的物料
     * @param salesCodes
     * @return key：销售代码+品牌，value:物料名称
     */
    private Map<String,String> getRelatedMaterials(List<String> salesCodes) {
        Map<String,String> relationMap = new HashMap<>();
        if (CollectionUtils.isEmpty(salesCodes)) {
            return relationMap;
        }
        List<PdmInfoEntity> pdmInfoEntities = pdmInfoRepository.queryPdmInfoBySalesCodeList(salesCodes);
        if (CollectionUtils.isEmpty(pdmInfoEntities)) {
            return relationMap;
        }
        //本地已记录销售代码，需要校验是否已使用
        Map<String, PdmInfoEntity> pdmInfoEntityMap = pdmInfoEntities.stream()
                .collect(Collectors.toMap(PdmInfoEntity::getId, Function.identity(), (existing, replacement) -> existing));
        List<String> pdmInfoIds = pdmInfoEntities.stream().map(PdmInfoEntity::getId).collect(Collectors.toList());
        //主表引用 临时表引用
        List<MaterialEntity> materialEntities = materialRepository.batchQueryByPdmInfoIds(pdmInfoIds);
        if (CollectionUtils.isNotEmpty(materialEntities)) {
            //主表存在已使用销售代码
            materialEntities.forEach(p->{
                PdmInfoEntity pdmInfoEntity = pdmInfoEntityMap.get(p.getPdmInfoId());
                relationMap.put(pdmInfoEntity.getSalesCode() + p.getBrand(),p.getName());
            });
        }
        List<MaterialTemporaryEntity> temporaryEntities = materialTemporaryRepository.batchQueryByPdmInfoIds(pdmInfoIds);
        if (CollectionUtils.isNotEmpty(temporaryEntities)) {
            //临时表存在已使用销售代码
            temporaryEntities.forEach(p->{
                PdmInfoEntity pdmInfoEntity = pdmInfoEntityMap.get(p.getPdmInfoId());
                relationMap.put(pdmInfoEntity.getSalesCode() + p.getBrand(),p.getName());
            });
        }
        return relationMap;
    }
    /* Ended by AICoder, pid:r6f79g89a5yd1b314709081ca0ba95570fa35041 */
    /* Started by AICoder, pid:542c791f53uac8a14b8f087330282b277971b8fa */
    /**
     * 物料模糊查询
     * @param materialFuzzyDto
     * @return
     */
    @Override
    public List<MaterialFuzzyVo> queryMaterialFuzzy(MaterialFuzzyDto materialFuzzyDto) {
        return materialRepository.selectMaterialFuzzy(materialFuzzyDto);
    }


    /**
     * 物料精确查询
     * @param materialAccurateDto
     * @return
     */
    @Override
    public List<MaterialFuzzyVo> queryMaterialAccurate(MaterialAccurateDto materialAccurateDto) {
        return materialRepository.selectMaterialAccurate(materialAccurateDto);
    }

    @Override
    public boolean checkUnique(String id,String name) {
        int count = materialRepository.selectByIdAndName(id, name);
        if (count == 0)
        {
            return true;
        }
        return false;
    }
    /* Ended by AICoder, pid:542c791f53uac8a14b8f087330282b277971b8fa */


    private void setCheckResultAndErrorReason(TemplateImportVo templateImportVo, String messageKey, String errorReason) {
        templateImportVo.setCheckResult(messageKey);
        String existingErrorReason = StringUtils.defaultIfBlank(templateImportVo.getErrorReason(), "");
        String separator = existingErrorReason.isEmpty() ? "" : GlobalConstants.SEMICOLON;
        String newReason = existingErrorReason + separator + errorReason;
        templateImportVo.setErrorReason(newReason);
    }
    /* Ended by AICoder, pid:8ed15cc9b2d8e59146780bfba03d485b6d496932 */

    /* Started by AICoder, pid:e0a507d02fmdc02146cb0ba0e010ed396f00520e */
    private void temporarilyStore(String materialStatus, MaterialEntity entity, MaterialEditDto editDto, String pdmInfoId, String othInfoId, String userId, String currentTime) {
        MaterialStatusEnums status = MaterialStatusEnums.getById(materialStatus);
        //非pdm的物料仅限草稿
//        if (StringUtils.isNotBlank(othInfoId) && !MaterialStatusEnums.DRAFT.getId().equals(materialStatus)) {
//            log.error("Only materials in draft status can be associated with non-PDM sales codes");
//            throw new BusinessException(ProductCategoryStatusCode.DRAFT_MATERIAL_ONLY);
//        }

        switch (status) {
            case DRAFT:
                BeanUtils.copyProperties(editDto, entity);
                entity.setPdmInfoId(pdmInfoId);
                entity.setDescription(editDto.getDescription());
                entity.setUpdateBy(userId);
                entity.setUpdateTime(currentTime);
                //空值设置
                entity.setOthInfoId(othInfoId);
                materialRepository.updateDraftMaterialRelation(entity.getId(),pdmInfoId,othInfoId);
                materialRepository.editMaterial(entity);
                // 关联文档
                relateDocuments(editDto.getId(), editDto.getDocumentIds());
                break;

            case LISTING:
            case UNAVAILABLE:
                if(StringUtils.isNotBlank(pdmInfoId)) {
                    createAndSaveTempEntity(editDto, pdmInfoId, entity.getApprovalId(), userId, currentTime, entity);
                }else {
                    BeanUtils.copyProperties(editDto, entity);
                    entity.setPdmInfoId(pdmInfoId);
                    entity.setDescription(editDto.getDescription());
                    entity.setUpdateBy(userId);
                    entity.setUpdateTime(currentTime);
                    //空值设置
                    entity.setOthInfoId(othInfoId);
                    materialRepository.updateDraftMaterialRelation(entity.getId(),pdmInfoId,othInfoId);
                    materialRepository.editMaterial(entity);
                }
                break;

            case AVAILABLE:
                // 更新正式表
                updateMainEntity(entity, entity.getApprovalId(), MaterialStatusEnums.LISTING.getId(), userId, currentTime);
                // 存入临时表
//                if(StringUtils.isNotBlank(pdmInfoId)) {
                    createAndSaveTempEntity(editDto, pdmInfoId, entity.getApprovalId(), userId, currentTime, entity);
//                }
                break;

            default:
                throw new BusinessException(ProductCategoryStatusCode.PARENT_PRODUCT_ERROR);
        }
    }
    /* Ended by AICoder, pid:e0a507d02fmdc02146cb0ba0e010ed396f00520e */

    private void submit(String materialStatus,MaterialEntity entity,MaterialEditDto editDto, String pdmInfoId,String userId,String currentTime)
    {
        ////针对已上架物料，仅变更文档信息 不再审批 only document properties are changed
        if (MaterialStatusEnums.AVAILABLE.getId().equals(materialStatus)) {
            boolean materialsEqual = isMaterialsEqual(editDto, entity);
            if (materialsEqual && pdmInfoId.equals(entity.getPdmInfoId())) {
                relateDocuments(editDto.getId(),editDto.getDocumentIds());
                return;
            }
        }
        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setSalesCode(editDto.getSalesCode());
        pdmInfoEntity.setProductionCode(editDto.getProductionCode());
        pdmInfoEntity.setSalesStatus(editDto.getSalesStatus());
        if (MaterialStatusEnums.DRAFT.getId().equals(materialStatus))
        {
            //T选型单的额物料提交有限制
            checkSelectionFormStatus(entity.getId());
            //更新正式表
            String approvalId = getApprovalId(entity.getId(),entity,pdmInfoEntity, ApprovalTypeEnums.AVAILABLE_APPROVAL.getId(),editDto.getApplyOth());
            BeanUtils.copyProperties(editDto,entity);
            entity.setPdmInfoId(pdmInfoId);
            //entity.setOthInfoId(null);
            entity.setDescription(editDto.getDescription());
            //更新关联附属信息
            materialRepository.updateDraftMaterialRelation(entity.getId(),pdmInfoId,null);
            updateMainEntity(entity,approvalId,MaterialStatusEnums.AVAILABLE_APPROVAL.getId(),userId,currentTime);
            //关联文档
            relateDocuments(editDto.getId(),editDto.getDocumentIds());
        }
        else if (MaterialStatusEnums.LISTING.getId().equals(materialStatus) ||MaterialStatusEnums.AVAILABLE.getId().equals(materialStatus)){
            BeanUtils.copyProperties(editDto,entity);
            entity.setPdmInfoId(pdmInfoId);
            //主表改状态为变更审批中(从pdm获取成本信息)
            setEntityPdmCost(pdmInfoId,entity);
            String approvalId = getApprovalId(entity.getId(),entity,pdmInfoEntity, ApprovalTypeEnums.CHANGE_APPROVAL.getId(),null);
            updateMainEntity(entity,approvalId,MaterialStatusEnums.CHANGE_APPROVAL.getId(),userId,currentTime);
            //存入临时表
            createAndSaveTempEntity(editDto,pdmInfoId,approvalId,userId,currentTime,entity);
        }
        else if (MaterialStatusEnums.UNAVAILABLE.getId().equals(materialStatus)) {
            BeanUtils.copyProperties(editDto,entity);
            entity.setPdmInfoId(pdmInfoId);
            //主表改状态为上架审批中
            String approvalId = getApprovalId(entity.getId(),entity,pdmInfoEntity, ApprovalTypeEnums.AVAILABLE_APPROVAL.getId(),editDto.getApplyOth());
            updateMainEntity(entity,approvalId,MaterialStatusEnums.AVAILABLE_APPROVAL.getId(),userId,currentTime);
            //存入临时表
            createAndSaveTempEntity(editDto,pdmInfoId,approvalId,userId,currentTime,entity);
        }
        else{
            throw new BusinessException(ProductCategoryStatusCode.PARENT_PRODUCT_ERROR);
        }
    }

    private void setEntityPdmCost(String pdmInfoId, MaterialEntity entity) {
        String cost=null;
        PdmInfoEntity pdmInfoEntityCost = pdmInfoRepository.queryById(pdmInfoId);
        if (pdmInfoEntityCost!=null){
            Map<String, String> materialCostInfo = getMaterialCostInfo(Collections.singletonList(pdmInfoEntityCost.getSalesCode()));
            cost=materialCostInfo.getOrDefault(pdmInfoEntityCost.getSalesCode(), null);
        }
        if (cost!=null){
            entity.setCost(cost);
        }
    }
    /**
     * 检查选型单状态
     */
    private void checkSelectionFormStatus(String materialId) {
        DemandManagementLectotypeVo lectotypeById = demandMapper.getLectotypeByMaterialId(materialId);
        // 如果没有找到对应的选型单或选型单类型不是指定的类型，则直接返回
        if (lectotypeById == null || !LectotypeTypeEnums.ONE.type.equals(lectotypeById.getLectotypeType())) {
            return;
        }
        //指定的不限制，招标的限制 非开标、上架中、已上架状态的选型单创建物料不能提交
        if (!LectotypeStatusEnums.FIVE.type.equals(lectotypeById.getLectotypeStatus())
                && !LectotypeStatusEnums.SIX.type.equals(lectotypeById.getLectotypeStatus())
                && !LectotypeStatusEnums.SEVEN.type.equals(lectotypeById.getLectotypeStatus())) {
            //非开标状态的选型单创建物料不能提交
            log.error("Materials on the selection form that are not in the bid opening state cannot be submitted");
            throw new BusinessException(ProductCategoryStatusCode.SELECTION_FORM_MATERIAL_SHELF_LIMIT);
        }
    }
    /* Started by AICoder, pid:i053cc2fc3v649f142cb084eb03a624be9031da8 */
    public static boolean isMaterialsEqual(MaterialEditDto dto, MaterialEntity entity) {
        // Compare String fields using a helper method
        if (!compareStringFields(dto, entity)) {
            return false;
        }
        if (!compareStringFieldsTwo(dto, entity)) {
            return false;
        }
        return true;
    }

    private static boolean compareStringFields(MaterialEditDto dto, MaterialEntity entity) {
        return safeEquals(dto.getName(), entity.getName()) &&
                safeEquals(dto.getBrand(), entity.getBrand()) &&
                safeEquals(dto.getSupplier(), entity.getSupplier()) &&
                safeEquals(dto.getPurchaseMode(), entity.getPurchaseMode()) &&
                safeEquals(dto.getExpirationDate(), entity.getExpirationDate()) &&
                safeEquals(dto.getWarrantyPeriod(), entity.getWarrantyPeriod());
    }
    private static boolean compareStringFieldsTwo(MaterialEditDto dto, MaterialEntity entity) {
        return safeEquals(dto.getGroupId(), entity.getGroupId()) &&
                safeEquals(dto.getSpecificationModel(), entity.getSpecificationModel()) &&
                safeEquals(dto.getService(), entity.getService()) &&
                safeEquals(dto.getRecommendedLevel(), entity.getRecommendedLevel()) &&
                safeEquals(dto.getDescription(), entity.getDescription()) &&
                safeEquals(dto.getUnit(), entity.getUnit());
    }

    private static boolean safeEquals(String o1, String o2) {
        // Handle case where both objects are null or empty
        if (StringUtils.isAllBlank(o1,o2)) {
            return true;
        }
        // If one is null, but not both, they're not equal
        if (o1 == null || o2 == null) {
            return false;
        }
        // Otherwise, use equals()
        return o1.equals(o2);
    }
    /* Ended by AICoder, pid:i053cc2fc3v649f142cb084eb03a624be9031da8 */

    /* Started by AICoder, pid:k736bgff42p87a81416e0970b0418f2d21f68bbf */
    /**
     * 创建并保存临时实体。
     */
    private void createAndSaveTempEntity(MaterialEditDto dto, String pdmInfoId, String approvalId, String userId, String currentTime,MaterialEntity entity) {
        //从pdm获取成本信息
        String cost=null;
        PdmInfoEntity pdmInfoEntity = pdmInfoRepository.queryById(pdmInfoId);
        if (pdmInfoEntity!=null){
            Map<String, String> materialCostInfo = getMaterialCostInfo(Collections.singletonList(pdmInfoEntity.getSalesCode()));
            cost=materialCostInfo.getOrDefault(pdmInfoEntity.getSalesCode(), null);
        }

        MaterialTemporaryEntity temporaryEntity = materialTemporaryRepository.selectByMaterialId(dto.getId());
        if (temporaryEntity == null)
        {
            temporaryEntity = new MaterialTemporaryEntity();
            BeanUtils.copyProperties(dto, temporaryEntity);
            temporaryEntity.setId(UUID.randomUUID().toString());
            if (cost!=null){
                temporaryEntity.setCost(cost);
            }
            temporaryEntity.setMaterialId(dto.getId());
            temporaryEntity.setPdmInfoId(pdmInfoId);
            temporaryEntity.setDocumentIds(getDocumentIds(dto.getDocumentIds()));
            temporaryEntity.setUpdateBy(userId);
            temporaryEntity.setUpdateTime(currentTime);
            temporaryEntity.setApprovalId(approvalId);
            //新增字段
            temporaryEntity.setDescription(dto.getDescription());
            //补充版本等信息
            temporaryEntity.setVersion(entity.getVersion());
            temporaryEntity.setCreateBy(entity.getCreateBy());
            temporaryEntity.setCreateTime(entity.getCreateTime());
            temporaryEntity.setNameEn(entity.getNameEn());
            temporaryEntity.setUnitEn(entity.getUnitEn());
            materialTemporaryRepository.addTempMaterials(temporaryEntity);
        }
        else
        {
            //编辑
            if (cost!=null){
                temporaryEntity.setCost(cost);
            }
            temporaryEntity.setName(dto.getName());
            temporaryEntity.setBrand(dto.getBrand());
            temporaryEntity.setSupplier(dto.getSupplier());
            temporaryEntity.setPurchaseMode(dto.getPurchaseMode());
            temporaryEntity.setExpirationDate(dto.getExpirationDate());
            temporaryEntity.setWarrantyPeriod(dto.getWarrantyPeriod());
            temporaryEntity.setGroupId(dto.getGroupId());
            temporaryEntity.setPdmInfoId(pdmInfoId);
            temporaryEntity.setDocumentIds(getDocumentIds(dto.getDocumentIds()));
            temporaryEntity.setUpdateBy(userId);
            temporaryEntity.setUpdateTime(currentTime);
            temporaryEntity.setApprovalId(approvalId);
            //新增字段
            temporaryEntity.setDescription(dto.getDescription());
            temporaryEntity.setSpecificationModel(dto.getSpecificationModel());
            temporaryEntity.setUnit(dto.getUnit());
            temporaryEntity.setService(dto.getService());
            temporaryEntity.setRecommendedLevel(dto.getRecommendedLevel());
            materialTemporaryRepository.editTempMaterials(temporaryEntity);
        }
    }

    private String[] getDocumentIds(List<String> documentIds) {
        if (CollectionUtils.isNotEmpty(documentIds)) {
             return documentIds.toArray(new String[0]);
        }
        return null;
    }
    /**
     * 更新主实体。
     */
    private void updateMainEntity(MaterialEntity entity, String approvalId, String status, String userId, String currentTime) {
        entity.setUpdateBy(userId);
        entity.setUpdateTime(currentTime);
        entity.setApprovalId(approvalId);
        entity.setMaterialStatus(status);
        materialRepository.editMaterial(entity);
    }
    /* Ended by AICoder, pid:k736bgff42p87a81416e0970b0418f2d21f68bbf */

    private String updatePdmInfo(MaterialEditDto editDto) {
        String pdmInfoId;
        PdmInfoEntity entity = pdmInfoRepository.queryBySalesCode(editDto.getSalesCode());
        if (entity == null)
        {
            //新增
            pdmInfoId = UUID.randomUUID().toString();
            entity = new PdmInfoEntity();
            entity.setId(pdmInfoId);
            entity.setName(editDto.getName());
            entity.setSalesCode(editDto.getSalesCode());
            entity.setSalesStatus(editDto.getSalesStatus());
            entity.setProductionCode(editDto.getProductionCode());
            entity.setUnit(editDto.getUnit());
            entity.setDescription(editDto.getDescription());
            pdmInfoRepository.addPdmInfo(entity);
        }
        else {
            //编辑
            pdmInfoId = entity.getId();
            entity.setName(editDto.getName());
            entity.setSalesCode(editDto.getSalesCode());
            entity.setSalesStatus(editDto.getSalesStatus());
            entity.setProductionCode(editDto.getProductionCode());
            entity.setUnit(editDto.getUnit());
            entity.setDescription(editDto.getDescription());
            pdmInfoRepository.editPdmInfo(entity);
        }
        return pdmInfoId;
    }

    /* Started by AICoder, pid:w831feb9bade70a140fe0be5e08b5427ca32d226 */
    @Override
    public boolean updateMaterialByGroupId(String oldGroupId, String newGroupId) {
        // 查询物料主表数据
        List<MaterialEntity> entity = materialRepository.queryByGroupId(oldGroupId);
        if (CollectionUtils.isNotEmpty(entity)) {
            entity.forEach(item -> item.setGroupId(newGroupId));
            materialRepository.updateBatchMaterials(entity);
        }
        // 查询历史物料表
        List<MaterialHistoryEntity> materialHistoryEntities = materialHistoryRepository.queryByGroupId(oldGroupId);
        if (CollectionUtils.isNotEmpty(materialHistoryEntities)) {
            materialHistoryEntities.forEach(item -> item.setGroupId(newGroupId));
            materialHistoryRepository.updateBatchMaterials(materialHistoryEntities);
        }
        // 查询临时物料表
        List<MaterialTemporaryEntity> materialTempEntities = materialTemporaryRepository.queryByGroupId(oldGroupId);
        if (CollectionUtils.isNotEmpty(materialTempEntities)) {
            materialTempEntities.forEach(item -> item.setGroupId(newGroupId));
            materialTemporaryRepository.updateBatchMaterials(materialTempEntities);
        }
        return true;
    }

    /* Started by AICoder, pid:8c3d2d0fb0udd7314c900a8b60747d5eada718bb */
    @Override
    public void uniqueCheckForPdm(String salesCode, String brand, String materialId) {
        //检查主表是否已使用
        PdmInfoEntity pdmInfoEntity = pdmInfoRepository.queryBySalesCode(salesCode);
        if (pdmInfoEntity == null) {
            return;
        }
        String id = pdmInfoEntity.getId();
        List<MaterialEntity> materialEntities = materialRepository.queryByPdmInfoIdAndBrand(id,brand,materialId);
        if (CollectionUtils.isNotEmpty(materialEntities)) {
            //已使用
            throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_UNIQUE);
        }
        //检查临时表是否已使用
        List<MaterialTemporaryEntity> temporaryEntities = materialTemporaryRepository.queryByPdmInfoIdAndBrand(id,brand,materialId);
        if (CollectionUtils.isNotEmpty(temporaryEntities)) {
            throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_UNIQUE);
        }
    }
    @Override
    public Boolean uniqueCheckForPdm1(String salesCode, String brand, String materialId) {
        //检查主表是否已使用
        PdmInfoEntity pdmInfoEntity = pdmInfoRepository.queryBySalesCode(salesCode);
        if (pdmInfoEntity == null) {
            return true;
        }
        String id = pdmInfoEntity.getId();
        List<MaterialEntity> materialEntities = materialRepository.queryByPdmInfoIdAndBrand(id,brand,materialId);
        if (CollectionUtils.isNotEmpty(materialEntities)) {
            //已使用
            return false;
        }
        //检查临时表是否已使用
        List<MaterialTemporaryEntity> temporaryEntities = materialTemporaryRepository.queryByPdmInfoIdAndBrand(id,brand,materialId);
        if (CollectionUtils.isNotEmpty(temporaryEntities)) {
            return false;
        }
        return true;
    }
    @Override
    public Boolean nonPdmNameUniqueCheck1(String name, String salesCode,String id) {
        //非PDM物料全是草稿
        boolean exist = materialRepository.checkNonPdmMaterialName(name,id);
        if (exist) {
            return false;
        }
        if (StringUtils.isBlank(salesCode)) {
            return true;
        }
        OthInfoEntity othInfoEntity = othSalesCodeRepository.queryBySalesCode(salesCode);
        if (othInfoEntity != null ) {
            //oth销售代码已存在，检查是否重复
            boolean exist1 = materialRepository.queryMaterialByOthId(othInfoEntity.getId(),id);
            if (exist1) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void nonPdmNameUniqueCheck(String name, String salesCode,String id) {
        //非PDM物料全是草稿
        boolean exist = materialRepository.checkNonPdmMaterialName(name,id);
        if (exist) {
            throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_UNIQUE);
        }
        nonPdmSalesCodeUniqueCheck(salesCode,id);
    }

    private void nonPdmSalesCodeUniqueCheck(String salesCode, String id) {
        //非空时 销售代码唯一
        if (StringUtils.isBlank(salesCode)) {
            return;
        }
        OthInfoEntity othInfoEntity = othSalesCodeRepository.queryBySalesCode(salesCode);
        if (othInfoEntity != null ) {
            //oth销售代码已存在，检查是否重复
            boolean exist = materialRepository.queryMaterialByOthId(othInfoEntity.getId(),id);
            if (exist) {
                throw new BusinessException(ProductCategoryStatusCode.MATERIAL_NOT_UNIQUE);
            }
        }
    }
    @Override
    public String addOrUpdateOthInfo(MaterialAddDto addDto) {
        //物料名称唯一
        OthInfoEntity othInfoEntity = Optional.ofNullable(othSalesCodeRepository.queryOthByName(addDto.getName()))
                .orElse(new OthInfoEntity());
        setCommonOthInfo(othInfoEntity,addDto);
        // 如果是新实体，则设置 ID 并保存为新增记录；否则更新现有记录
        if (StringUtils.isBlank(othInfoEntity.getId())) {
            String pdmId = UUID.randomUUID().toString();
            othInfoEntity.setId(pdmId);
            othSalesCodeRepository.addOthInfo(othInfoEntity);
            return pdmId;
        } else {
            othSalesCodeRepository.editOthInfo(othInfoEntity);
            return othInfoEntity.getId();
        }
    }

    /* Started by AICoder, pid:ic242caf6eu3bdc1442208e98135b101f3820369 */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void specialBatchSubmit(List<MaterialBatchAddDto> list) {
        //  区分pdm和非pdm的销售代码 使用Stream API的partitioningBy进行分区
        Map<Boolean, List<MaterialBatchAddDto>> partitioned = list.stream()
                .collect(Collectors.partitioningBy(
                        p -> StringUtils.isNotBlank(p.getSalesCode()) &&
                                SalesCodeUtils.isPdmSalesCode(p.getSalesCode())
                ));
        // 获取PDM销售代码和非PDM销售代码的材料批次添加DTO列表
        List<MaterialBatchAddDto> pdmList = partitioned.getOrDefault(true,Collections.emptyList());
        List<MaterialBatchAddDto> nonPdmList = partitioned.getOrDefault(false,Collections.emptyList());
        //todo 非18代码
        // 1. 根据销售ID列表查询PDM信息
        List<String> salesCodeList = pdmList.stream()
                .map(MaterialBatchAddDto::getSalesCode)
                .collect(Collectors.toList());

        //本地已保存的销售代码
        List<PdmInfoEntity> existPdmInfoList = getExistPdmInfo(salesCodeList);
        List<PdmInfoEntity> pdmInfoEntityList = queryPdmInfoBySalesCodeList(salesCodeList);
        if (CollectionUtils.isNotEmpty(existPdmInfoList)) {
            keepOriginalId(pdmInfoEntityList,existPdmInfoList);
        }
        //检查查询的pdm信息中名称是否为空
        /* Started by AICoder, pid:53c26ubf309abc11452108c1a08ddb0e7d64d1bf */
        List<PdmInfoEntity> emptyNameEntities = pdmInfoEntityList.stream()
                .filter(entity -> StringUtils.isBlank(entity.getName()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emptyNameEntities)) {
            log.error("pdm name is empty. Invalid pdm data:{}",emptyNameEntities);
            throw new BusinessException(PdmStatusCode.INVALID_PDM_DATA);
        }
        Map<String, PdmInfoEntity> pdmInfoEntityMap = pdmInfoEntityList.stream()
                .collect(Collectors.toMap(PdmInfoEntity::getSalesCode, pdmInfoEntity -> pdmInfoEntity));

        // 3. 获取当前时间和用户ID
        String currentTime = DateTimeUtils.getCurrentTime();
        String userId = authService.getUserId();
        List<MaterialEntity> othDraftMaterialList = getOthDraftMaterial(nonPdmList, currentTime, userId);
        // 4. 构建物料实体列表
        List<MaterialEntity> materialEntityList = pdmList.stream()
                .map(dto -> {
                    PdmInfoEntity pdmInfoEntity = pdmInfoEntityMap.get(dto.getSalesCode());
                    if (pdmInfoEntity == null) {
                        log.error("Material sales code:{} corresponding PDM information does not exist.", dto.getSalesCode());
                        throw new BusinessException(StatusCode.DATA_NOT_FOUND);
                    }
                    MaterialEntity entity = new MaterialEntity();
                    entity.setName(pdmInfoEntity.getName());
                    entity.setId(UUID.randomUUID().toString());
                    entity.setMaterialStatus(MaterialStatusEnums.AVAILABLE_APPROVAL.getId());
                    entity.setPdmInfoId(pdmInfoEntity.getId());
                    entity.setGroupId(dto.getGroupId());
                    entity.setBrand(dto.getBrand());
                    entity.setSupplier(dto.getSupplier());
                    String modeEnumsId = getPurchaseModeEnumsId(dto.getPurchaseMode());
                    entity.setPurchaseMode(modeEnumsId);
                    String formatDateStr = getFormatDateStr(dto.getExpirationDate());
                    entity.setExpirationDate(formatDateStr);
                    entity.setWarrantyPeriod(dto.getWarrantyPeriod());
                    entity.setSpecificationModel(dto.getSpecificationModel());
                    entity.setService(dto.getService());
                    entity.setVersion(String.valueOf(GlobalConstants.ONE));
                    entity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
                    entity.setRecommendedLevel(dto.getRecommendedLevel());
                    entity.setUnit(dto.getUnit());
                    entity.setDescription(dto.getDescription());
                    entity.setCreateBy(userId);
                    entity.setUpdateBy(userId);
                    entity.setCreateTime(currentTime);
                    entity.setUpdateTime(currentTime);
                    return entity;
                })
                .collect(Collectors.toList());
        //先删后增
        // 6. 批量新增PDM信息和物料信息
        pdmInfoRepository.batchAdd(pdmInfoEntityList);
        List<String> stringList = materialEntityList.stream().map(MaterialEntity::getPdmInfoId).collect(Collectors.toList());
        Map<String, PdmInfoEntity> pdmMap = pdmInfoRepository.selectPdmInfoByIds(stringList).stream().collect(Collectors.toMap(PdmInfoEntity::getId, Function.identity()));
        List<MaterialHistoryEntity> historyMaterialList = materialEntityList.stream().map(p -> mainCovertToHistory(p, null, null,pdmMap)).collect(Collectors.toList());
        materialEntityList.addAll(othDraftMaterialList);
        materialRepository.batchAdd(materialEntityList);
        materialHistoryRepository.batchAddHistoryMaterial(historyMaterialList);
    }
    /* Ended by AICoder, pid:ic242caf6eu3bdc1442208e98135b101f3820369 */
    private void setCommonOthInfo(OthInfoEntity othInfoEntity,MaterialAddDto addDto) {
        // 设置公共属性
        othInfoEntity.setSalesCode(addDto.getSalesCode());
        othInfoEntity.setName(addDto.getName());
        othInfoEntity.setSalesStatus(GlobalConstants.OTH_SALES_CODE);
        othInfoEntity.setProductionCode(addDto.getProductionCode());
        //othInfoEntity.setUnit(addDto.getUnit());
        //othInfoEntity.setDescription(addDto.getDescription());
    }
    /* Ended by AICoder, pid:8c3d2d0fb0udd7314c900a8b60747d5eada718bb */
    /* Ended by AICoder, pid:w831feb9bade70a140fe0be5e08b5427ca32d226 */

    @Override
    public List<MaterialWithExtendInfoVo> queryByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<MaterialWithExtendInfoEntity> entityList = materialRepository.queryMaterialWithExtendInfoByIds(ids);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        // 查询产品分组
        List<String> groupIds = entityList.stream().map(MaterialWithExtendInfoEntity::getGroupId).collect(Collectors.toList());
        List<ProductGroupEntity> productGroupEntities = productGroupRepository.selectByIds(groupIds);
        Map<String, ProductGroupEntity> groupPathNameMap = productGroupEntities.stream().collect(Collectors.toMap(ProductGroupEntity::getId, vo -> vo));

        // 查询产品小类
        List<String> productCategoryIdList = productGroupEntities.stream().map(ProductGroupEntity::getProductCategoryId).collect(Collectors.toList());
        List<ProductSubcategoryVo> pGEntityList = productCategoryRepository.queryUserProductSubcategory(productCategoryIdList);
        Map<String, ProductSubcategoryVo> pGEntityMap = pGEntityList.stream().collect(Collectors.toMap(ProductSubcategoryVo::getId, vo -> vo));

        return entityList.stream().map(item -> {
            MaterialWithExtendInfoVo materialVo = new MaterialWithExtendInfoVo();
            materialVo.setId(item.getId());
            materialVo.setMaterialName(item.getMaterialName());
            materialVo.setGroupId(item.getGroupId());
            materialVo.setExpirationDate(item.getExpirationDate());
            materialVo.setMaterialStatus(item.getMaterialStatus());
            PurchaseModeEnums purchaseModeEnum = PurchaseModeEnums.getById(item.getPurchaseMode());
            if (purchaseModeEnum != null) {
                String purchaseModeName = I18nUtil.getI18nFromString(purchaseModeEnum.getName());
                materialVo.setPurchaseMode(new IdNameBean(item.getPurchaseMode(), purchaseModeName));
            }
            PdmInfoEntity pdmInfoEntity = item.getPdmInfoEntity();
            if (pdmInfoEntity != null) {
                PdmInfoVo pdmInfoVo = new PdmInfoVo();
                materialVo.setPdmInfo(pdmInfoVo);
                pdmInfoVo.setSalesCode(pdmInfoEntity.getSalesCode());
                pdmInfoVo.setProductionCode(pdmInfoEntity.getProductionCode());
            }
            OthInfoEntity othInfoEntity = item.getOthInfoEntity();
            if (othInfoEntity != null) {
                OthInfoVo othInfoVo = new OthInfoVo();
                materialVo.setOthInfo(othInfoVo);
                othInfoVo.setSalesCode(othInfoEntity.getSalesCode());
                othInfoVo.setProductionCode(othInfoEntity.getProductionCode());
            }
            /* Started by AICoder, pid:o46cbpc2aax0ef51403c0a85f04dce143655395c */
            // 优化后的代码
            ProductGroupEntity productGroupEntity = groupPathNameMap.get(item.getGroupId());
            if (productGroupEntity != null) {
                materialVo.setGroupPathName(productGroupEntity.getPathName());
                String productCategoryId = productGroupEntity.getProductCategoryId();
                // 直接在获取psVo后进行非空检查并设置值
                ProductSubcategoryVo psVo = pGEntityMap.get(productCategoryId);
                if (psVo != null) {
                    materialVo.setProductCategoryId(productCategoryId);
                    materialVo.setPathName(psVo.getPathName());
                }
            }
            materialVo.setMaterialNameEn(item.getMaterialNameEn());
            materialVo.setUnit(item.getUnit());
            materialVo.setUnitEn(item.getUnitEn());

            materialVo.setTecParam(item.getTecParam());
            materialVo.setCost(item.getCost());
            /* Ended by AICoder, pid:o46cbpc2aax0ef51403c0a85f04dce143655395c */
            return materialVo;
        }).collect(Collectors.toList());
    }

    @Override
    public PageVO<MaterialVo> fuzzyQuery(MaterialFuzzyQueryDto queryDto) {
        List<String> actualGroupIds = getGroupCondition(queryDto.getGroupId(), queryDto.getProductCategoryId());
        if (CollectionUtils.isEmpty(actualGroupIds)) {
            return new PageVO<>(0, Collections.emptyList());
        }
        queryDto.setGroupId(actualGroupIds);
        log.debug("actualGroupIds:{}",actualGroupIds);
        PageInfo<MaterialVo> pageInfo = materialRepository.fuzzyQuery(queryDto);
        List<MaterialVo> list = pageInfo.getList();
        List<String> materialIds = list.stream().map(MaterialVo::getId).collect(Collectors.toList());
        Map<String,String> lectotypeStatusMap = getLectotypeStatusMap(materialIds);
        list.forEach(item -> {
            String lectotypeStatus = lectotypeStatusMap.getOrDefault(item.getId(), StringUtils.EMPTY);
            item.setLectotypeStatus(lectotypeStatus);
            item.setPurchaseMode(getPurchaseModeName(item.getPurchaseMode()));
        });
        //
        return new PageVO<>(pageInfo.getTotal(), list);
    }

    private Map<String, String> getLectotypeStatusMap(List<String> materialIds) {

        Map<String, String> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(materialIds)) {
            return resultMap;
        }
        List<LectotypeMaterialVo> lectotypeMaterialVos = materialRepository.querySelectionFormByMaterialIds(materialIds);

        // 将List<LectotypeMaterialVo>转换为Map<String, String>，其中key是materialId，value是lectotypeStatus的字符串形式
        lectotypeMaterialVos.forEach(p->{
            if (LectotypeTypeEnums.ONE.type.equals(p.getLectotypeType())) {
                if (p.getLectotypeStatus() == null) {
                    //没有填写，默认初始阶段
                    p.setLectotypeStatus(LectotypeStatusEnums.ONE.type);
                }
                resultMap.put(p.getMaterialId(), String.valueOf(p.getLectotypeStatus()));
            }
        });
       return resultMap;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateMaterialCost(List<MaterialCostBatchUpdateDto> list) {
        Map<String, String> materialCostMap = list.stream().collect(Collectors.toMap(MaterialCostBatchUpdateDto::getId, MaterialCostBatchUpdateDto::getCost, (exist, replace) -> exist));
        //查询主表数据，不处理临时表中数据
        List<MaterialEntity> materialEntities = materialRepository.queryMaterialByIds(new ArrayList<>(materialCostMap.keySet()));
        String currentTime = DateTimeUtils.getCurrentTime();
        String userId = authService.getUserId();
        //主表数据更新，同时记录一份到历史表中
        if (CollectionUtils.isNotEmpty(materialEntities)) {
            ProductGroupEntity groupEntity = productGroupRepository.queryProductGroupDetail(materialEntities.get(0).getGroupId());
            if(groupEntity!=null){
                permissionUtil.checkPermission(groupEntity.getProductCategoryId(), PermissionEnum.MATERIAL_COST_BREAKDOWN);
            }
            materialEntities.forEach(item -> {
                item.setCost(materialCostMap.get(item.getId()));
                String ver = item.getVersion();
                if (StringUtils.isNotBlank(ver)) {
                    int version = Integer.parseInt(ver) + 1;
                    item.setVersion(String.valueOf(version));
                } else {
                    int version = GlobalConstants.ONE;
                    item.setVersion(String.valueOf(version));
                }
            });
            List<MaterialHistoryEntity> historyList = materialEntities.stream().map(material -> {
                MaterialHistoryEntity historyEntity = mainCovertToHistory(material, null, null, new HashMap<>());
                historyEntity.setApprovalId("");
                historyEntity.setUpdateTime(currentTime);
                historyEntity.setUpdateBy(userId);
                historyEntity.setCreateBy(userId);
                historyEntity.setCreateTime(currentTime);
                return historyEntity;
            }).collect(Collectors.toList());

            //历史成本表数据
            List<ProcurementPriceHistoryEntity> priceHistoryEntityList=new ArrayList<>();
            materialEntities.forEach(materialEntity -> {
                String uuid = UUID.randomUUID().toString();
                ProcurementPriceHistoryEntity priceHistoryEntity=new ProcurementPriceHistoryEntity();
                priceHistoryEntity.setId(uuid);
                priceHistoryEntity.setPrice(materialEntity.getCost());
                priceHistoryEntity.setPriceCategory(PriceCategoryEnums.COST_PRICE.getCode());
                priceHistoryEntity.setLectotypeId(materialEntity.getId());
                priceHistoryEntity.setLectotypeType(materialEntity.getId());
                priceHistoryEntity.setCreateBy(userId);
                priceHistoryEntity.setCreateTime(currentTime);
                priceHistoryEntity.setUpdateBy(userId);
                priceHistoryEntity.setUpdateTime(currentTime);
                priceHistoryEntityList.add(priceHistoryEntity);
            });
            materialRepository.batchUpdateMaterials(materialEntities);
            materialHistoryRepository.batchAddHistoryMaterial(historyList);
            priceHistoryRepository.batchAddHistory(priceHistoryEntityList);
        }
    }

    @Override
    /* Started by AICoder, pid:pef5dc971bb9865144370848a0a5c95948c580b5 */
    public SelectionFormMaterialVo queryBySelectionForm(SelectionFormMaterialQueryDto queryDto) {
        SelectionFormMaterialVo selectionFormMaterialVo = new SelectionFormMaterialVo();

        // TODO
        String sortOrder = GlobalConstants.DESC_ORDER.equalsIgnoreCase(queryDto.getSortOrder())
                ? GlobalConstants.DESC_ORDER
                : GlobalConstants.ASC_ORDER;
        queryDto.setSortOrder(sortOrder);

        List<String> materialIdList = materialRepository.queryAssociationMaterial(queryDto.getLectotypeId());
        List<MaterialVo> list = materialRepository.queryAssociatedMaterial(materialIdList, queryDto);

        if (CollectionUtils.isNotEmpty(list)) {
            ProductGroupEntity groupEntity = productGroupRepository.queryProductGroupDetail(list.get(0).getGroupId());
            if (groupEntity != null) {
                permissionUtil.checkPermission(groupEntity.getProductCategoryId(), PermissionEnum.PROCUREMENT_MATERIAL_COST_VIEW);
            }
        }

        // 设置采购模式名称，并计算总成本
        BigDecimal totalCost = list.stream()
                .peek(item -> {
                    // 先处理采购模式名称
                    item.setPurchaseMode(getPurchaseModeName(item.getPurchaseMode()));

                    // 处理成本解密逻辑(因为之前存在未加密的数据，解密会出现异常于是根据字符长度判断是否进行解密,或者后面可以判断纯数字就是历史数据不解密)
//                    String cost = item.getCost();
//                    if (StringUtils.isNotBlank(cost)&&cost.length()>GlobalConstants.TWENTY) {
//                        try {
//                            // 尝试解密
//                            String decryptedCost = CryptoUtil.decrypt(cost);
//                            // 验证是否为有效数值
//                            new BigDecimal(decryptedCost); // 仅验证不抛异常
//                            item.setCost(decryptedCost); // 更新为解密后的值
//                        } catch (Exception e) {
//                            // 若解密失败，(部分历史成本数据存在未加密的情况)尝试直接解析原值
//                            try {
//                                new BigDecimal(cost); // 验证原值是否合法
//                            } catch (NumberFormatException ex) {
//                                log.warn("Invalid cost format: {}", cost);
//                                item.setCost(BigDecimal.ZERO.toString()); // 非法值清零
//                            }
//                        }
//                    }
                })
                .map(item -> StringUtils.isBlank(item.getCost())
                        ? BigDecimal.ZERO
                        : new BigDecimal(item.getCost())) // 这里用处理后的cost
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        selectionFormMaterialVo.setList(list);
        selectionFormMaterialVo.setTotalCost(totalCost);

        return selectionFormMaterialVo;
    }

    /* Ended by AICoder, pid:pef5dc971bb9865144370848a0a5c95948c580b5 */

    @Override
    public PageVO<MaterialVo> selectionAssociationConditions(MaterialConditionQueryDto dto) {

        List<String> materialIdList = materialRepository.queryAssociationMaterial(dto.getLectotypeId());
        if (CollectionUtils.isEmpty(materialIdList)) {
            return new PageVO<>(0,Collections.emptyList());
        }
        String sortOrder = GlobalConstants.DESC_ORDER.equalsIgnoreCase(dto.getSortOrder()) ? GlobalConstants.DESC_ORDER : GlobalConstants.ASC_ORDER;
        dto.setSortOrder(sortOrder);
        dto.setMaterialIdList(materialIdList);
        PageVO<MaterialVo> pageVO = materialRepository.selectionAssociationConditions(dto);
        List<MaterialVo> list = pageVO.getList();
        list = list.stream()
                .peek(ma -> {
                    ma.setMaterialStatusName(getMaterialStatusName(ma.getMaterialStatus()));
                    ma.setPurchaseMode(getPurchaseModeName(ma.getPurchaseMode()));
                }).collect(Collectors.toList());
        return pageVO;


    }

    @Override
    public PageVO<MaterialVo> fuzzyQuerySelection(MaterialFuzzyQueryDto queryDto) {

        PageInfo<MaterialVo> pageInfo = materialRepository.fuzzyQuery(queryDto);
        List<MaterialVo> list = pageInfo.getList();
        list.forEach(item -> {
            item.setPurchaseMode(getPurchaseModeName(item.getPurchaseMode()));
            item.setMaterialStatusName(getMaterialStatusName(item.getMaterialStatus()));
        });
        return new PageVO<>(pageInfo.getTotal(), list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteMaterial(List<String> materialIdList) {
        if (CollectionUtils.isEmpty(materialIdList)) {
            return;
        }
        List<MaterialEntity> materialEntities = materialRepository.queryMaterialByIds(materialIdList);
        if (CollectionUtils.isEmpty(materialEntities)) {
            return;
        }
        // 检查权限
        checkPermissionForDeletion(materialEntities.get(0));
        // 过滤不能删除的物料状态，并在有不支持删除的状态时抛出异常
        checkUnsupportedDeleteStatus(materialEntities);

        // 删除物料及其相关数据
        performBatchDelete(materialEntities);
    }
    private void checkPermissionForDeletion(MaterialEntity materialEntity) {
        ProductGroupEntity groupEntity = productGroupRepository.queryProductGroupDetail(materialEntity.getGroupId());
        if(groupEntity != null) {
            permissionUtil.checkPermission(groupEntity.getProductCategoryId(), PermissionEnum.PRODUCT_MATERIAL_DELETE);
        }
    }
    private void checkUnsupportedDeleteStatus(List<MaterialEntity> materialEntities) {
        boolean hasUnsupportedStatus = materialEntities.stream()
                .anyMatch(entity -> !MaterialStatusEnums.DRAFT.getId().equals(entity.getMaterialStatus()) &&
                        !MaterialStatusEnums.UNAVAILABLE.getId().equals(entity.getMaterialStatus()));
        if (hasUnsupportedStatus) {
            throw new BusinessException(ProductCategoryStatusCode.UNSUPPORTED_DELETE);
        }
    }
    private void performBatchDelete(List<MaterialEntity> materialEntities) {
        // 执行批量删除操作
        materialRepository.batchDeleteMaterialByIds(materialEntities.stream().map(MaterialEntity::getId).collect(Collectors.toList()));

        List<String> unavailableMaterialIds = new ArrayList<>();
        List<String> othInfoIds = new ArrayList<>();

        // 在单次遍历中同时收集unavailableMaterialIds和othInfoIds
        for (MaterialEntity entity : materialEntities) {
            if (MaterialStatusEnums.UNAVAILABLE.getId().equals(entity.getMaterialStatus())) {
                unavailableMaterialIds.add(entity.getId());
            }
            if (StringUtils.isNotBlank(entity.getOthInfoId())) {
                othInfoIds.add(entity.getOthInfoId());
            }
        }
        // 删除临时表中的数据
        if (!unavailableMaterialIds.isEmpty()) {
            materialTemporaryRepository.deleteByMaterialIds(unavailableMaterialIds);
        }
        // 删除其他销售代码信息
        if (!othInfoIds.isEmpty()) {
            othSalesCodeRepository.batchDeleteByIds(othInfoIds);
        }

        // 删除文档关联关系
        materialEntities.forEach(entity -> documentService.deleteByResourceId(entity.getId()));
    }

    @Override
    public Boolean delMaterial(String id) {
        MaterialEntity entity = materialRepository.queryMaterialById(id);
        if (entity == null) {
            return true;
        }

        String materialStatus = entity.getMaterialStatus();
        //t已创建的物料，可以删除，对于已上架或变更中、变更审批中的物料需要先下架后才能删除。 其他待审批的物料自动撤回审批后删除物料。
        if (MaterialStatusEnums.AVAILABLE.getId().equals(materialStatus) ||
                MaterialStatusEnums.LISTING.getId().equals(materialStatus)||
                MaterialStatusEnums.CHANGE_APPROVAL.getId().equals(materialStatus)) {
            return false;
        }

        if (MaterialStatusEnums.UNAVAILABLE_APPROVAL.getId().equals(materialStatus)||
                MaterialStatusEnums.AVAILABLE_APPROVAL.getId().equals(materialStatus) ) {
            demandProcessRpc.delMaterialProcess(entity.getId());

        }

        materialRepository.deleteMaterial(id);
        if (MaterialStatusEnums.UNAVAILABLE.getId().equals(materialStatus)) {
            //已下架的，需要同步删除可能存在的临时表数据
            materialTemporaryRepository.deleteByMaterialIds(Collections.singletonList(id));
        }
        if (StringUtils.isNotBlank(entity.getOthInfoId())) {
            othSalesCodeRepository.deleteOthSalesCodeById(entity.getOthInfoId());
        }
        //删除文档关联关系
        documentService.deleteByResourceId(id);

        return true;
    }

    @Override
    public ParseVerifyMaterialOthVo othImport(FormDataMultiPart file) {
        ParseVerifyMaterialOthVo queryDto = new ParseVerifyMaterialOthVo();

        List<JtExcelFieldIn> list = new ArrayList<>();
        JtExcelFieldIn excelFieldIn;

        // 添加字段配置
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("categoryName");
        excelFieldIn.setColIndex('A');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("name");
        excelFieldIn.setColIndex('B');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("nameEn");
        excelFieldIn.setColIndex('C');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("unit");
        excelFieldIn.setColIndex('D');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("unitEn");
        excelFieldIn.setColIndex('E');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("remark");
        excelFieldIn.setColIndex('F');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("tecParam");
        excelFieldIn.setColIndex('G');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("productName");
        excelFieldIn.setColIndex('H');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("cost");
        excelFieldIn.setColIndex('I');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("id");
        excelFieldIn.setColIndex('J');
        list.add(excelFieldIn);

        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("othCode");
        excelFieldIn.setColIndex('K');
        list.add(excelFieldIn);

        String json = JSON.toJSONString(list);
        List<JtExcelFieldIn> fields = JSON.parseArray(json, JtExcelFieldIn.class);

        // 构建 Excel 导入构建器
        ExcelImportBuilder builder = new ExcelImportBuilder.Builder(fields, TemplateImportOthVo.class)
                .sheetName("Sheet1")
                .startLineNum(2)
                //.endLineNum(3)
                .build();

        FormDataBodyPart part = file.getField("file");
        List<TemplateImportOthVo> list1 = builder.imports(part.getValueAs(InputStream.class));

        if (list1 != null && list1.size() > 0) {
            for (TemplateImportOthVo t : list1) {
                if (StringUtils.isBlank(t.getOthCode())) {
                    t.setCheckResult("1");
                    t.setErrorReason("OTH代码为空");
                } else {
                    MaterialVo me = materialRepository.multiTableQueryById(t.getId());
                    if (me == null) {
                        t.setCheckResult("1");
                        t.setErrorReason("没有获取到物料");
                    } else {
                        t.setCheckResult("0");
                        t.setErrorReason("");
                    }
                }
            }
        } else {
            throw new BusinessException(ProductCategoryStatusCode.READING_EXCEL_FAIL);
        }

        queryDto.setMaterial(list1);
        queryDto.setTotal(list1.size());
        return queryDto;
    }

    /**
     * othExport 方法用于导出 Excel 文件。
     *
     * @param approvalId 审批ID
     * @param response HTTP 响应对象
     */
    @Override
    public void othExport(String approvalId, HttpServletResponse response) {
        ApprovalCommonVo approvalObj = processService.getApprovalByApprovalId(approvalId);

        String bussesDataJson = approvalObj.getBussesDataJson();
        List<TemplateExportOthVo> list = new ArrayList<>();

        try {
            List<ApprovalMaterialOtVo> materialVos = JSON.parseArray(bussesDataJson, ApprovalMaterialOtVo.class);
            List<String> ids = materialVos.stream().map(ApprovalMaterialOtVo::getId).collect(Collectors.toList());
            List<MaterialWithExtendInfoVo> materialList = queryByIds(ids);

            for (MaterialWithExtendInfoVo item : materialList) {
                TemplateExportOthVo templateExportOthVo = new TemplateExportOthVo();
                templateExportOthVo.setId(item.getId());

                if (null != item.getPurchaseMode() && item.getPurchaseMode().getName().equals("自研")) {
                    templateExportOthVo.setCategoryName("其他-自制硬件");
                } else {
                    templateExportOthVo.setCategoryName("其他-外购硬件国内采购");
                }

                templateExportOthVo.setName(item.getMaterialName());
                templateExportOthVo.setNameEn(item.getMaterialNameEn());
                templateExportOthVo.setUnit(item.getUnit());
                templateExportOthVo.setUnitEn(item.getUnitEn());

                if (StringUtils.isNotBlank(approvalObj.getSubmitUser())) {
                    UserVo u = systemService.getUserinfoById(approvalObj.getSubmitUser());
                    if (u != null) {
                        templateExportOthVo.setRemark(u.getName() + approvalObj.getSubmitUser());
                    }
                }

                templateExportOthVo.setTecParam(item.getTecParam());
                log.info("materialVos:{}", JSON.toJSONString(materialVos));
                log.info("templateStream:{}", JSON.toJSONString(list));
                for (ApprovalMaterialOtVo approvalMaterialOtVo:materialVos){
                    if(approvalMaterialOtVo.getId().equals(item.getId())){
                        templateExportOthVo.setCost(approvalMaterialOtVo.getCost());
                    }
                }
                String productId = demandMapper.getProductIdByMaterialId(item.getId());
                log.info("productId:{}", JSON.toJSONString(productId));
                if(StringUtils.isNotBlank(productId)){
                    ProjectDetailInfoVo projectDetailInfoVo =projectService.getProjectDetailInfo(productId);
                    log.info("projectDetailInfoVo:{}", JSON.toJSONString(projectDetailInfoVo));
                    if(null!=projectDetailInfoVo){
                        templateExportOthVo.setProductName(projectDetailInfoVo.getName());
                    }else {
                        templateExportOthVo.setProductName("/");
                    }
                }


                list.add(templateExportOthVo);
            }
        } catch (BusinessException e) {
            log.error("bussesDataJson failed to transfer an ApprovalMaterialVo", e);
        }



        try {
            // 获取模板文件输入流
            ClassPathResource resource = new ClassPathResource("template" + File.separator + "oth-template.xlsx");
            log.info("templateStream:{}", JSON.toJSONString(resource));
            InputStream templateStream = resource.getInputStream();

            try {
                String fileNameStr = "OTH-template" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";
                String encodedFileName = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8.toString());
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
            } catch (Exception e) {
                log.error("set response header error", e);
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }
            EasyExcel.write(response.getOutputStream())
                    .withTemplate(templateStream)
                    .registerWriteHandler(setStyle())
                    .excelType(ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.TRUE)
                    .sheet("Sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("export productCoreParamList data is filed", e);
            throw new BusinessException(StatusCode.FAILED);
        }
    }

    /* Started by AICoder, pid:b5756188c4cadff148dc0a85d0887c2de426e46b */
    @Transactional
    @Override
    public void changeMaterialCost(MaterialCostDto costDto) {
        // 1: 更新物料表-----------------------
        MaterialEntity materialEntity = materialRepository.queryMaterialById(costDto.getId());
        if (null == materialEntity) {
            log.error("changeMaterialCost the materialEntity info is not exist");
            throw new BusinessException(ProductCategoryStatusCode.OBJECT_NULL_ERROR);
        }
        //历史成本
        String previousCost = materialEntity.getCost();
//        if (StringUtils.isNotBlank(previousCost)) {
//            try {
//                //解密成本
//                previousCost = decryptPrice(previousCost);
//            } catch (Exception e) {
//                //解密失败使用原值
//                log.info("using original value of the previous cost");
//            }
//        }
        //当前成本
        String currentCost = costDto.getCost();
        if (currentCost.equals(previousCost)) {
            //若历史成本与本次变更成本相同则不记录变更历史以及版本
            return;
        }
        String currentUserId=authService.getUserId();
        String currentTime=DateTimeUtils.getCurrentTime();
        //BigDecimal bigDecimalPrice=new BigDecimal(currentCost);
        //加密成本
        //String price= encryptPrice(bigDecimalPrice);
        int currentVersion = StringUtils.isBlank(materialEntity.getVersion()) ? GlobalConstants.MATERIAL_DEFAULT_VERSION_NUMBER : Integer.parseInt(materialEntity.getVersion()) + 1;
        materialEntity.setVersion(String.valueOf(currentVersion));
        materialEntity.setCost(currentCost);
        materialEntity.setUpdateBy(currentUserId);
        materialEntity.setUpdateTime(currentTime);
        materialRepository.updateMaterial(materialEntity);

        // 2: 写入物料历史表-----------------------
        MaterialHistoryEntity materialHistoryEntity = new MaterialHistoryEntity();
        BeanUtils.copyProperties(materialEntity, materialHistoryEntity);
        String uuid = UUID.randomUUID().toString();
        materialHistoryEntity.setId(uuid);
        materialHistoryEntity.setMaterialId(materialEntity.getId());
        //因为物料变更不涉及上架流程所以不记录流程id
        materialHistoryEntity.setApprovalId("");
        materialHistoryEntity.setApprovalTime("");
        materialHistoryRepository.addHistoryMaterial(materialHistoryEntity);
        //3: 写入历史成本表-----------------------
        ProcurementPriceHistoryEntity priceHistoryEntity=new ProcurementPriceHistoryEntity();
        priceHistoryEntity.setId(uuid);
        priceHistoryEntity.setPrice(currentCost);
        priceHistoryEntity.setPriceCategory(PriceCategoryEnums.COST_PRICE.getCode());
        //因为变更物料成本没有选型单id,这里存物料id
        priceHistoryEntity.setLectotypeId(materialEntity.getId());
        priceHistoryEntity.setLectotypeType(materialEntity.getId());
        priceHistoryEntity.setCreateBy(currentUserId);
        priceHistoryEntity.setCreateTime(currentTime);
        priceHistoryEntity.setUpdateBy(currentUserId);
        priceHistoryEntity.setUpdateTime(currentTime);
        //记录成本变更原因(成本变更)
        priceHistoryEntity.setRemark(GlobalConstants.NUMBER_THREE);
        priceHistoryRepository.addPriceHistory(priceHistoryEntity);
    }

    public String buildLogDetail(RequirementDashboardAddDto object) {
        JsonUtils jsonUtils = JsonUtils.getInstance();
        try {
            return jsonUtils.objectToJson(object);
        } catch (UedmException e) {
            log.error("convert error",e);
        }
        return StringUtils.EMPTY;
    }

    /* Started by AICoder, pid:o194ava0dd44b8f1495f0a3230f9e92942036250 */

    /* Started by AICoder, pid:vd01c800f4s099a140fc0af56042023373f2f03f */
    /**
     * 价格加密保存
     *
     * @param price 价格
     * @return 加密字符串
     */
    private String encryptPrice(BigDecimal price) {
        if (price == null) {
            return StringUtils.EMPTY;
        }
        try {
            return CryptoUtil.encrypt(price.toString());
        } catch (Exception e) {
            log.error("encryptPrice error", e);
            throw new BusinessException(ProductCategoryStatusCode.PRICE_ENCRYPT_ERROR);
        }
    }

    /**
     * 价格解密展示保存
     *
     * @param price 价格加密字符串
     * @return 解密后的价格
     */
    private String decryptPrice(String price) {
        try {
            return CryptoUtil.decrypt(price);
        } catch (Exception e) {
            log.error("decryptPrice error", e);
            throw new BusinessException(ProductCategoryStatusCode.PRICE_DECRYPT_ERROR);
        }
    }

    /* Ended by AICoder, pid:vd01c800f4s099a140fc0af56042023373f2f03f */
    @Override
    public PageVO<PriceHistoryVo> getChangeMaterialCost(MaterialCostDto costDto) {
        //成本类型
        String priceCategory=PriceCategoryEnums.COST_PRICE.getCode();
        //物料id
        String materialId = costDto.getId();
        // 默认分页参数
        Integer pageNum = GlobalConstants.ONE;
        Integer pageSize = GlobalConstants.TEN;

        if (costDto.getPageNum() != null && costDto.getPageSize() != null) {
            pageNum = costDto.getPageNum();
            pageSize = costDto.getPageSize();
        }

        PageHelper.startPage(pageNum, pageSize);
        List<PriceHistoryVo> priceHistoryList = materialHistoryRepository.getChangeMaterialPriceHistoryList(materialId);
        if (CollectionUtils.isEmpty(priceHistoryList)) {
            return new PageVO<>();
        }
        //组装数据
        decryptionList(priceHistoryList);
        PageInfo<PriceHistoryVo> pageInfo = new PageInfo<>(priceHistoryList);
        List<PriceHistoryVo> list = pageInfo.getList();
        return new PageVO<>(pageInfo.getTotal(), list);
    }

    /* Ended by AICoder, pid:o194ava0dd44b8f1495f0a3230f9e92942036250 */

    /* Started by AICoder, pid:k0be2x0b2695c0014c760aefa04d5b10f66472a3 */
    @Transactional
    @Override
    public void maintainSpecification(MaintainSpecificationDto maintainSpecificationDto) {
        String materialId = maintainSpecificationDto.getMaterialId();
        String maintainRemark = maintainSpecificationDto.getSpecificationRemark();
        String currentUserId = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();

        MaterialEntity materialEntity = materialRepository.queryMaterialById(materialId);
        if(null==materialEntity){
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        materialEntity.setUpdateTime(currentTime);
        materialEntity.setUpdateBy(currentUserId);
        materialEntity.setSpecificationRemark(maintainRemark);
        //更新物料主表
        materialRepository.updateMaterial(materialEntity);
        //更新物料暂存表(存在暂存数据才更新)
        MaterialTemporaryEntity materialTemporaryEntity = materialTemporaryRepository.queryTempMaterialByMaterialId(materialId);
        if (materialTemporaryEntity!=null){
            materialTemporaryEntity.setSpecificationModel(maintainRemark);
            materialTemporaryEntity.setUpdateTime(currentTime);
            materialTemporaryEntity.setUpdateBy(currentUserId);
            materialTemporaryRepository.updateTempMaterial(materialTemporaryEntity);
        }
    }

    /* Ended by AICoder, pid:k0be2x0b2695c0014c760aefa04d5b10f66472a3 */

    /* Started by AICoder, pid:u602ac03471c06914dad098e80399a482a92697d */
    private void decryptionList(List<PriceHistoryVo> priceHistoryVos) {
        // 获取用户id集合
        List<String> userIds = priceHistoryVos.stream()
                .map(PriceHistoryVo::getUpdateBy)
                .filter(Objects::nonNull) // 过滤掉 null 值
                .collect(Collectors.toList());

        List<UserVo> userVos = systemService.getUserinfoByIds(userIds);

        // 构造用户id和用户信息映射（处理重复键）
        Map<String, UserVo> userMap = userVos.stream()
                .collect(Collectors.toMap(
                        UserVo::getId,
                        Function.identity(),
                        (existing, replacement) -> existing // 保留首次出现的用户
                ));

        priceHistoryVos.forEach(item -> {
            String updateUserId = item.getUpdateBy();
            item.setUpdateBy(
                    Optional.ofNullable(updateUserId)
                            .map(userMap::get) // 方法引用简化
                            .map(UserVo::getDisplayText)
                            .orElse("未知用户") // 统一处理null
            );
            if (StringUtils.isNotBlank(item.getRemark())){
                String name = MaterialCostChangeEnums.getById(item.getRemark()).getName();
                item.setRemark(I18nUtil.getI18nFromString(name));
            }
            String cost = item.getCost();
            if (StringUtils.isNotBlank(cost)) {
                if (cost.length() > GlobalConstants.TWENTY_FOUR) {
                    // 解密成本价格
                    String decryptPrice = decryptPrice(cost);
                    BigDecimal bigDecimalNumber = new BigDecimal(decryptPrice);
                    item.setPrice(bigDecimalNumber);
                } else {
                    BigDecimal bigDecimalNumber = new BigDecimal(cost);
                    item.setPrice(bigDecimalNumber);
                }
                BigDecimal bigDecimalNumber = new BigDecimal(cost);
                item.setPrice(bigDecimalNumber);
            } else {
                item.setPrice(BigDecimal.ZERO);
            }
        });
    }

    /* Ended by AICoder, pid:u602ac03471c06914dad098e80399a482a92697d */
    /* Started by AICoder, pid:91932sbe22vee9d143b708561009811d865392aa */
    private List<PriceHistoryVo> convertToPriceHistoryVo(List<ProcurementPriceHistoryEntity> priceHistoryEntities) {
        //获取用户id集合
        List<String> userIds = priceHistoryEntities.stream()
                .map(ProcurementPriceHistoryEntity::getUpdateBy)
                .filter(Objects::nonNull) // 过滤掉 null 值
                .collect(Collectors.toList());
        List<UserVo> userVos = systemService.getUserinfoByIds(userIds);
        //构造用户id和用户信息映射
        Map<String, UserVo> userMap = userVos.stream().collect(Collectors.toMap(UserVo::getId, Function.identity()));
        List<PriceHistoryVo> priceHistoryVos = new ArrayList<>();
        for (ProcurementPriceHistoryEntity priceHistoryEntity : priceHistoryEntities) {
            PriceHistoryVo priceHistoryVo = new PriceHistoryVo();
            String price=priceHistoryEntity.getPrice();
            //解密成本价格
            //String decryptPrice = decryptPrice(price);
            BigDecimal bigDecimalNumber = new BigDecimal(price);
            priceHistoryVo.setPrice(bigDecimalNumber);
            priceHistoryVo.setId(priceHistoryEntity.getLectotypeId());
            String updateUserId=priceHistoryEntity.getUpdateBy();
            priceHistoryVo.setUpdateBy(
                    Optional.ofNullable(updateUserId)
                            .map(userId -> userMap.get(userId))
                            .map(UserVo::getDisplayText)
                            .orElse("未知用户")
            );
            priceHistoryVo.setUpdateTime(priceHistoryEntity.getUpdateTime());
            priceHistoryVos.add(priceHistoryVo);
        }
        return priceHistoryVos;
    }

    /* Ended by AICoder, pid:91932sbe22vee9d143b708561009811d865392aa */
    /* Ended by AICoder, pid:b5756188c4cadff148dc0a85d0887c2de426e46b */

    /**
     * setStyle 方法用于设置 Excel 单元格样式。
     *
     * @return HorizontalCellStyleStrategy 对象
     */
    private HorizontalCellStyleStrategy setStyle() {
        // 定义样式：自动换行
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setWrapped(true); // 关键：开启自动换行

        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("Microsoft YaHei"); // 字体
        writeFont.setFontHeightInPoints((short) 12); // 字体大小
        contentWriteCellStyle.setWriteFont(writeFont);

        // 注册样式策略（全局生效）
        HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(
                null, // 头样式（默认）
                contentWriteCellStyle // 内容样式（自动换行）
        );

        return styleStrategy;
    }
    /* Ended by AICoder, pid:412d8fc9b9o61571469d0897627f4f10a1e5d51c */
}
/* Ended by AICoder, pid:u6595k8c15312b1149000b3f5064134bc5f44d89 */
/* Ended by AICoder, pid:qa9b3n7157ca73314e9d0acca2c90a2a44939c35 */