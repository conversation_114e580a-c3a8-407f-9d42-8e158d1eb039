/* Started by AICoder, pid:528e8n5e199dc7a146640ae892532e2d10b8895c */
package com.zte.uedm.dcdigital.domain.model.material.event;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.PurchaseModeEnums;
import com.zte.uedm.dcdigital.domain.common.enums.TemplateCheckResultEnum;
import com.zte.uedm.dcdigital.domain.common.utils.SalesCodeUtils;
import com.zte.uedm.dcdigital.domain.model.material.vobj.TemplateObj;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.TemplateImportVo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;


/**
 * <AUTHOR>
 */
@Slf4j
@Setter
@Getter
public class MaterialReadListener extends AnalysisEventListener<TemplateObj> {

    //PDM销售代码的导入数据
    private List<TemplateImportVo> pdmSalesCodeList = new ArrayList<>();
    //PDM销售代码
    private List<String> pdmSalesCodes = new ArrayList<>();
    //PDM重复的数据
    private Map<String,List<TemplateImportVo>> pdmRepeatMap = new HashMap<>();

    //非PDM销售代码的导入数据
    private List<TemplateImportVo> othSalesCodeList = new ArrayList<>();
    //非PDM 销售代码集 非空的
    private List<String> othSalesCodes = new ArrayList<>();
    //非PDM 物料名称唯一
    private List<String> othNameList = new ArrayList<>();
    //非 PDM销售代码非空时唯一
    private Map<String,List<TemplateImportVo>> salesCodeRepeatMap = new HashMap<>();
    //非PDM销售代码的物料 名称唯一
    private Map<String,List<TemplateImportVo>> nameRepeatMap = new HashMap<>();


    private String productCategoryName;

    public MaterialReadListener(String productCategoryName) {
        this.productCategoryName = productCategoryName;
    }


    List<TemplateCheckRule> checkFunctions = Arrays.asList(
            this::checkProductName,
            this::checkGroup,
            this::checkMaterialName,
            this::checkBrand,
            this::checkRecommendedLevel,
            this::checkService,
            this::checkSpecificationModel,
            this::checkPurchaseMode,
            this::checkSupplier,
            this::checkUnit,
            this::checkDescription,
            this::checkExpirationDate
    );

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 所有数据解析完成后的操作，这里也可以不实现任何逻辑。
        log.info("读取完成");
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        // 处理异常情况
        log.info("解析过程中发生异常: " + exception.getMessage());
    }

    @Override
    public void invoke(TemplateObj templateObj, AnalysisContext analysisContext) {
        log.debug("读取到一行数据: " + templateObj);
        Integer rowIndex = analysisContext.readRowHolder().getRowIndex();
        if  (rowIndex <= 1) {
            return;
        }
        TemplateImportVo templateImportVo = new TemplateImportVo();
        templateImportVo.setRowIndex(rowIndex);
        BeanUtils.copyProperties(templateObj, templateImportVo);
        validate(templateImportVo);
    }

    private void validate(TemplateImportVo templateImportVo) {
        for (TemplateCheckRule checkFunction : checkFunctions) {
            checkFunction.apply(templateImportVo);
        }
        //销售代码
        checkSalesCode(templateImportVo);
    }


    private void checkSalesCode(TemplateImportVo templateImportVo) {
        String salesCode = templateImportVo.getSalesCode();
        if (StringUtils.isNotBlank(salesCode)) {
            String code = getRealString(salesCode);
            templateImportVo.setSalesCode(code);
            if (SalesCodeUtils.isPdmSalesCode(code)) {
                pdmSalesCodes.add(code);
                String key = templateImportVo.getSalesCode() + templateImportVo.getBrand();
                pdmRepeatMap.computeIfAbsent(key, k -> new ArrayList<>()).add(templateImportVo);
                pdmSalesCodeList.add(templateImportVo);
                return;
            }
            othSalesCodes.add(code);
            salesCodeRepeatMap.computeIfAbsent(templateImportVo.getSalesCode(), k -> new ArrayList<>()).add(templateImportVo);
        }
        if (StringUtils.isNotBlank(templateImportVo.getMaterialName())) {
            othNameList.add(templateImportVo.getMaterialName());
            nameRepeatMap.computeIfAbsent(templateImportVo.getMaterialName(), k -> new ArrayList<>()).add(templateImportVo);
        }
        //非18开头的 不用查询pdm
        othSalesCodeList.add(templateImportVo);
    }

    private void checkDescription(TemplateImportVo templateImportVo) {
        if (StringUtils.isBlank(templateImportVo.getDescription())) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.DESC_EMPTY));
        }
        String description = templateImportVo.getDescription();
        if (StringUtils.isNotBlank(description) && description.length() > GlobalConstants.DESCRIPTION_LENGTH) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.DESC_LENGTH_LIMIT_EXCEEDED));
        }
    }

    private void checkUnit(TemplateImportVo templateImportVo) {
        if (StringUtils.isBlank(templateImportVo.getUnit())) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.UNIT_EMPTY));
        }
        if (StringUtils.isNotBlank(templateImportVo.getUnit()) && templateImportVo.getUnit().length() > GlobalConstants.TEN) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.UNIT_LENGTH_LIMIT_EXCEEDED));
        }
    }


    private void checkProductName(TemplateImportVo templateImportVo) {
        String productName = templateImportVo.getProductName();
        if (StringUtils.isBlank(productName)) {
            //产品小类不能为空
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.PRODUCT_NAME_EMPTY));
            return;
        }
        String proName = getRealString(productName);
        templateImportVo.setProductName(proName);
        //产品小类名称长度不超过50
        if (proName.length() > GlobalConstants.NAME_LENGTH) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.PRODUCT_NAME_EXCEED_LIMIT));
        }
        //不能跨产品小类
        if (!proName.equals(productCategoryName)) {
            String[] args = new String[] {proName,productCategoryName};
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18nWithArgs(GlobalConstants.PRODUCT_NAME_ERROR,args));
        }
    }

    private void checkMaterialName(TemplateImportVo templateImportVo) {
        String materialName = templateImportVo.getMaterialName();
        if (StringUtils.isBlank(materialName)) {
            //物料名称不能为空
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.MATERIAL_NAME_EMPTY));
            return;
        }
        String realName = getRealString(materialName);
        templateImportVo.setMaterialName(realName);
        //物料名称长度不超过50
        if ( realName.length() > GlobalConstants.NAME_LENGTH) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.MATERIAL_NAME_TOO_LONG));
        }
    }

    private void checkGroup(TemplateImportVo templateImportVo) {
        String groupL1 = templateImportVo.getGroupL1();
        if (StringUtils.isBlank(groupL1)) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.GROUP_L1_EMPTY));
            return;
        }
        String groupL1Name = getRealString(groupL1);
        templateImportVo.setGroupL1(groupL1Name);
        if (groupL1Name.length() > GlobalConstants.NAME_LENGTH) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.GROUP_L1_TOO_LONG));
        }
        //分组只有 一级、两级 两种
        String groupL2 = templateImportVo.getGroupL2();
        if (StringUtils.isBlank(groupL2)) {
            //只有一级
            templateImportVo.setGroupPathName(groupL1Name);
            return;
        }
        String groupL2Name = getRealString(groupL2);
        templateImportVo.setGroupL2(groupL2Name);
        if (groupL2Name.length() > GlobalConstants.NAME_LENGTH) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.GROUP_L2_TOO_LONG));
        }
        String pathName = groupL1Name.concat(GlobalConstants.FORWARD_SLASH).concat(groupL2Name);
        templateImportVo.setGroupPathName(pathName);
    }

    private void checkBrand(TemplateImportVo templateImportVo) {
        String brand = templateImportVo.getBrand();
        if (StringUtils.isNotBlank(brand) && brand.length() > GlobalConstants.NAME_LENGTH_LIMIT) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.BRAND_TOO_LONG));
        }
    }

    //检查推荐等级
    private void checkRecommendedLevel(TemplateImportVo templateImportVo) {
        String recommendedLevel = templateImportVo.getRecommendedLevel();
        if (StringUtils.isNotBlank(recommendedLevel)) {
            String realString = getRealString(recommendedLevel);
            templateImportVo.setRecommendedLevel(realString);
            if (!GlobalConstants.RECOMMENDED_LEVEL_A.equalsIgnoreCase(realString)
            && !GlobalConstants.RECOMMENDED_LEVEL_B.equalsIgnoreCase(realString)
            && !GlobalConstants.RECOMMENDED_LEVEL_C.equalsIgnoreCase(realString)) {
                setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                        I18nUtil.getI18n(GlobalConstants.RECOMMENDED_LEVEL_ERROR));
            }
        }
    }

    private void checkService(TemplateImportVo templateImportVo) {
        String service = templateImportVo.getService();
        if (StringUtils.isNotBlank(service) && service.length() > GlobalConstants.NAME_LENGTH_LIMIT) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.SERVICE_LENGTH_LIMIT_EXCEEDED));
        }
    }
    private void checkSpecificationModel(TemplateImportVo templateImportVo) {
        String specificationModel = templateImportVo.getSpecificationModel();
        if (StringUtils.isNotBlank(specificationModel) && specificationModel.length() > GlobalConstants.NAME_LENGTH_LIMIT_100) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.SPECIFICATION_MODEL_LENGTH_LIMIT_EXCEEDED));
        }
    }
    private void checkPurchaseMode(TemplateImportVo templateImportVo) {
        String purchaseMode = templateImportVo.getPurchaseMode();
        PurchaseModeEnums purchaseModeEnums = PurchaseModeEnums.getByCnName(purchaseMode);
        if (StringUtils.isBlank(purchaseMode)) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.PURCHASE_MODE_EMPTY));
        } else if (purchaseModeEnums == null) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.PURCHASE_MODE_UNKNOWN));
        }
    }
    private void checkSupplier(TemplateImportVo templateImportVo) {
        String supplier = templateImportVo.getSupplier();
        if (StringUtils.isNotBlank(supplier) && supplier.length() > GlobalConstants.NAME_LENGTH_LIMIT) {
            setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                    I18nUtil.getI18n(GlobalConstants.SUPPLIER_TOO_LONG));
        }
    }
    private void checkExpirationDate(TemplateImportVo templateImportVo) {
        String expirationDate = templateImportVo.getExpirationDate();
        //失效日期可以为空
        if (StringUtils.isNotBlank(expirationDate)) {
            if (!DateTimeUtils.isValidDateRightFormat(expirationDate)) {
                setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                        I18nUtil.getI18n(GlobalConstants.EXPIRATION_DATE_FORMAT_ERROR));
            } else if (DateTimeUtils.getCurrentRightDate().compareTo(expirationDate) > 0) {
                setCheckResultAndErrorReason(templateImportVo, TemplateCheckResultEnum.FAIL.getCode(),
                        I18nUtil.getI18n(GlobalConstants.EXPIRATION_DATE_EXPIRED));
            }
        }
    }
    private void setCheckResultAndErrorReason(TemplateImportVo templateImportVo, String messageKey, String errorReason) {
        templateImportVo.setCheckResult(messageKey);
        String existingErrorReason = StringUtils.defaultIfBlank(templateImportVo.getErrorReason(), "");
        String separator = existingErrorReason.isEmpty() ? "" : GlobalConstants.SEMICOLON;
        String newReason = existingErrorReason + separator + errorReason;
        templateImportVo.setErrorReason(newReason);
    }

    /**
     * 从excel读取的数据去掉空格
     * @return 去掉空格后的字符串
     */
    private String getRealString(String str) {
        return str.replace("\u00A0", " ").trim();
    }

}
/* Ended by AICoder, pid:528e8n5e199dc7a146640ae892532e2d10b8895c */