DROP TABLE IF EXISTS product_group;
-- 创建 product_group 表
CREATE TABLE  product_group (
    id                  VARCHAR(64)       NOT NULL PRIMARY KEY,
    name                VARCHAR(50)       NOT NULL,
    pdm_model_spec      VARCHAR(64)       NOT NULL,
    product_category_id VARCHAR(64),
    parent_id           VARCHAR(64),
    path_name           TEXT,
    path_id             TEXT,
    group_level         INTEGER           NOT NULL,
    create_time         VARCHAR(32)       NOT NULL,
    update_time         VARCHAR(32)       NOT NULL,
    create_by           VARCHAR(64)       NOT NULL,
    update_by           VARCHAR(64)       NOT NULL
);
-- 添加 product_group 表注释
COMMENT ON TABLE product_group IS '产品分组表';

-- 添加 product_group 字段注释
COMMENT ON COLUMN product_group.id IS '分组ID';
COMMENT ON COLUMN product_group.name IS '分组名称';
COMMENT ON COLUMN product_group.pdm_model_spec IS 'PDM型号规格';
COMMENT ON COLUMN product_group.product_category_id IS '产品小类ID，第一层分组';
COMMENT ON COLUMN product_group.parent_id IS '分组父ID，第二层、第三层分组';
COMMENT ON COLUMN product_group.path_name IS '路径, path1/path2/path3';
COMMENT ON COLUMN product_group.path_id IS '路径ID，id1/id2/id3';
COMMENT ON COLUMN product_group.group_level IS '分组层级，1 第一层，2 第二层，3 第三层';
COMMENT ON COLUMN product_group.create_time IS '创建时间';
COMMENT ON COLUMN product_group.update_time IS '更新时间';
COMMENT ON COLUMN product_group.create_by IS '创建用户';
COMMENT ON COLUMN product_group.update_by IS '更新用户';

-- 添加自引用外键约束注释
--创建触发器函数以防止插入或更新为空字符串的 parent_id
CREATE OR REPLACE FUNCTION check_parent_id_not_empty()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.parent_id = '' THEN
        RAISE EXCEPTION 'Parent ID cannot be an empty string';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
-- 创建触发器
CREATE TRIGGER trg_check_parent_id_not_empty
BEFORE INSERT OR UPDATE OF parent_id ON product_group
FOR EACH ROW EXECUTE PROCEDURE check_parent_id_not_empty();

DROP TABLE IF EXISTS product_category;
CREATE TABLE product_category
(
  id text NOT NULL,
  product_name text NOT NULL,
  description text ,
  product_line_no text ,
  product_line_name text ,
  parent_id text ,
  path_name text ,
  path_id text ,
  node_type INTEGER NOT NULL ,
  sort_order INTEGER DEFAULT 0,
  create_time text NOT NULL ,
  update_time text NOT NULL ,
  create_by text,
  update_by text,
  PRIMARY KEY (id),
    CONSTRAINT fk_parent_id
          FOREIGN KEY (parent_id)
          REFERENCES product_category(id)
          ON DELETE SET NULL
);

COMMENT ON TABLE product_category IS '产品分类表';

COMMENT ON COLUMN product_category.id IS 'id';
COMMENT ON COLUMN product_category.product_name IS '产品分类名称';
COMMENT ON COLUMN product_category.description IS '描述';
COMMENT ON COLUMN product_category.product_line_no IS 'pdm产品线编号';
COMMENT ON COLUMN product_category.product_line_name IS 'pdm产品线名称';
COMMENT ON COLUMN product_category.parent_id IS '父id';
COMMENT ON COLUMN product_category.path_name IS '路径名';
COMMENT ON COLUMN product_category.path_id IS '路径id';
COMMENT ON COLUMN product_category.node_type IS '节点类型1-产品线,2-产品大类,3-产品小类';
COMMENT ON COLUMN product_category.sort_order IS '定义产品分类的显示顺序';
COMMENT ON COLUMN product_category.create_time IS '创建时间';
COMMENT ON COLUMN product_category.update_time IS '更新时间';

-- 创建触发器
CREATE TRIGGER trg_check_parent_id_not_empty
BEFORE INSERT OR UPDATE OF parent_id ON product_category
FOR EACH ROW EXECUTE PROCEDURE check_parent_id_not_empty();

DROP TABLE IF EXISTS product_category_extra_info;
CREATE TABLE product_category_extra_info
(
  id text NOT NULL,
  product_category_id text NOT NULL,
  product_level text NOT NULL,
  product_component text NOT NULL,
  non_standard_items text ,
  extended_warranty_factor DECIMAL ,
  product_no text,
  material_type text NULL,
  PRIMARY KEY (id)
);

COMMENT ON TABLE product_category_extra_info IS '产品额外信息表';

COMMENT ON COLUMN product_category_extra_info.id IS 'id';
COMMENT ON COLUMN product_category_extra_info.product_category_id IS '产品分类表关联id';
COMMENT ON COLUMN product_category_extra_info.product_level IS '产品梯队';
COMMENT ON COLUMN product_category_extra_info.product_component IS '产品组成';
COMMENT ON COLUMN product_category_extra_info.non_standard_items IS '可非标项';
COMMENT ON COLUMN product_category_extra_info.extended_warranty_factor IS '延保系数';
COMMENT ON COLUMN product_category_extra_info.product_no IS '产品编号';
COMMENT ON COLUMN product_category_extra_info.material_type IS '材料类别';


DROP TABLE IF EXISTS pdm_info;
CREATE TABLE pdm_info
(
  id text NOT NULL,
  name text NULL,
  sales_code text NOT NULL UNIQUE,
  production_code text NULL,
  sales_status text,
  unit text,
  description text,
  PRIMARY KEY (id)
);

COMMENT ON TABLE pdm_info IS 'pdm信息表';

COMMENT ON COLUMN pdm_info.id IS 'id';
COMMENT ON COLUMN pdm_info.name IS 'pdm名称';
COMMENT ON COLUMN pdm_info.sales_code IS '销售代码';
COMMENT ON COLUMN pdm_info.production_code IS '生产代码';
COMMENT ON COLUMN pdm_info.sales_status IS '销售状态';
COMMENT ON COLUMN pdm_info.unit IS '单位';
COMMENT ON COLUMN pdm_info.description IS '描述';

DROP TABLE IF EXISTS material;
CREATE TABLE material
(
  id text NOT NULL,
  approval_id text,
  name text NOT NULL,
  brand text ,
  supplier text ,
  specification_model text ,
  service text ,
  purchase_mode text NOT NULL,
  expiration_date text,
  warranty_period text ,
  cost text,
  delivery_days text ,
  group_id text NOT NULL,
  pdm_info_id text NULL,
  oth_info_id text NULL,
  material_status text NOT NULL,
  recommended_level text,
  unit text,
  description text,
  version text,
  create_by text,
  update_by text,
  create_time text,
  update_time text,
  specification_remark text,
  PRIMARY KEY (id)
);

COMMENT ON TABLE material IS '物料表';

COMMENT ON COLUMN material.id IS '物料id';
COMMENT ON COLUMN material.approval_id IS '审批id';
COMMENT ON COLUMN material.name IS '物料名称';
COMMENT ON COLUMN material.brand IS '品牌';
COMMENT ON COLUMN material.supplier IS '供应商';
COMMENT ON COLUMN material.specification_model IS '规格型号';
COMMENT ON COLUMN material.service IS '服务';
COMMENT ON COLUMN material.purchase_mode IS '采购模式';
COMMENT ON COLUMN material.expiration_date IS '失效日期';
COMMENT ON COLUMN material.warranty_period IS '保质期';
COMMENT ON COLUMN material.cost IS '成本费用';
COMMENT ON COLUMN material.delivery_days IS '交期';
COMMENT ON COLUMN material.group_id IS '分组id';
COMMENT ON COLUMN material.pdm_info_id IS 'pdm的id';
COMMENT ON COLUMN material.oth_info_id IS '非pdm的id';
COMMENT ON COLUMN material.material_status IS '物料状态';
COMMENT ON COLUMN material.recommended_level IS '推荐等级';
COMMENT ON COLUMN material.unit IS '单位';
COMMENT ON COLUMN material.description IS '描述';
COMMENT ON COLUMN material.version IS '版本';
COMMENT ON COLUMN material.create_by IS '创建者';
COMMENT ON COLUMN material.update_by IS '更新者';
COMMENT ON COLUMN material.create_time IS '创建时间';
COMMENT ON COLUMN material.update_time IS '更新时间';
COMMENT ON COLUMN material.specification_remark IS '物料规格书备注';

DROP TABLE IF EXISTS material_historical;
CREATE TABLE material_historical
(
  id text NOT NULL,
  material_id text NOT NULL,
  approval_id text NULL,
  name text NOT NULL,
  brand text ,
  supplier text ,
  purchase_mode text NOT NULL,
  expiration_date text,
  warranty_period text ,
  cost text,
  delivery_days text ,
  group_id text NOT NULL,
  pdm_info_id text NULL,
  recommended_level text,
  unit text,
  description text,
  version text,
  production_code text,
  sales_status text,
  submitter text ,
  approval_time text ,
  change_reason text ,
  specification_model text ,
  service text ,
  create_by text,
  update_by text,
  create_time text,
  update_time text,
  PRIMARY KEY (id)
);

COMMENT ON TABLE material_historical IS '物料历史表';

COMMENT ON COLUMN material_historical.id IS '历史id';
COMMENT ON COLUMN material_historical.approval_id IS '审批id';
COMMENT ON COLUMN material_historical.material_id IS '物料id';
COMMENT ON COLUMN material_historical.name IS '物料名称';
COMMENT ON COLUMN material_historical.brand IS '品牌';
COMMENT ON COLUMN material_historical.supplier IS '供应商';
COMMENT ON COLUMN material_historical.purchase_mode IS '采购模式';
COMMENT ON COLUMN material_historical.expiration_date IS '失效日期';
COMMENT ON COLUMN material_historical.warranty_period IS '保质期';
COMMENT ON COLUMN material_historical.cost IS '成本费用';
COMMENT ON COLUMN material_historical.delivery_days IS '交期';
COMMENT ON COLUMN material_historical.group_id IS '分组id';
COMMENT ON COLUMN material_historical.pdm_info_id IS 'pdm的id';
COMMENT ON COLUMN material_historical.recommended_level IS '推荐等级';
COMMENT ON COLUMN material_historical.unit IS '单位';
COMMENT ON COLUMN material_historical.description IS '描述';
COMMENT ON COLUMN material_historical.version IS '版本';
COMMENT ON COLUMN material_historical.submitter IS '发起人';
COMMENT ON COLUMN material_historical.approval_time IS '审批通过时间';
COMMENT ON COLUMN material_historical.change_reason IS '版本变更原因';
COMMENT ON COLUMN material_historical.specification_model IS '规格型号';
COMMENT ON COLUMN material_historical.service IS '服务';
COMMENT ON COLUMN material_historical.create_by IS '创建者';
COMMENT ON COLUMN material_historical.update_by IS '更新者';
COMMENT ON COLUMN material_historical.create_time IS '创建时间';
COMMENT ON COLUMN material_historical.update_time IS '更新时间';
COMMENT ON COLUMN material_historical.production_code IS '生产代码';
COMMENT ON COLUMN material_historical.sales_status IS '销售状态';

DROP TABLE IF EXISTS material_temporary;
CREATE TABLE material_temporary
(
  id text NOT NULL,
  material_id text NOT NULL,
  approval_id text,
  name text NOT NULL,
  brand text ,
  supplier text ,
  specification_model text ,
  service text ,
  purchase_mode text NOT NULL,
  expiration_date text,
  warranty_period text ,
  cost text,
  delivery_days text ,
  group_id text NOT NULL,
  pdm_info_id text NOT NULL,
  recommended_level text,
  unit text,
  description text,
  version text,
  document_ids text[],
  create_by text,
  update_by text,
  create_time text,
  update_time text,
  specification_remark text,
  PRIMARY KEY (id)
);

COMMENT ON TABLE material_temporary IS '临时物料表';

COMMENT ON COLUMN material_temporary.id IS '临时id';
COMMENT ON COLUMN material_temporary.material_id IS '物料id';
COMMENT ON COLUMN material_temporary.approval_id IS '审批id';
COMMENT ON COLUMN material_temporary.name IS '物料名称';
COMMENT ON COLUMN material_temporary.brand IS '品牌';
COMMENT ON COLUMN material_temporary.supplier IS '供应商';
COMMENT ON COLUMN material_temporary.specification_model IS '规格型号';
COMMENT ON COLUMN material_temporary.service IS '服务';
COMMENT ON COLUMN material_temporary.purchase_mode IS '采购模式';
COMMENT ON COLUMN material_temporary.expiration_date IS '失效日期';
COMMENT ON COLUMN material_temporary.warranty_period IS '保质期';
COMMENT ON COLUMN material_temporary.cost IS '成本费用';
COMMENT ON COLUMN material_temporary.delivery_days IS '交期';
COMMENT ON COLUMN material_temporary.group_id IS '分组id';
COMMENT ON COLUMN material_temporary.pdm_info_id IS 'pdm的id';
COMMENT ON COLUMN material_temporary.recommended_level IS '推荐等级';
COMMENT ON COLUMN material_temporary.unit IS '单位';
COMMENT ON COLUMN material_temporary.description IS '描述';
COMMENT ON COLUMN material_temporary.version IS '版本';
COMMENT ON COLUMN material_temporary.document_ids IS '文档ids';
COMMENT ON COLUMN material_temporary.create_by IS '创建者';
COMMENT ON COLUMN material_temporary.update_by IS '更新者';
COMMENT ON COLUMN material_temporary.create_time IS '创建时间';
COMMENT ON COLUMN material_temporary.update_time IS '更新时间';
COMMENT ON COLUMN material_temporary.specification_remark IS '物料规格书备注';


-- 创建 product_brand 表
DROP TABLE IF EXISTS product_brand;
CREATE TABLE product_brand (
    id text PRIMARY KEY, -- 主键，唯一标识一个品牌。
    product_category_id text, -- 所属产品小类ID，关联到具体的产品。
    brand_name text, -- 品牌名称，描述品牌的名称。
    selection_attribute int, -- 选型属性，0表示优选，1表示其它。
    selection_score numeric(3, 1), -- 选型评分，范围在1-10之间，允许一位小数。
    procurement_mode text, -- 采购模式，1:自研、2:战略采购、3:项目采购、4:框架采购。
    expiry_date text, -- 失效日期，格式为YYYY-MM-DD。
    quote_id text, -- 被引用的业务ID，关联到具体的业务。
    create_time text, -- 创建时间，记录品牌创建的时间。
    update_time text, -- 更新时间，记录品牌最后更新的时间。
    create_by text, -- 创建用户ID，记录创建品牌的用户。
    update_by text -- 更新用户ID，记录最后更新品牌的用户。
);

-- 添加字段注释
COMMENT ON COLUMN product_brand.id IS '主键，唯一标识一个品牌。';
COMMENT ON COLUMN product_brand.product_category_id IS '所属产品小类ID，关联到具体的产品。';
COMMENT ON COLUMN product_brand.brand_name IS '品牌名称，描述品牌的名称。';
COMMENT ON COLUMN product_brand.selection_attribute IS '选型属性，0表示优选，1表示其它。';
COMMENT ON COLUMN product_brand.selection_score IS '选型评分，范围在1-10之间，允许一位小数。';
COMMENT ON COLUMN product_brand.procurement_mode IS '采购模式，1:自研、2:战略采购、3:项目采购、4:框架采购。';
COMMENT ON COLUMN product_brand.expiry_date IS '失效日期，格式为YYYY-MM-DD。';
COMMENT ON COLUMN product_brand.quote_id IS '被引用的业务ID，关联到具体的业务。';
COMMENT ON COLUMN product_brand.create_time IS '创建时间，记录品牌创建的时间，默认当前时间。';
COMMENT ON COLUMN product_brand.update_time IS '更新时间，记录品牌最后更新的时间，默认当前时间。';
COMMENT ON COLUMN product_brand.create_by IS '创建用户ID，记录创建品牌的用户。';
COMMENT ON COLUMN product_brand.update_by IS '更新用户ID，记录最后更新品牌的用户。';

-- 添加检查约束，确保 selection_score 在 1.0 和 10.0 之间（含）
ALTER TABLE product_brand ADD CONSTRAINT check_selection_score_range
CHECK (selection_score >= 1.0 AND selection_score <= 10.0);

-- 创建 product_brand_tag 表
DROP TABLE IF EXISTS product_brand_tag;
CREATE TABLE product_brand_tag (
    id text PRIMARY KEY, -- 主键，唯一标识一个品牌标签。
    tag_name text NOT NULL, -- 标签名称，描述品牌标签的具体名称。
    brand_id text NOT NULL, -- 品牌ID，关联到具体的品牌。
    tag_order int NOT NULL DEFAULT 0 -- 标签排序。
);

-- 添加字段注释
COMMENT ON COLUMN product_brand_tag.id IS '主键，唯一标识一个品牌标签。';
COMMENT ON COLUMN product_brand_tag.tag_name IS '标签名称，描述品牌标签的具体名称。';
COMMENT ON COLUMN product_brand_tag.brand_id IS '品牌ID，关联到具体的品牌。';
COMMENT ON COLUMN product_brand_tag.tag_order IS '标签排序。';

DROP TABLE IF EXISTS oth_info;
CREATE TABLE oth_info
(
  id text NOT NULL,
  name text NOT NULL UNIQUE,
  sales_code text NULL,
  production_code text NULL,
  sales_status text,
  unit text,
  description text,
  PRIMARY KEY (id)
);

COMMENT ON TABLE oth_info IS '非pdm信息表';

COMMENT ON COLUMN oth_info.id IS 'id';
COMMENT ON COLUMN oth_info.name IS '自定义名称';
COMMENT ON COLUMN oth_info.sales_code IS '销售代码';
COMMENT ON COLUMN oth_info.production_code IS '生产代码';
COMMENT ON COLUMN oth_info.sales_status IS '销售状态';
COMMENT ON COLUMN oth_info.unit IS '单位';
COMMENT ON COLUMN oth_info.description IS '描述';

DROP TABLE IF EXISTS product_core_param;
CREATE TABLE product_core_param (
     id TEXT NOT NULL PRIMARY KEY,  -- 主键
     product_category_id TEXT NOT NULL,  -- 产品小类id
     param_group_L1 TEXT,  -- 产品分组L1
     param_group_L2 TEXT,  -- 产品分组L2
     main_param TEXT NOT NULL,  -- 主要性能参数
     param_remark TEXT,  -- 参数备注
     del_flag INT NOT NULL DEFAULT 0,  -- 删除标志：0未删除；1删除
     create_time varchar NOT NULL,  -- 创建时间
     update_time varchar NOT NULL,  -- 更新时间
     create_by TEXT NOT NULL,  -- 创建用户
     update_by TEXT NOT NULL  -- 更新用户
);

-- 添加注释（可选）
COMMENT ON COLUMN product_core_param.id IS 'ID';
COMMENT ON COLUMN product_core_param.product_category_id IS '产品小类id';
COMMENT ON COLUMN product_core_param.param_group_L1 IS '产品分组L1';
COMMENT ON COLUMN product_core_param.param_group_L2 IS '产品分组L2';
COMMENT ON COLUMN product_core_param.main_param IS '主要性能参数';
COMMENT ON COLUMN product_core_param.param_remark IS '参数备注';
COMMENT ON COLUMN product_core_param.del_flag IS '0未删除；1删除';
COMMENT ON COLUMN product_core_param.create_time IS '创建时间';
COMMENT ON COLUMN product_core_param.update_time IS '更新时间';
COMMENT ON COLUMN product_core_param.create_by IS '创建用户';
COMMENT ON COLUMN product_core_param.update_by IS '更新用户';

DROP TABLE IF EXISTS product_group_custom_sort;
-- 创建 product_group_custom_sort 表
CREATE TABLE  product_group_custom_sort (
    id                  VARCHAR(64)       NOT NULL PRIMARY KEY,
    parent_id           VARCHAR(64),
    group_id            VARCHAR(64)       NOT NULL,
    serial_num          INTEGER           NOT NULL,
    create_time         VARCHAR(32)       NOT NULL,
    update_time         VARCHAR(32)       NOT NULL,
    create_by           VARCHAR(64)       NOT NULL,
    update_by           VARCHAR(64)       NOT NULL
);
-- 添加 product_group_custom_sort 表注释
COMMENT ON TABLE product_group_custom_sort IS '产品分组自定义排序表';

-- 添加 product_group_custom_sort 字段注释
COMMENT ON COLUMN product_group_custom_sort.id IS '自定义排序ID主键';
COMMENT ON COLUMN product_group_custom_sort.parent_id IS '分组父级id';
COMMENT ON COLUMN product_group_custom_sort.group_id IS '分组id';
COMMENT ON COLUMN product_group_custom_sort.serial_num IS '序号';
COMMENT ON COLUMN product_group_custom_sort.create_time IS '创建时间';
COMMENT ON COLUMN product_group_custom_sort.update_time IS '更新时间';
COMMENT ON COLUMN product_group_custom_sort.create_by IS '创建用户';
COMMENT ON COLUMN product_group_custom_sort.update_by IS '更新用户';

DROP TABLE IF EXISTS demand_management;
CREATE TABLE demand_management (
    id TEXT NOT NULL, -- 主键ID
    project_id TEXT NOT NULL, -- 项目/商机id
    bill_quantity_id TEXT NOT NULL, -- 工程量清单id
    product_category_id TEXT NOT NULL, -- 产品小类id
    create_user TEXT NOT NULL, -- 创建人
    exp_time_complet TEXT , -- 期望完成时间
    act_time_complet TEXT , -- 实际完成时间
    processor TEXT NOT NULL, -- 处理人
    demand_type TEXT NOT NULL, -- 状态
    create_by TEXT NOT NULL, -- 创建用户ID
    create_time TEXT NOT NULL, -- 创建时间
    update_by TEXT NOT NULL, -- 更新用户ID
    update_time TEXT NOT NULL, -- 更新时间
    bill_quantitie_time TEXT NULL -- 工程量清单条目发生变化时间
);

-- 添加注释
COMMENT ON TABLE demand_management IS '需求管理表';
COMMENT ON COLUMN demand_management.id IS '主键ID';
COMMENT ON COLUMN demand_management.project_id IS '项目/商机id';
COMMENT ON COLUMN demand_management.bill_quantity_id IS '工程量清单id';
COMMENT ON COLUMN demand_management.product_category_id IS '产品小类id';
COMMENT ON COLUMN demand_management.create_user IS '创建人';
COMMENT ON COLUMN demand_management.exp_time_complet IS '期望完成时间';
COMMENT ON COLUMN demand_management.act_time_complet IS '实际完成时间';
COMMENT ON COLUMN demand_management.processor IS '处理人';
COMMENT ON COLUMN demand_management.demand_type IS '状态';
COMMENT ON COLUMN demand_management.create_by IS '创建用户ID';
COMMENT ON COLUMN demand_management.create_time IS '创建时间';
COMMENT ON COLUMN demand_management.update_by IS '更新用户ID';
COMMENT ON COLUMN demand_management.update_time IS '更新时间';
COMMENT ON COLUMN demand_management.bill_quantitie_time IS '工程量清单条目发生变化时间';

DROP TABLE IF EXISTS procurement_cost;
CREATE TABLE  procurement_cost (
    id TEXT NOT NULL PRIMARY KEY,
    lectotype_id TEXT NOT NULL,
    lectotype_type TEXT NOT NULL,
    negotiated_price TEXT ,
    datum_target_price TEXT ,
    challenge_target_price TEXT ,
    open_tender_price TEXT ,
    set_bid_price TEXT ,
    bid_issuing_time TEXT ,
    bid_opening_time TEXT ,
    create_time TEXT NOT NULL,
    update_time TEXT NOT NULL,
    create_by TEXT NOT NULL,
    update_by TEXT NOT NULL
);
-- 添加 procurement_cost 表注释
COMMENT ON TABLE procurement_cost IS '招采成本表';
-- 添加 procurement_cost 字段注释
COMMENT ON COLUMN procurement_cost.id IS '自定义排序ID主键';
COMMENT ON COLUMN procurement_cost.lectotype_id IS '归属选型单id';
COMMENT ON COLUMN procurement_cost.lectotype_type IS '归属选型单类型';
COMMENT ON COLUMN procurement_cost.negotiated_price IS '洽谈价';
COMMENT ON COLUMN procurement_cost.datum_target_price IS '基准目标价';
COMMENT ON COLUMN procurement_cost.challenge_target_price IS '挑战目标价';
COMMENT ON COLUMN procurement_cost.open_tender_price IS '开标价';
COMMENT ON COLUMN procurement_cost.set_bid_price IS '定标价';
COMMENT ON COLUMN procurement_cost.bid_issuing_time IS '发标时间';
COMMENT ON COLUMN procurement_cost.bid_opening_time IS '开标时间';
COMMENT ON COLUMN procurement_cost.create_time IS '创建时间';
COMMENT ON COLUMN procurement_cost.update_time IS '更新时间';
COMMENT ON COLUMN procurement_cost.create_by IS '创建用户';
COMMENT ON COLUMN procurement_cost.update_by IS '更新用户';

DROP TABLE IF EXISTS procurement_price_history;
CREATE TABLE  procurement_price_history (
    id TEXT NOT NULL PRIMARY KEY,
    lectotype_id TEXT NOT NULL,
    lectotype_type TEXT NOT NULL,
    price_category TEXT ,
    price TEXT ,
    create_time TEXT NOT NULL,
    update_time TEXT NOT NULL,
    create_by TEXT NOT NULL,
    update_by TEXT NOT NULL,
    remark TEXT NULL
);
-- 添加 procurement_cost 表注释
COMMENT ON TABLE procurement_price_history IS '招采成本价格历史表';
-- 添加 procurement_cost 字段注释
COMMENT ON COLUMN procurement_price_history.id IS '自定义排序ID主键';
COMMENT ON COLUMN procurement_price_history.lectotype_id IS '归属选型单id';
COMMENT ON COLUMN procurement_price_history.lectotype_type IS '归属选型单类型';
COMMENT ON COLUMN procurement_price_history.price_category IS '价格类型';
COMMENT ON COLUMN procurement_price_history.price IS '价格';
COMMENT ON COLUMN procurement_price_history.create_time IS '创建时间';
COMMENT ON COLUMN procurement_price_history.update_time IS '更新时间';
COMMENT ON COLUMN procurement_price_history.create_by IS '创建用户';
COMMENT ON COLUMN procurement_price_history.update_by IS '更新用户';
COMMENT ON COLUMN procurement_price_history.remark IS '备注';

-- Started by AICoder, pid:le897ne2226c6311431a0a5710de203ec928d511
 DROP TABLE IF EXISTS demand_management;
 CREATE TABLE demand_management (
    id TEXT NOT NULL, -- 主键ID
    project_id TEXT NOT NULL, -- 项目/商机id
    bill_quantity_id TEXT NOT NULL, -- 工程量清单id
    product_category_id TEXT NOT NULL, -- 产品小类id
    create_user TEXT NOT NULL, -- 创建人
    exp_time_complet TEXT , -- 期望完成时间
    act_time_complet TEXT , -- 实际完成时间
    processor TEXT NOT NULL, -- 处理人
    demand_type TEXT NOT NULL, -- 状态
    create_by TEXT NOT NULL, -- 创建用户ID
    create_time TEXT NOT NULL, -- 创建时间
    update_by TEXT NOT NULL, -- 更新用户ID
    update_time TEXT NOT NULL -- 更新时间
);

-- 添加注释
COMMENT ON TABLE demand_management IS '需求管理表';
COMMENT ON COLUMN demand_management.id IS '主键ID';
COMMENT ON COLUMN demand_management.project_id IS '项目/商机id';
COMMENT ON COLUMN demand_management.bill_quantity_id IS '工程量清单id';
COMMENT ON COLUMN demand_management.product_category_id IS '产品小类id';
COMMENT ON COLUMN demand_management.create_user IS '创建人';
COMMENT ON COLUMN demand_management.exp_time_complet IS '期望完成时间';
COMMENT ON COLUMN demand_management.act_time_complet IS '实际完成时间';
COMMENT ON COLUMN demand_management.processor IS '处理人';
COMMENT ON COLUMN demand_management.demand_type IS '状态';
COMMENT ON COLUMN demand_management.create_by IS '创建用户ID';
COMMENT ON COLUMN demand_management.create_time IS '创建时间';
COMMENT ON COLUMN demand_management.update_by IS '更新用户ID';
COMMENT ON COLUMN demand_management.update_time IS '更新时间';



DROP TABLE IF EXISTS demand_management_lectotype;

-- 创建 demand_management_lectotype 表
CREATE TABLE demand_management_lectotype (
    lectotype_id   TEXT      NOT NULL PRIMARY KEY,
    demand_id      TEXT      NOT NULL,
    lectotype_name TEXT      NOT NULL,
    lectotype_type TEXT      NOT NULL,
    lectotype_status INTEGER,
    send_bid_time  TEXT,
    end_time       TEXT,
    open_bid_time  TEXT,
    bid_url        TEXT,
    file_ids       TEXT,
    create_by      TEXT,
    create_time    TEXT,
    update_by      TEXT,
    update_user      TEXT,
    update_time    TEXT
);

-- 添加 demand_management_lectotype 表注释
COMMENT ON TABLE demand_management_lectotype IS '需求选型单';

-- 添加 demand_management_lectotype 字段注释
COMMENT ON COLUMN demand_management_lectotype.lectotype_id IS '需求选型ID';
COMMENT ON COLUMN demand_management_lectotype.demand_id IS '需求ID';
COMMENT ON COLUMN demand_management_lectotype.lectotype_name IS '选型名称';
COMMENT ON COLUMN demand_management_lectotype.lectotype_type IS '选型类型';
COMMENT ON COLUMN demand_management_lectotype.lectotype_status IS '选型状态';
COMMENT ON COLUMN demand_management_lectotype.send_bid_time IS '发标时间';
COMMENT ON COLUMN demand_management_lectotype.end_time IS '完成时间';
COMMENT ON COLUMN demand_management_lectotype.open_bid_time IS '开标时间';
COMMENT ON COLUMN demand_management_lectotype.bid_url IS '招采申请url';
COMMENT ON COLUMN demand_management_lectotype.file_ids IS '文件id列表';
COMMENT ON COLUMN demand_management_lectotype.create_by IS '创建用户';
COMMENT ON COLUMN demand_management_lectotype.create_time IS '创建时间';
COMMENT ON COLUMN demand_management_lectotype.update_by IS '更新用户';
COMMENT ON COLUMN demand_management_lectotype.update_time IS '更新时间';

-- Ended by AICoder, pid:le897ne2226c6311431a0a5710de203ec928d511

-- Started by AICoder, pid:vb9f0c68b8ue3ef14f0d0bdd30e586111397f452
DROP TABLE IF EXISTS demand_management_lectotype_material;
CREATE TABLE demand_management_lectotype_material (
    lectotype_id   TEXT       NOT NULL,
    material_id    TEXT       NOT NULL,
    create_time    TEXT,
    PRIMARY KEY (lectotype_id, material_id)
);
COMMENT ON TABLE demand_management_lectotype_material IS '需求选型单关联物料';
COMMENT ON COLUMN demand_management_lectotype_material.lectotype_id IS '需求选型ID';
COMMENT ON COLUMN demand_management_lectotype_material.material_id IS '物料ID';
COMMENT ON COLUMN demand_management_lectotype_material.create_time IS '创建时间';
-- Ended by AICoder, pid:vb9f0c68b8ue3ef14f0d0bdd30e586111397f452

-- 20250423
ALTER TABLE public.material ADD name_en text NULL;
COMMENT ON COLUMN public.material.name_en IS '物料名称英文';
ALTER TABLE public.material ADD unit_en text NULL;
COMMENT ON COLUMN public.material.unit_en IS '单位名称英文';
ALTER TABLE public.material_historical  ADD name_en text NULL;
COMMENT ON COLUMN public.material_historical.name_en IS '物料名称英文';
ALTER TABLE public.material_historical ADD unit_en text NULL;
COMMENT ON COLUMN public.material_historical.unit_en IS '单位名称英文';
ALTER TABLE public.material_temporary  ADD name_en text NULL;
COMMENT ON COLUMN public.material_temporary.name_en IS '物料名称英文';
ALTER TABLE public.material_temporary ADD unit_en text NULL;
COMMENT ON COLUMN public.material_temporary.unit_en IS '单位名称英文';

 DROP TABLE IF EXISTS requirement_dashboard;
CREATE TABLE requirement_dashboard (
    id TEXT NOT NULL,  -- 主表id
    product_category_id TEXT NOT NULL,
    project_id TEXT NOT NULL,
    resource_confirmation_mode TEXT[],
    demand_confirmation_time TEXT,
    expect_bid_open_time TEXT,
    status INTEGER DEFAULT 0,
    tender_launch_time TEXT,
    bidding_close_time TEXT,
    actual_bid_opening_time TEXT,
    code_status TEXT,
    remarks TEXT,
    bidding_brand text NULL,
    create_by TEXT NOT NULL,
    create_time TEXT NOT NULL,
    update_by TEXT NOT NULL,
    update_time TEXT NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT unique_product_category_per_project UNIQUE (product_category_id, project_id)  -- 项目下产品小类只有一个
);
-- 添加注释
COMMENT ON TABLE requirement_dashboard IS '需求看板表';
COMMENT ON COLUMN requirement_dashboard.id IS '主键ID';
COMMENT ON COLUMN requirement_dashboard.project_id IS '项目/商机id';
COMMENT ON COLUMN requirement_dashboard.product_category_id IS '产品小类id';
COMMENT ON COLUMN requirement_dashboard.resource_confirmation_mode IS '资源确认模式：可多个：招标、自研、战采、框标、独家、生态、取消';
COMMENT ON COLUMN requirement_dashboard.demand_confirmation_time IS ' 需求确认时间';
COMMENT ON COLUMN requirement_dashboard.expect_bid_open_time IS '期望招标开标时间';
COMMENT ON COLUMN requirement_dashboard.status IS '状态：0-正常，1-取消，默认正常';
COMMENT ON COLUMN requirement_dashboard.tender_launch_time IS '招标TS发起时间';
COMMENT ON COLUMN requirement_dashboard.bidding_close_time IS '招标TS关闭时间';
COMMENT ON COLUMN requirement_dashboard.actual_bid_opening_time IS '实际开标时间';
COMMENT ON COLUMN requirement_dashboard.code_status IS '代码状态：1-待提交审批，2-上架审批中，3-已上架';
COMMENT ON COLUMN requirement_dashboard.remarks IS '备注';
COMMENT ON COLUMN requirement_dashboard.bidding_brand IS '投标品牌';
COMMENT ON COLUMN requirement_dashboard.create_by IS '创建用户ID';
COMMENT ON COLUMN requirement_dashboard.create_time IS '创建时间';
COMMENT ON COLUMN requirement_dashboard.update_by IS '更新用户ID';
COMMENT ON COLUMN requirement_dashboard.update_time IS '更新时间';

-- 20250523
DROP TABLE IF EXISTS strong_echo;
CREATE TABLE strong_echo (
	id text NOT NULL,
	category_id text NOT NULL, -- 分类ID
	title text NULL, -- 标题
	create_time text NULL, -- 创建时间
	update_time text NULL, -- 更新时间
	create_by text NULL, -- 创建人
	update_by text NULL, -- 更新人
	"content" text NULL, -- 内容
	contact text NULL -- 需通知的用户ID，多个英文逗号分隔
);
COMMENT ON TABLE strong_echo IS '回音强';

COMMENT ON COLUMN strong_echo.category_id IS '分类ID';
COMMENT ON COLUMN strong_echo.title IS '标题';
COMMENT ON COLUMN strong_echo.create_time IS '创建时间';
COMMENT ON COLUMN strong_echo.update_time IS '更新时间';
COMMENT ON COLUMN strong_echo.create_by IS '创建人';
COMMENT ON COLUMN strong_echo.update_by IS '更新人';
COMMENT ON COLUMN strong_echo."content" IS '内容';
COMMENT ON COLUMN strong_echo.contact IS '需通知的用户ID，多个英文逗号分隔';

DROP TABLE IF EXISTS strong_echo_comment;
CREATE TABLE strong_echo_comment (
	id text NOT NULL,
	echo_id text NOT NULL, -- 问题ID
	"comment" text NULL, -- 评论内容
	create_time text NULL, -- 创建时间
	update_time text NULL,
	create_by text NULL,
	update_by text NULL, -- 更新时间
	p_id text NULL, -- 回复评论id
	contact text NULL -- 通知人ID,英文逗号分隔
);
COMMENT ON COLUMN strong_echo_comment.echo_id IS '问题ID';
COMMENT ON COLUMN strong_echo_comment."comment" IS '评论内容';
COMMENT ON COLUMN strong_echo_comment.create_time IS '创建时间';
COMMENT ON COLUMN strong_echo_comment.update_by IS '更新时间';
COMMENT ON COLUMN strong_echo_comment.p_id IS '回复评论id';
COMMENT ON COLUMN strong_echo_comment.contact IS '通知人ID,英文逗号分隔';