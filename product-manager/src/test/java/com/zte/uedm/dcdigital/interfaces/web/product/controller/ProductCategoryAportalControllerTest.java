package com.zte.uedm.dcdigital.interfaces.web.product.controller;

/* Started by AICoder, pid:sc48am37816f59914d680a13b187724840010112 */
import com.github.pagehelper.PageInfo;



import com.zte.uedm.dcdigital.application.category.ProductCategoryCommandService;
import com.zte.uedm.dcdigital.application.category.ProductCategoryQueryService;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;

import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductCategoryAportalControllerTest {

    @InjectMocks
    private ProductCategoryAportalController productCategoryAportalController;

    @Mock
    private ProductCategoryCommandService productCategoryService;
    @Mock
    private ProductCategoryQueryService productCategoryQueryService;

    private ProductCategoryQueryDto queryDto;
    private ProductCategoryAddDto addDto;
    private ProductCategoryEditDto editDto;

    @Before
    public void setUp() {
        queryDto = new ProductCategoryQueryDto();
        addDto = new ProductCategoryAddDto();
        editDto = new ProductCategoryEditDto();
    }

    @Test
    public void testQueryProductCategory() throws BusinessException {
        PageInfo<ProductCategoryVo> pageInfo = new PageInfo<>(Collections.singletonList(new ProductCategoryVo()));
        when(productCategoryQueryService.queryProductCategory(any(ProductCategoryQueryDto.class))).thenReturn(pageInfo);
        queryDto.setNodeType(1);
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        BaseResult response = productCategoryAportalController.queryProductCategory(queryDto);
        assertEquals("0", String.valueOf(response.getCode()));
    }

    @Test
    public void testQueryProductCategory_Error() throws BusinessException {


        BaseResult response = productCategoryAportalController.queryProductCategory(queryDto);
        assertEquals(StatusCode.INVALID_PARAMETER.getCode(), response.getCode());
    }

    @Test
    public void testQueryProductCategoryById() throws BusinessException {
        ProductCategoryVo vo = new ProductCategoryVo();
        when(productCategoryQueryService.queryProductCategoryById(anyString())).thenReturn(vo);

        BaseResult response = productCategoryAportalController.queryProductCategoryById("1");
        assertEquals("0", String.valueOf(response.getCode()));
        assertEquals(vo, response.getData());
    }

    @Test
    public void testQueryProductCategoryById_EmptyId() {
        BaseResult response = productCategoryAportalController.queryProductCategoryById("");
        assertEquals(StatusCode.INVALID_PARAMETER.getCode(), response.getCode());
    }

    @Test
    public void testQueryProductCategoryById_Error() throws BusinessException {
        when(productCategoryQueryService.queryProductCategoryById(anyString())).thenThrow(new BusinessException(StatusCode.INVALID_PARAMETER));

        BaseResult response = productCategoryAportalController.queryProductCategoryById("1");
        assertEquals(StatusCode.INVALID_PARAMETER.getCode(), response.getCode());
    }

    @Test
    public void testAddProductCategory() throws BusinessException {
        doNothing().when(productCategoryService).addProductCategory(any(ProductCategoryAddDto.class));
        addDto.setProductName("111");
        addDto.setNodeType(1);
        BaseResult response = productCategoryAportalController.addProductCategory(addDto);
        assertEquals("0", String.valueOf(response.getCode()));
    }

    @Test
    public void testAddProductCategory_Error() throws BusinessException {

        BaseResult response = productCategoryAportalController.addProductCategory(addDto);
        assertEquals(StatusCode.INVALID_PARAMETER.getCode(), response.getCode());
    }

    @Test
    public void testEditProductCategory() throws BusinessException {
        doNothing().when(productCategoryService).editProductCategory(any(ProductCategoryEditDto.class));
        editDto.setProductName("123");
        editDto.setId("123");
        BaseResult response = productCategoryAportalController.editProductCategory(editDto);
        assertEquals("0", String.valueOf(response.getCode()));
    }

    @Test
    public void testEditProductCategory_Error() throws BusinessException {
        BaseResult response = productCategoryAportalController.editProductCategory(editDto);
        assertEquals(StatusCode.INVALID_PARAMETER.getCode(), response.getCode());
    }

    @Test
    public void testDeleteProductCategoryById() throws BusinessException {
        doNothing().when(productCategoryService).deleteProductCategoryById(anyString());

        BaseResult response = productCategoryAportalController.deleteProductCategoryById("1");
        assertEquals("0", String.valueOf(response.getCode()));
    }

    @Test
    public void testDeleteProductCategoryById_EmptyId() {
        BaseResult response = productCategoryAportalController.deleteProductCategoryById("");
        assertEquals(StatusCode.INVALID_PARAMETER.getCode(), response.getCode());
    }

    @Test
    public void testDeleteProductCategoryById_Error() throws BusinessException {
        doThrow(new BusinessException(StatusCode.INVALID_PARAMETER)).when(productCategoryService).deleteProductCategoryById(anyString());

        BaseResult response = productCategoryAportalController.deleteProductCategoryById("1");
        assertEquals(StatusCode.INVALID_PARAMETER.getCode(), response.getCode());
    }
}
/* Ended by AICoder, pid:sc48am37816f59914d680a13b187724840010112 */
