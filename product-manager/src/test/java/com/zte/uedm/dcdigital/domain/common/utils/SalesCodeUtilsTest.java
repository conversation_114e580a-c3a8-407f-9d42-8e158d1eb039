package com.zte.uedm.dcdigital.domain.common.utils;
/* Started by AICoder, pid:x828do80a5nc762149f60bedf066d143fbb0fb6a */
import static org.junit.Assert.*;

import org.junit.Test;

public class SalesCodeUtilsTest {

    private SalesCodeUtils salesCodeUtils = new SalesCodeUtils();

    @Test
    public void testIsPdmSalesCode_Null() {
        assertFalse(salesCodeUtils.isPdmSalesCode(null));
    }

    @Test
    public void testIsPdmSalesCode_EmptyString() {
        assertFalse(salesCodeUtils.isPdmSalesCode(""));
    }

    @Test
    public void testIsPdmSalesCode_Whitespace() {
        assertFalse(salesCodeUtils.isPdmSalesCode(" "));
    }

    @Test
    public void testIsPdmSalesCode_InvalidLength() {
        assertFalse(salesCodeUtils.isPdmSalesCode("1812345678")); // 少于12位
        assertFalse(salesCodeUtils.isPdmSalesCode("1812345678901")); // 多于12位
    }

    @Test
    public void testIsPdmSalesCode_NotStartingWith18() {
        assertFalse(salesCodeUtils.isPdmSalesCode("191234567890"));
    }

    @Test
    public void testIsPdmSalesCode_ValidPdmSalesCode() {
        assertTrue(salesCodeUtils.isPdmSalesCode("181234567890"));
    }
}
/* Ended by AICoder, pid:x828do80a5nc762149f60bedf066d143fbb0fb6a */