package com.zte.uedm.dcdigital.domain.common.errorCode;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.zte.uedm.dcdigital.domain.common.statuscode.ProductGroupStatusCode;
import org.junit.jupiter.api.Test;
public class ProductGroupStatusCodeTest {

    /* Started by AICoder, pid:hb5eeg4e39v1d6414c5d0826b0f86d6b17737786 */
    @Test
    public void given_NodeNotExist_when_GetCodeAndMessage_then_ReturnCorrectValues() {
        ProductGroupStatusCode statusCode = ProductGroupStatusCode.NODE_NOT_EXIST;
        assertNotNull(statusCode, "Status code should not be null");
        assertEquals(1101, statusCode.getCode(), "Code should match the expected value");
    }

    @Test
    public void given_NodeAddLevelExceedsLimit_when_GetCodeAndMessage_then_ReturnCorrectValues() {
        ProductGroupStatusCode statusCode = ProductGroupStatusCode.NODE_ADD_LEVEL_EXCEEDS_LIMIT;
        assertNotNull(statusCode, "Status code should not be null");
        assertEquals(1102, statusCode.getCode(), "Code should match the expected value");

    }

    @Test
    public void given_NodeAddExist_when_GetCodeAndMessage_then_ReturnCorrectValues() {
        ProductGroupStatusCode statusCode = ProductGroupStatusCode.NODE_ADD_EXIST;
        assertNotNull(statusCode, "Status code should not be null");
        assertEquals(1103, statusCode.getCode(), "Code should match the expected value");

    }

    @Test
    public void given_NodeAddFail_when_GetCodeAndMessage_then_ReturnCorrectValues() {
        ProductGroupStatusCode statusCode = ProductGroupStatusCode.NODE_ADD_FAIL;
        assertNotNull(statusCode, "Status code should not be null");
        assertEquals(1104, statusCode.getCode(), "Code should match the expected value");

    }

    @Test
    public void given_NodeParentNotExist_when_GetCodeAndMessage_then_ReturnCorrectValues() {
        ProductGroupStatusCode statusCode = ProductGroupStatusCode.NODE_PARENT_NOT_EXIST;
        assertNotNull(statusCode, "Status code should not be null");
        assertEquals(1105, statusCode.getCode(), "Code should match the expected value");

    }

    @Test
    public void given_NodeExistSubset_when_GetCodeAndMessage_then_ReturnCorrectValues() {
        ProductGroupStatusCode statusCode = ProductGroupStatusCode.NODE_EXIST_SUBSET;
        assertNotNull(statusCode, "Status code should not be null");
        assertEquals(1106, statusCode.getCode(), "Code should match the expected value");

    }

    @Test
    public void given_ProductCategoryNotExist_when_GetCodeAndMessage_then_ReturnCorrectValues() {
        ProductGroupStatusCode statusCode = ProductGroupStatusCode.PRODUCT_CATEGORY_NOT_EXIST;
        assertNotNull(statusCode, "Status code should not be null");
        assertEquals(1107, statusCode.getCode(), "Code should match the expected value");

    }
    /* Ended by AICoder, pid:hb5eeg4e39v1d6414c5d0826b0f86d6b17737786 */
}

