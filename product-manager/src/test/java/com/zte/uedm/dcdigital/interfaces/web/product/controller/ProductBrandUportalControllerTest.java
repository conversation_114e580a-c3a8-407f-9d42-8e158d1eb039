package com.zte.uedm.dcdigital.interfaces.web.product.controller;/* Started by AICoder, pid:24a22063aaua44c146770b4421b6f78855788870 */
import com.zte.uedm.dcdigital.application.brand.ProductBrandCommandService;
import com.zte.uedm.dcdigital.application.brand.ProductBrandQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductBrandVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductBrandUportalControllerTest {

    @InjectMocks
    private ProductBrandUportalController controller;

    @Mock
    private ProductBrandQueryService queryService;

    @Mock
    private ProductBrandCommandService commandService;

    private ProductBrandQueryDto queryDto;
    private ProductBrandAddDto addDto;

    @Before
    public void setUp() {
        queryDto = new ProductBrandQueryDto();
        addDto = new ProductBrandAddDto();
    }

    /**
     * 测试获取选型评分最新的上限或下限。
     */
    @Test
    public void given_ValidInput_when_GetNewSelectScore_then_ReturnSuccess() {
        queryDto.setProductCategoryId("123");
        queryDto.setSelectionAttribute(0);
        when(queryService.getNewSelectScore(anyString(), anyInt())).thenReturn(new BigDecimal("5.0"));

        BaseResult<Object> result = controller.getNewSelectScore(queryDto);

        assertEquals(BaseResult.success(new BigDecimal("5.0")).getCode(), result.getCode());
    }

    @Test(expected = BusinessException.class)
    public void given_ValidInput_when_GetNewSelectScore_then_ReturnFailed() {
            ProductBrandQueryDto dto = new ProductBrandQueryDto();
            dto.setProductCategoryId("some-category-id");
            dto.setSelectionAttribute(null);
            controller.getNewSelectScore(dto);
    }

    @Test
    public void testReducedFuzzySearchBrandList_Normal() throws Exception {
        // 创建 ProductBrandQueryDto 对象并设置必要的属性
        ProductBrandQueryDto dto = new ProductBrandQueryDto();
        dto.setPageNum(1);
        dto.setPageSize(10);
        dto.setBrandName("test brand");

        // 创建 PageVO<ProductBrandVo> 对象并设置数据
        PageVO<ProductBrandVo> pageVO = new PageVO<>();
        ProductBrandVo brandVo1=new ProductBrandVo();
        brandVo1.setId("123");
        ProductBrandVo brandVo2=new ProductBrandVo();
        brandVo2.setId("123");
        List<ProductBrandVo> productBrandVoList = new ArrayList<>();
        productBrandVoList.add(brandVo1);
        productBrandVoList.add(brandVo2);
        pageVO.setList(productBrandVoList);
        pageVO.setTotal((long) productBrandVoList.size());

        // 模拟 queryService.fuzzySearchBrandList 返回指定的对象
        //when(queryService.fuzzySearchBrandList(dto)).thenReturn(pageVO);

        // 调用被测方法
        BaseResult<Object> result = controller.reducedFuzzySearchBrandList(dto);
        assertEquals(pageVO, result.getData());
    }

    /**
     * 测试查询品牌名称是否存在。
     */
    @Test
    public void given_ValidInput_when_DuplicateBrandNameQuery_then_ReturnSuccess() {
        queryDto.setProductCategoryId("123");
        queryDto.setBrandName("Apple");
        when(queryService.judgingConditionBrandName(anyString(), anyString(), anyString())).thenReturn(true);

        BaseResult<Object> result = controller.duplicateBrandNameQuery(queryDto);

        assertEquals(BaseResult.success(true).getCode(), result.getCode());
    }

    /**
     * 测试新增品牌。
     */
    @Test
    public void given_ValidInput_when_AddBrand_then_ReturnSuccess() {
        when(commandService.addBrandAndTag(any(ProductBrandAddDto.class))).thenReturn(1);
        addDto.setId("123");
        addDto.setProductCategoryId("123456");
        addDto.setBrandName("123456");
        addDto.setSelectionAttribute(0);
        addDto.setSelectionScore(BigDecimal.ONE);
        BaseResult<Object> result = controller.add(addDto);

        assertEquals(BaseResult.success().getCode(), result.getCode());
    }

    /**
     * 测试查询产品小类的品牌列表。
     */
    @Test
    public void given_ValidInput_when_FuzzyQuery_then_ReturnSuccess() {
        queryDto.setProductCategoryId("123");
        PageVO<ProductBrandVo> pageVO = new PageVO<>();

        BaseResult<Object> result = controller.fuzzyQuery(queryDto);

        assertEquals(BaseResult.success(pageVO).getCode(), result.getCode());
    }

    @Test(expected = BusinessException.class)
    public void given_ValidInput_when_FuzzyQuery_then_ReturnFailed() {
        queryDto.setProductCategoryId("");
        controller.fuzzyQuery(queryDto);
    }

    /**
     * 测试查询品牌详情。
     */
    @Test
    public void given_ValidId_when_GetDetail_then_ReturnSuccess() {
        ProductBrandVo brandVo = new ProductBrandVo();
        when(queryService.getBrandAndTagDetail(anyString())).thenReturn(brandVo);

        BaseResult<Object> result = controller.getDetail("123");

        assertEquals(BaseResult.success(brandVo).getCode(), result.getCode());
    }

    @Test(expected = BusinessException.class)
    public void given_ValidId_when_GetDetail_then_ReturnFailed() {
        String nullId = null;

        // 调用被测方法，期望抛出异常
        controller.getDetail(nullId);
    }

    /**
     * 测试更新/编辑品牌。
     */
    @Test
    public void given_ValidInput_when_UpdateBrand_then_ReturnSuccess() {
        addDto.setId("123");
        addDto.setProductCategoryId("123456");
        addDto.setBrandName("123456");
        addDto.setSelectionAttribute(0);
        addDto.setSelectionScore(BigDecimal.valueOf(5));
        when(commandService.updateBrandAndTag(any(ProductBrandAddDto.class))).thenReturn(1);

        BaseResult<Object> result = controller.update(addDto);

        assertEquals(BaseResult.success().getCode(), result.getCode());
    }
    @Test(expected = BusinessException.class)
    public void given_ValidInput_when_UpdateBrand_then_fail() {
        addDto.setId("123");

        BaseResult<Object> result = controller.update(addDto);

        assertEquals(BaseResult.success().getCode(), result.getCode());
    }

    @Test(expected = BusinessException.class)
    public void given_ValidInput_when_UpdateBrand_then_ReturnFailed() {
        addDto.setId("");
        controller.update(addDto);
    }

    /**
     * 测试删除品牌。
     */
    @Test
    public void given_ValidId_when_DeleteBrand_then_ReturnSuccess() {
        when(commandService.deleteBrandAndTag(anyString())).thenReturn(1);

        BaseResult<Object> result = controller.delete("123");

        assertEquals(BaseResult.success().getCode(), result.getCode());
    }

    @Test(expected = BusinessException.class)
    public void given_ValidId_when_DeleteBrand_then_ReturnFailed() {
        controller.delete("");
    }

    /**
     * 测试查询所有产品小类的品牌列表。
     */
    @Test
    public void given_ValidInput_when_DetailQuery_then_ReturnSuccess() {
        PageVO<ProductBrandVo> pageVO = new PageVO<>();
        when(queryService.detailedSearchBrandAndTagList(any(ProductBrandQueryDto.class))).thenReturn(pageVO);

        BaseResult<Object> result = controller.detailQuery(queryDto);

        assertEquals(BaseResult.success(pageVO).getCode(), result.getCode());
    }
}

/* Ended by AICoder, pid:24a22063aaua44c146770b4421b6f78855788870 */