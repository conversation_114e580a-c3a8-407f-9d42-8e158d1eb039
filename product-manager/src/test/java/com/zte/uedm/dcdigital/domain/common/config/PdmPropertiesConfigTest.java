package com.zte.uedm.dcdigital.domain.common.config;

import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/* Started by AICoder, pid:jd90d5c6169d6fb14d630ae070578348e3b14616 */
public class PdmPropertiesConfigTest {

    private PdmPropertiesConfig pdmPropertiesConfig;

    @Before
    public void setUp() {
        pdmPropertiesConfig = new PdmPropertiesConfig();
    }

    @Test
    public void testDefaultValues() {
        assertNull(pdmPropertiesConfig.getToken());
        assertNull(pdmPropertiesConfig.getBaseUrl());
        assertNull(pdmPropertiesConfig.getNumber());
        assertNull(pdmPropertiesConfig.getName());
    }

    @Test
    public void testSettersAndGetters() {
        pdmPropertiesConfig.setToken("token");
        pdmPropertiesConfig.setBaseUrl("https://example.com");
        pdmPropertiesConfig.setName("name");
        pdmPropertiesConfig.setNumber("number");

        assertEquals("token", pdmPropertiesConfig.getToken());
        assertEquals("https://example.com", pdmPropertiesConfig.getBaseUrl());
        assertEquals("name", pdmPropertiesConfig.getName());
        assertEquals("number", pdmPropertiesConfig.getNumber());
    }

    @Test
    public void testToString() {
        pdmPropertiesConfig.setToken("token");
        pdmPropertiesConfig.setBaseUrl("https://example.com");
        pdmPropertiesConfig.setName("name");
        pdmPropertiesConfig.setNumber("number");
        String result = pdmPropertiesConfig.toString();
        assertNotNull(result);
        assertNotEquals("", result);
    }
}
/* Ended by AICoder, pid:jd90d5c6169d6fb14d630ae070578348e3b14616 */
