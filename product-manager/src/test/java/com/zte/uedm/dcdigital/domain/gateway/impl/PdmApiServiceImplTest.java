package com.zte.uedm.dcdigital.domain.gateway.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.common.config.PdmPropertiesConfig;
import com.zte.uedm.dcdigital.domain.common.constant.PdmConstants;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.CategoryDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.MaterialDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.ProductLineDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.SpecModelDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.*;
import io.vavr.collection.Array;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;


/* Started by AICoder, pid:b5323u820d01002149a10a9d130a6110ac4355ad */
public class PdmApiServiceImplTest {

    @InjectMocks
    private PdmApiServiceImpl pdmApiService;

    @Mock
    private PdmPropertiesConfig pdmPropertiesConfig;

    @Mock
    private RestTemplate restTemplate;

    private ObjectMapper objectMapper;

    private String baseUrl;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        baseUrl = "https://pdmweb.test.zte.com.cn";

        when(pdmPropertiesConfig.getName()).thenReturn("zte-energy-dc-digital");
        when(pdmPropertiesConfig.getToken()).thenReturn("aabac626fd3fd3aa3f32131e657aaf00");
        when(pdmPropertiesConfig.getBaseUrl()).thenReturn(baseUrl);
        when(pdmPropertiesConfig.getNumber()).thenReturn("100000230077");
    }

    @Test
    public void testGetProductLineList() {
        ProductLineDto dto = new ProductLineDto();
        dto.setPageNum(1);
        dto.setPageSize(10);

        ProductResponseVo responseVo = new ProductResponseVo();
        responseVo.setOther(new ProductResponseVo.Other());
        responseVo.getOther().setTotal(20);
        responseVo.getOther().setPageCount(2);

        responseVo.setCode(new ProductResponseVo.Code());
        responseVo.getCode().setCode("0000");

        List<ProductResponseVo.BO> boList = new ArrayList<>();
        ProductResponseVo.BO bo = new ProductResponseVo.BO();
        bo.setItemNo("parent1");
        bo.setCnName("产品线");
        bo.setParentNo("parent");
        boList.add(bo);
        ProductResponseVo.BO bo1 = new ProductResponseVo.BO();
        bo1.setItemNo("item1");
        bo1.setCnName("产品线1");
        bo1.setParentNo("parent1");
        boList.add(bo1);

        responseVo.setBo(boList);

        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(ProductResponseVo.class)))
                .thenReturn(ResponseEntity.ok(responseVo));

        // Call the method to be tested
        PageVO<ProductLineVo> result = pdmApiService.getProductLineList(dto);
        Assert.assertEquals(20, result.getTotal());
    }

    @Test
    public void testGetProductLineListWithFailure() {
        ProductLineDto dto = new ProductLineDto();
        dto.setPageNum(1);
        dto.setPageSize(10);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(ProductResponseVo.class)))
                .thenThrow(new RestClientException("Failed to request the pdm"));
        try {
            pdmApiService.getProductLineList(dto);
        } catch (BusinessException e) {
            Assert.assertEquals("failed to obtain pdm data", e.getMessage());
        }

        ResponseEntity<ProductResponseVo> emptyBodyResponseEntity = new ResponseEntity<>(null, HttpStatus.OK);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(ProductResponseVo.class))).thenReturn(emptyBodyResponseEntity);
        try {
            pdmApiService.getProductLineList(dto);
        } catch (BusinessException e) {
            Assert.assertEquals("failed to obtain pdm data", e.getMessage());
        }

        ProductResponseVo responseVo = new ProductResponseVo();
        responseVo.setOther(new ProductResponseVo.Other());
        responseVo.getOther().setTotal(20);
        responseVo.getOther().setPageCount(2);

        responseVo.setCode(new ProductResponseVo.Code());
        responseVo.getCode().setCode("00001");

        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(ProductResponseVo.class)))
                .thenReturn(ResponseEntity.ok(responseVo));
        try {
            pdmApiService.getProductLineList(dto);
        } catch (BusinessException e) {
            Assert.assertEquals("failed to obtain pdm data", e.getMessage());
        }
    }

    @Test
    public void testGetLargeCategoryList() {
        CategoryDto dto = new CategoryDto();
        dto.setProductLineNo("1");

        CategoryRelationVo.BO.Item item1 = new CategoryRelationVo.BO.Item();
        item1.setItemNo("categoryNo1");
        item1.setCnName("Category Name 1");

        CategoryRelationVo.BO.Item item2 = new CategoryRelationVo.BO.Item();
        item2.setItemNo("categoryNo2");
        item2.setCnName("Category Name 2");

        CategoryRelationVo.BO bo = new CategoryRelationVo.BO();
        bo.setItemChildList(Arrays.asList(item1, item2));

        CategoryRelationVo relationVo = new CategoryRelationVo();
        relationVo.setBo(Arrays.asList(bo));

        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(CategoryRelationVo.class))).thenReturn(ResponseEntity.ok(relationVo));
        List<CategoryVo> result = pdmApiService.getLargeCategoryList(dto);
        Assert.assertNotNull(result);

        CategoryDto categoryDto = new CategoryDto();
        categoryDto.setCategoryNo("1");
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(CategoryRelationVo.class))).thenReturn(ResponseEntity.ok(relationVo));
        List<CategoryVo> result1 = pdmApiService.getLargeCategoryList(categoryDto);
        Assert.assertNotNull(result1);
    }

    @Test
    public void testGetLargeCategoryListWithFailure() throws JsonProcessingException {
        try {
            pdmApiService.getLargeCategoryList(null);
        } catch (BusinessException e) {
            Assert.assertEquals(1001, e.getCode().intValue());
        }
        CategoryDto dto = new CategoryDto();
        dto.setProductLineNo("1");
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(CategoryRelationVo.class)))
                .thenThrow(new RestClientException("Failed to request the pdm"));
        try {
            pdmApiService.getLargeCategoryList(dto);
        } catch (BusinessException e) {
            Assert.assertEquals(1120, e.getCode().intValue());
        }

        ResponseEntity<CategoryRelationVo> emptyBodyResponseEntity = new ResponseEntity<>(null, HttpStatus.OK);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(CategoryRelationVo.class))).thenReturn(emptyBodyResponseEntity);

        try {
            pdmApiService.getLargeCategoryList(dto);
        } catch (BusinessException e) {
            Assert.assertEquals(1120, e.getCode().intValue());
        }
    }

    @Test
    public void testGetSpecificationModelAll() {
        SpecModelDto dto = new SpecModelDto();
        dto.setPageNum(1);
        dto.setPageSize(10);

        CategoryRelationVo.BO.Item item1 = new CategoryRelationVo.BO.Item();
        item1.setItemNo("categoryNo1");
        item1.setCnName("Category Name 1");

        CategoryRelationVo.BO.Item item2 = new CategoryRelationVo.BO.Item();
        item2.setItemNo("categoryNo2");
        item2.setCnName("Category Name 2");

        CategoryRelationVo.BO bo = new CategoryRelationVo.BO();
        bo.setItemChildList(Arrays.asList(item1, item2));

        CategoryRelationVo relationVo = new CategoryRelationVo();
        relationVo.setBo(Arrays.asList(bo));
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(CategoryRelationVo.class))).thenReturn(ResponseEntity.ok(relationVo));

        ProductResponseVo responseVo = new ProductResponseVo();
        responseVo.setOther(new ProductResponseVo.Other());
        responseVo.getOther().setTotal(20);
        responseVo.getOther().setPageCount(2);

        responseVo.setCode(new ProductResponseVo.Code());
        responseVo.getCode().setCode("0000");

        List<ProductResponseVo.BO> boList = new ArrayList<>();
        ProductResponseVo.BO productBo = new ProductResponseVo.BO();
        productBo.setItemNo("parent1");
        productBo.setCnName("产品线");
        productBo.setParentNo("parent");
        boList.add(productBo);
        ProductResponseVo.BO bo1 = new ProductResponseVo.BO();
        bo1.setItemNo("item1");
        bo1.setCnName("产品线1");
        bo1.setParentNo("parent1");
        boList.add(bo1);

        responseVo.setBo(boList);

        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(ProductResponseVo.class)))
                .thenReturn(ResponseEntity.ok(responseVo));

        PageVO<SpecModelVo> model = pdmApiService.getSpecificationModel(dto);
        Assert.assertNotNull(model);
    }

    @Test
    public void testGetSpecificationModelWithFailure() {
        try {
            pdmApiService.getSpecificationModel(null);
        } catch (BusinessException e) {
            Assert.assertEquals(1001, e.getCode().intValue());
        }
    }

    @Test
    public void testGetSpecificationModelCategoryNo() {
        SpecModelDto dto = new SpecModelDto();
        dto.setPageNum(1);
        dto.setPageSize(10);
        dto.setLargeCategoryNo("largeCategoryNo");
        CategoryRelationVo.BO.Item item1 = new CategoryRelationVo.BO.Item();
        item1.setItemNo("categoryNo1");
        item1.setCnName("Category Name 1");

        CategoryRelationVo.BO.Item item2 = new CategoryRelationVo.BO.Item();
        item2.setItemNo("categoryNo2");
        item2.setCnName("Category Name 2");

        CategoryRelationVo.BO bo = new CategoryRelationVo.BO();
        bo.setItemChildList(Arrays.asList(item1, item2));

        CategoryRelationVo relationVo = new CategoryRelationVo();
        relationVo.setBo(Arrays.asList(bo));
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(CategoryRelationVo.class))).thenReturn(ResponseEntity.ok(relationVo));

        ProductResponseVo responseVo = new ProductResponseVo();
        responseVo.setOther(new ProductResponseVo.Other());
        responseVo.getOther().setTotal(20);
        responseVo.getOther().setPageCount(2);

        responseVo.setCode(new ProductResponseVo.Code());
        responseVo.getCode().setCode("0000");

        List<ProductResponseVo.BO> boList = new ArrayList<>();
        ProductResponseVo.BO productBo = new ProductResponseVo.BO();
        productBo.setItemNo("parent1");
        productBo.setCnName("产品线");
        productBo.setParentNo("parent");
        boList.add(productBo);
        ProductResponseVo.BO bo1 = new ProductResponseVo.BO();
        bo1.setItemNo("item1");
        bo1.setCnName("产品线1");
        bo1.setParentNo("parent1");
        boList.add(bo1);

        responseVo.setBo(boList);

        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(ProductResponseVo.class)))
                .thenReturn(ResponseEntity.ok(responseVo));
        PageVO<SpecModelVo> model = pdmApiService.getSpecificationModel(dto);
        Assert.assertNotNull(model);

        dto.setSmallCategoryNo("smallCategoryNo");
        PageVO<SpecModelVo> model1 = pdmApiService.getSpecificationModel(dto);
        Assert.assertNotNull(model1);
    }

    @Test
    public void testGetSpecificationModelitemNoOrProductModel() {
        SpecModelDto dto = new SpecModelDto();
        dto.setPageNum(1);
        dto.setPageSize(10);
        dto.setItemNo("itemNo");
        dto.setProductModel("productModel");

        ProductResponseVo responseVo = new ProductResponseVo();
        responseVo.setOther(new ProductResponseVo.Other());
        responseVo.getOther().setTotal(20);
        responseVo.getOther().setPageCount(2);

        responseVo.setCode(new ProductResponseVo.Code());
        responseVo.getCode().setCode("0000");

        List<ProductResponseVo.BO> boList = new ArrayList<>();
        ProductResponseVo.BO productBo = new ProductResponseVo.BO();
        productBo.setItemNo("parent1");
        productBo.setCnName("产品线");
        productBo.setParentNo("parent");
        boList.add(productBo);
        ProductResponseVo.BO bo1 = new ProductResponseVo.BO();
        bo1.setItemNo("item1");
        bo1.setCnName("产品线1");
        bo1.setParentNo("parent1");
        boList.add(bo1);

        responseVo.setBo(boList);

        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(ProductResponseVo.class)))
                .thenReturn(ResponseEntity.ok(responseVo));
        PageVO<SpecModelVo> model = pdmApiService.getSpecificationModel(dto);
        Assert.assertNotNull(model);
    }

    @Test
    public void testGetMaterialList_noData() {
        MaterialDto dto = new MaterialDto();
        dto.setQueryType(PdmConstants.QUERY_SBOM_NO);
        dto.setPageNum(1);
        dto.setPageSize(10);

        PageVO<MaterialVo> materialList = pdmApiService.getMaterialList(dto);
        Assert.assertNotNull(materialList);
    }

    @Test
    public void testGetMaterialList_QueryTypeIsEmpty(){
        MaterialDto dto = new MaterialDto();
        dto.setQueryType(null);
        try{
            pdmApiService.getMaterialList(dto);
        }catch (BusinessException e){
            Assert.assertEquals(1001, e.getCode().intValue());
        }
    }

    /* Started by AICoder, pid:d1590775abxbdf01454d09eb20fc3b427f86eaa0 */
    @Test
    public void testGetMaterialList_QueryTypeIsSbomNo(){
        MaterialDto dto = new MaterialDto();
        dto.setQueryType(PdmConstants.QUERY_SBOM_NO);
        dto.setPageNum(1);
        dto.setPageSize(10);
        dto.setSbomNo("testSbomNo");
        dto.setSbomName("testSbomName");

        SbomInfoResponseVo sbomInfoResponseVo = new SbomInfoResponseVo();
        sbomInfoResponseVo.setCode(new SbomInfoResponseVo.Code());
        sbomInfoResponseVo.getCode().setCode("0000");
        List<SbomInfoResponseVo.Bo> boList = new ArrayList<>();
        SbomInfoResponseVo.Bo bo = new SbomInfoResponseVo.Bo();
        bo.setOrgId("1");
        bo.setSbomNo("1");
        bo.setNameCn("test");
        boList.add(bo);
        sbomInfoResponseVo.setBo(boList);
        sbomInfoResponseVo.setOther(new SbomInfoResponseVo.Other());
        sbomInfoResponseVo.getOther().setTotal(1);
        sbomInfoResponseVo.getOther().setPageCount(1);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomInfoResponseVo.class)))
                .thenReturn(ResponseEntity.ok(sbomInfoResponseVo));

        SbomDirectComPosinfoResponseVo salesCodeResult = new SbomDirectComPosinfoResponseVo();
        salesCodeResult.setCode(new SbomDirectComPosinfoResponseVo.Code());
        salesCodeResult.getCode().setCode("0000");
        salesCodeResult.setBo(new SbomDirectComPosinfoResponseVo.Bo());
        salesCodeResult.getBo().setRows(Collections.emptyList());
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomDirectComPosinfoResponseVo.class)))
                .thenReturn(ResponseEntity.ok(salesCodeResult));

        SbomDetailResponseVo sbomDetailResponseVo = new SbomDetailResponseVo();
        sbomDetailResponseVo.setCode(new SbomDetailResponseVo.Code());
        sbomDetailResponseVo.getCode().setCode("0000");
        List<SbomDetailResponseVo.BO> detailInfo = Collections.singletonList(new SbomDetailResponseVo.BO());
        Map<String, List<SbomDetailResponseVo.BO>> objectObjectMap = new HashMap<>();
        objectObjectMap.put("S0001", detailInfo);
        sbomDetailResponseVo.setBo(objectObjectMap);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomDetailResponseVo.class)))
                .thenReturn(ResponseEntity.ok(sbomDetailResponseVo));

        PageVO<MaterialVo> materialList = pdmApiService.getMaterialList(dto);
        Assert.assertNotNull(materialList);
    }
    /* Ended by AICoder, pid:d1590775abxbdf01454d09eb20fc3b427f86eaa0 */

    /* Started by AICoder, pid:w52d7a788bu1b4f14321089fc031c926ec81802a */
    @Test
    public void testGetMaterialList_QueryTypeIsSbomNo_WithFailure(){
        MaterialDto dto = new MaterialDto();
        dto.setQueryType(PdmConstants.QUERY_SBOM_NO);
        dto.setPageNum(1);
        dto.setPageSize(10);
        dto.setSbomNo("testSbomNo");
        dto.setSbomName("testSbomName");

        SbomInfoResponseVo sbomInfoResponseVo = new SbomInfoResponseVo();
        sbomInfoResponseVo.setCode(new SbomInfoResponseVo.Code());
        sbomInfoResponseVo.getCode().setCode("00001");

        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomInfoResponseVo.class)))
                .thenReturn(ResponseEntity.ok(sbomInfoResponseVo));
        try {
            pdmApiService.getMaterialList(dto);
        } catch (BusinessException e) {
            Assert.assertEquals(1120, e.getCode().intValue());
        }
    }
    /* Ended by AICoder, pid:w52d7a788bu1b4f14321089fc031c926ec81802a */

    /* Started by AICoder, pid:i8d95vb4d0ma13314f8e08fd600bc0186160517a */
    @Test
    public void testGetMaterialList_QueryTypeIsPartNo_noData(){
        MaterialDto dto = new MaterialDto();
        dto.setQueryType(PdmConstants.QUERY_PART_NO);
        dto.setPageNum(1);
        dto.setPageSize(10);

        PageVO<MaterialVo> materialList = pdmApiService.getMaterialList(dto);
        Assert.assertNotNull(materialList);
    }
    /* Ended by AICoder, pid:i8d95vb4d0ma13314f8e08fd600bc0186160517a */

    /* Started by AICoder, pid:s5e16r20d7ac3f8140bc0a66e0c3fc453b544825 */
    @Test
    public void testGetMaterialList_QueryTypeIsPartNo(){
        MaterialDto dto = new MaterialDto();
        dto.setQueryType(PdmConstants.QUERY_PART_NO);
        dto.setPageNum(1);
        dto.setPageSize(10);
        dto.setPartNo("testPartNo");

        BasicQueryResponseVo queryResponseVo = new BasicQueryResponseVo();
        queryResponseVo.setCode(new BasicQueryResponseVo.Code());
        queryResponseVo.getCode().setCode("0000");
        BasicQueryResponseVo.Bo bo = new BasicQueryResponseVo.Bo();
        bo.setTotalPage(1);
        bo.setTotal(1);
        BasicQueryResponseVo.Rows row = new BasicQueryResponseVo.Rows();
        row.setNo("testNo");
        row.setNameCn("testNameCn");
        bo.setRows(Collections.singletonList(row));
        queryResponseVo.setBo(bo);

        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(BasicQueryResponseVo.class)))
                .thenReturn(ResponseEntity.ok(queryResponseVo));

        SbomDirectComPosinfoResponseVo salesCodeResult = new SbomDirectComPosinfoResponseVo();
        salesCodeResult.setCode(new SbomDirectComPosinfoResponseVo.Code());
        salesCodeResult.getCode().setCode("0000");
        salesCodeResult.setBo(new SbomDirectComPosinfoResponseVo.Bo());
        salesCodeResult.getBo().setRows(Collections.emptyList());
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomDirectComPosinfoResponseVo.class)))
                .thenReturn(ResponseEntity.ok(salesCodeResult));

        SbomDetailResponseVo sbomDetailResponseVo = new SbomDetailResponseVo();
        sbomDetailResponseVo.setCode(new SbomDetailResponseVo.Code());
        sbomDetailResponseVo.getCode().setCode("0000");
        List<SbomDetailResponseVo.BO> detailInfo = Collections.singletonList(new SbomDetailResponseVo.BO());
        Map<String, List<SbomDetailResponseVo.BO>> objectObjectMap = new HashMap<>();
        objectObjectMap.put("S0001", detailInfo);
        sbomDetailResponseVo.setBo(objectObjectMap);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomDetailResponseVo.class)))
                .thenReturn(ResponseEntity.ok(sbomDetailResponseVo));

        PageVO<MaterialVo> materialList = pdmApiService.getMaterialList(dto);
        Assert.assertNotNull(materialList);
    }
    /* Ended by AICoder, pid:s5e16r20d7ac3f8140bc0a66e0c3fc453b544825 */

    /* Started by AICoder, pid:g33893d419vc27814eb10817c0d2ef2011c22590 */
    @Test
    public void testGetMaterialList_QueryTypeIsPartNo_WithFailure(){
        MaterialDto dto = new MaterialDto();
        dto.setQueryType(PdmConstants.QUERY_PART_NO);
        dto.setPageNum(1);
        dto.setPageSize(10);
        dto.setPartNo("testPartNo");

        BasicQueryResponseVo queryResponseVo = new BasicQueryResponseVo();
        queryResponseVo.setCode(new BasicQueryResponseVo.Code());
        queryResponseVo.getCode().setCode("0004");
        queryResponseVo.setBo(new BasicQueryResponseVo.Bo());
        queryResponseVo.getBo().setMsg("failed to get material list by partNo");
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(BasicQueryResponseVo.class)))
                .thenReturn(ResponseEntity.ok(queryResponseVo));

        try {
            pdmApiService.getMaterialList(dto);
        } catch (BusinessException e) {
            Assert.assertEquals(1120, e.getCode().intValue());
        }
    }
    /* Ended by AICoder, pid:g33893d419vc27814eb10817c0d2ef2011c22590 */

    @Test
    public void testGetMaterialListByCode(){
        SbomDirectComPosinfoResponseVo salesCodeResult = new SbomDirectComPosinfoResponseVo();
        salesCodeResult.setCode(new SbomDirectComPosinfoResponseVo.Code());
        salesCodeResult.getCode().setCode("0000");
        salesCodeResult.setBo(new SbomDirectComPosinfoResponseVo.Bo());
        List<SbomDirectComPosinfoResponseVo.Rows> list = new ArrayList<>();
        SbomDirectComPosinfoResponseVo.Rows rows = new SbomDirectComPosinfoResponseVo.Rows();
        rows.setBaseNo("123");
        list.add(rows);
        salesCodeResult.getBo().setRows(list);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomDirectComPosinfoResponseVo.class)))
                .thenReturn(ResponseEntity.ok(salesCodeResult));

        SbomDetailResponseVo sbomDetailResponseVo = new SbomDetailResponseVo();
        sbomDetailResponseVo.setCode(new SbomDetailResponseVo.Code());
        sbomDetailResponseVo.getCode().setCode("0000");
        List<SbomDetailResponseVo.BO> detailInfo = Collections.singletonList(new SbomDetailResponseVo.BO());
        Map<String, List<SbomDetailResponseVo.BO>> objectObjectMap = new HashMap<>();
        objectObjectMap.put("S0001", detailInfo);
        sbomDetailResponseVo.setBo(objectObjectMap);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomDetailResponseVo.class)))
                .thenReturn(ResponseEntity.ok(sbomDetailResponseVo));

        List<String> saleCodeList = new ArrayList<>();
        saleCodeList.add("S0001");
        List<MaterialVo> result = pdmApiService.getMaterialListByCode(saleCodeList);
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetMaterialListBySbom(){
        SbomInfoResponseVo sbomInfoResponseVo = new SbomInfoResponseVo();
        sbomInfoResponseVo.setCode(new SbomInfoResponseVo.Code());
        sbomInfoResponseVo.getCode().setCode("0000");
        List<SbomInfoResponseVo.Bo> boList = new ArrayList<>();
        SbomInfoResponseVo.Bo bo = new SbomInfoResponseVo.Bo();
        bo.setOrgId("1");
        bo.setSbomNo("1");
        bo.setNameCn("test");
        boList.add(bo);
        sbomInfoResponseVo.setBo(boList);
        sbomInfoResponseVo.setOther(new SbomInfoResponseVo.Other());
        sbomInfoResponseVo.getOther().setTotal(1);
        sbomInfoResponseVo.getOther().setPageCount(1);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomInfoResponseVo.class)))
                .thenReturn(ResponseEntity.ok(sbomInfoResponseVo));

        SbomInfoResponseVo.Bo result = pdmApiService.getMaterialListBySbom("test");
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetMaterialListBySbom_WithFailure(){
        SbomInfoResponseVo sbomInfoResponseVo = new SbomInfoResponseVo();
        sbomInfoResponseVo.setCode(new SbomInfoResponseVo.Code());
        sbomInfoResponseVo.getCode().setCode("0001");
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), eq(SbomInfoResponseVo.class)))
                .thenReturn(ResponseEntity.ok(sbomInfoResponseVo));

        try {
            pdmApiService.getMaterialListBySbom("test");
        } catch (BusinessException e) {
            Assert.assertEquals(1120, e.getCode().intValue());
        }
    }

}
/* Ended by AICoder, pid:b5323u820d01002149a10a9d130a6110ac4355ad */
