package com.zte.uedm.dcdigital.interfaces.web.product.controller;

import com.zte.uedm.dcdigital.application.group.executor.ProductGroupCommandService;
import com.zte.uedm.dcdigital.application.group.executor.ProductGroupQueryService;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;



public class ProductGroupUportalControllerTest {

    @InjectMocks
    private ProductGroupUportalController productGroupUportalController;

    @Mock
    private ProductGroupCommandService productGroupCommandService;

    @Mock
    private ProductGroupQueryService productGroupQueryService;


    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    /* Started by AICoder, pid:655a8fd46d93fa9143d70a3bf05f121da21386b7 */
    @Test
    public void testAddProductGroup() throws Exception {
        ProductGroupAddDto addDto = new ProductGroupAddDto();
        addDto.setName("Test Group");
        addDto.setParentId("123");
        addDto.setProductCategoryId("456");
        ProductGroupVo productGroupVo = new ProductGroupVo();
        productGroupVo.setId("1");
        productGroupVo.setName("Test Group");
        productGroupVo.setParentId("123");
        Mockito.when(productGroupCommandService.add(addDto)).thenReturn(productGroupVo);
        BaseResult<ProductGroupVo> responseBean = productGroupUportalController.add(addDto);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }
    /* Ended by AICoder, pid:655a8fd46d93fa9143d70a3bf05f121da21386b7 */



    /* Started by AICoder, pid:f2371kebe7154ce147e30a3dc099580efc66c0a5 */
    @Test
    public void testDeleteProductGroup() throws Exception {
        Mockito.when(productGroupCommandService.delete(Mockito.anyString())).thenReturn(1);
        BaseResult responseBean = productGroupUportalController.delete("1");
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }
    /* Ended by AICoder, pid:f2371kebe7154ce147e30a3dc099580efc66c0a5 */




    /* Started by AICoder, pid:3531ccee19bcc5c140b00ad270a1331d5be94786 */
    @Test
    public void testGetProductGroups() throws Exception {
        ProductGroupQueryDto queryDto = new ProductGroupQueryDto();
        queryDto.setProductCategoryId("123");
        List<ProductGroupVo> entityList = new ArrayList<>();
        ProductGroupVo entity1 = new ProductGroupVo();
        entity1.setId("1");
        entity1.setName("Group 1");
        entity1.setParentId("123");
        entityList.add(entity1);
        ProductGroupVo entity2 = new ProductGroupVo();
        entity2.setId("2");
        entity2.setName("Group 2");
        entity2.setParentId("456");
        entityList.add(entity2);
        Mockito.when(productGroupQueryService.queryProductGroups(Mockito.any(ProductGroupQueryDto.class))).thenReturn(entityList);
        BaseResult responseBean = productGroupUportalController.getGroup(queryDto);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }
    /* Ended by AICoder, pid:3531ccee19bcc5c140b00ad270a1331d5be94786 */


    /* Started by AICoder, pid:he08di65d5jd82714a880bff9087f31300630932 */
    @Test
    public void testEditProductGroup() throws Exception {
        ProductGroupEditDto editDto = new ProductGroupEditDto();
        editDto.setId("1");
        editDto.setName("Updated Group");
        editDto.setPdmModelSpec("mocspec");
        ProductGroupVo productGroupVo = new ProductGroupVo();
        productGroupVo.setId("1");
        productGroupVo.setName("Updated Group");
        Mockito.when(productGroupCommandService.edit(Mockito.any(ProductGroupEditDto.class))).thenReturn(productGroupVo);
        BaseResult responseBean = productGroupUportalController.edit(editDto);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }
    /* Ended by AICoder, pid:he08di65d5jd82714a880bff9087f31300630932 */

    @Test(expected = BusinessException.class)
    public void updateSort2() throws Exception {

        GroupSortUpdateDto sortUpdateDto = new GroupSortUpdateDto();
        sortUpdateDto.setParentId("1");
        productGroupUportalController.updateSort(sortUpdateDto);
    }

    @Test
    public void updateSort() throws Exception {
        GroupSortUpdateDto sortUpdateDto = new GroupSortUpdateDto();
        sortUpdateDto.setParentId("1");
        List<ProductGroupVo> ls = new ArrayList<>();
        sortUpdateDto.setProductGroupVoList(ls);

        BaseResult responseBean = productGroupUportalController.updateSort(sortUpdateDto);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }



}
