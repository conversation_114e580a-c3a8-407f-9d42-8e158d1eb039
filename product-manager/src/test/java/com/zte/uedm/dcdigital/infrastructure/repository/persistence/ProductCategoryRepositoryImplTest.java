/* Started by AICoder, pid:q2de4ea1ae0f1d1142eb088d62f3512037c73e9f */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCategoryExtraInfoMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCategoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PageHelper.class})
public class ProductCategoryRepositoryImplTest {

    @InjectMocks
    private ProductCategoryRepositoryImpl productCategoryRepository;

    @Mock
    private ProductCategoryMapper productCategoryMapper;

    @Mock
    private ProductCategoryExtraInfoMapper categoryExtraInfoMapper;

    @Mock
    private PageHelper pageHelper;
    private ProductCategoryEntity entity;
    private ProductCategoryPo po;

    @Before
    public void setUp() {
        entity = new ProductCategoryEntity();
        entity.setId("1");
        entity.setProductName("Test Category");

        po = new ProductCategoryPo();
        po.setId("1");
        po.setProductName("Test Category");
        PowerMockito.mockStatic(PageHelper.class);

    }

    @Test
    public void testQueryById() throws BusinessException {
        when(productCategoryMapper.selectById(anyString())).thenReturn(po);


        ProductCategoryEntity result = productCategoryRepository.queryById("1");

        assertNotNull(result);
        assertEquals("1", result.getId());
        assertEquals("Test Category", result.getProductName());
    }

    @Test(expected = BusinessException.class)
    public void testQueryById_Exception() throws BusinessException {
        when(productCategoryMapper.selectById(anyString())).thenThrow(new RuntimeException());

        productCategoryRepository.queryById("1");
    }





    @Test
    public void testDeleteById() throws BusinessException {
        doReturn(1).when(productCategoryMapper).deleteById(Mockito.anyString());
        productCategoryRepository.deleteById("1");

    }

    @Test(expected = BusinessException.class)
    public void testDeleteById_Exception() throws BusinessException {
        doThrow(new RuntimeException()).when(productCategoryMapper).deleteById(anyString());

        productCategoryRepository.deleteById("1");
    }



    @Test
    public void testAddProductCategory() throws BusinessException {
        doReturn(1).when(productCategoryMapper).insert(Mockito.any());
        productCategoryRepository.addProductCategory(entity);

    }

    @Test(expected = BusinessException.class)
    public void testAddProductCategory_Exception() throws BusinessException {

        doThrow(new RuntimeException()).when(productCategoryMapper).insert(any(ProductCategoryPo.class));

        productCategoryRepository.addProductCategory(entity);
    }



    @Test
    public void testUpdateProductCategory() throws BusinessException {

        doReturn(1).when(productCategoryMapper).updateById(Mockito.any());
        productCategoryRepository.updateProductCategory(entity);

    }

    @Test(expected = BusinessException.class)
    public void testUpdateProductCategory_Exception() throws BusinessException {

        doThrow(new RuntimeException()).when(productCategoryMapper).updateById(any(ProductCategoryPo.class));

        productCategoryRepository.updateProductCategory(entity);
    }



    @Test
    public void testQueryByCondition() throws BusinessException {

        List<ProductCategoryPo> pos = Collections.singletonList(po);
        when(productCategoryMapper.selectByCondition(Mockito.anyString(),Mockito.anyInt(),Mockito.anyString())).thenReturn(pos);
        ProductCategoryQueryDto queryDto = new ProductCategoryQueryDto();
        queryDto.setPageSize(10);
        queryDto.setPageNum(1);
        PageInfo<ProductCategoryEntity> productCategoryEntityPageInfo = productCategoryRepository.queryByCondition(queryDto);

        assertNotNull(productCategoryEntityPageInfo);

    }

    @Test(expected = BusinessException.class)
    public void testQueryByCondition_Exception() throws BusinessException {
        when(productCategoryMapper.selectByCondition(Mockito.anyString(),Mockito.anyInt(),Mockito.anyString())).thenThrow(new RuntimeException());
        ProductCategoryQueryDto queryDto = new ProductCategoryQueryDto();
        productCategoryRepository.queryByCondition(queryDto);
    }

    /* Started by AICoder, pid:t9917k21bdt302f141ed0a26600b413445c18d6e */
    @Test
    public void testCountByNameAndIdNot() {
        // 模拟数据库查询返回1，表示存在相同名称但不同ID的记录
        when(productCategoryMapper.queryByIdAndName(Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        boolean result = productCategoryRepository.countByNameAndIdNot("Test Category", "1");
        assertTrue(result);
    }
    @Test
    public void testNameValidation() {
        when(productCategoryMapper.exists(any(LambdaQueryWrapper.class))).thenReturn(true);

        boolean result = productCategoryRepository.nameValidation("Test Category", 1,"parent");
        assertTrue(result);
    }
    /* Ended by AICoder, pid:t9917k21bdt302f141ed0a26600b413445c18d6e */

    @Test
    public void testCheckCorrelationRelationship() throws BusinessException {
        when(productCategoryMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(1L);

        Long result = productCategoryRepository.checkCorrelationRelationship("1");

        assertEquals(Long.valueOf(1), result);
    }

    @Test(expected = BusinessException.class)
    public void testCheckCorrelationRelationship_Exception() throws BusinessException {
        when(productCategoryMapper.selectCount(any(LambdaQueryWrapper.class))).thenThrow(new RuntimeException());

        productCategoryRepository.checkCorrelationRelationship("1");
    }

    @Test
    public void testBatchQueryProductSubclassesWithNullList() {
        List<String> ids = null;
        List<ProductCategoryVo> result = productCategoryRepository.batchQueryProductSubclasses(ids);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testBatchQueryProductSubclassesWithValidIds() {
        List<String> ids = Arrays.asList("1", "2", "3");
        ProductCategoryVo mockVo1 = new ProductCategoryVo();
        ProductCategoryVo mockVo2 = new ProductCategoryVo();
        ProductCategoryVo mockVo3 = new ProductCategoryVo();

        when(productCategoryMapper.queryProductSubclasses(ids, GlobalConstants.GROUP_LEVEL_MAX))
                .thenReturn(Arrays.asList(mockVo1, mockVo2, mockVo3));

        List<ProductCategoryVo> result = productCategoryRepository.batchQueryProductSubclasses(ids);

        assertEquals(3, result.size());
        assertEquals(mockVo1, result.get(0));
        assertEquals(mockVo2, result.get(1));
        assertEquals(mockVo3, result.get(2));
    }

    @Test
    public void queryUserProductSubcategoryTest() {
        List<ProductSubcategoryVo> subcategoryVos = productCategoryRepository.queryUserProductSubcategory(null);
        assertTrue(subcategoryVos.isEmpty());

        Mockito.when(productCategoryMapper.queryUserProductSubcategory(Mockito.any(),Mockito.anyInt())).thenReturn(new ArrayList<>());
        List<String> ids =Arrays.asList("1","2");
        List<ProductSubcategoryVo> subcategoryVoList = productCategoryRepository.queryUserProductSubcategory(ids);
        assertTrue(subcategoryVoList.isEmpty());
    }
    @Test
    public void selectCurentNodeAllChildNodeTest() {

        List<ProductCategoryEntity> productCategoryEntityList = productCategoryRepository.selectCurentNodeAllChildNode("null");
        assertTrue(productCategoryEntityList.isEmpty());
    }
    @Test
    public void updateBatchTest() {


        Mockito.doReturn(2).when(productCategoryMapper).updateById(Mockito.any());

        productCategoryRepository.updateBatch(new ArrayList<>());
        verify(productCategoryMapper, never()).updateById(Mockito.any());
    }
    /* Started by AICoder, pid:z9e78o8c2036c85140000afd706de11f43c41ec5 */
    @Test
    public void testQueryAllProductSubcategory() {
        // 模拟查询结果
        List<ProductSubcategoryVo> mockList = Collections.singletonList(new ProductSubcategoryVo());
        when(productCategoryMapper.queryAllProductSubcategory(GlobalConstants.GROUP_LEVEL_3)).thenReturn(mockList);

        // 调用服务层方法
        List<ProductSubcategoryVo> result = productCategoryRepository.queryAllProductSubcategory();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(productCategoryMapper, times(1)).queryAllProductSubcategory(GlobalConstants.GROUP_LEVEL_3);
    }
    /* Ended by AICoder, pid:z9e78o8c2036c85140000afd706de11f43c41ec5 */
}
/* Ended by AICoder, pid:q2de4ea1ae0f1d1142eb088d62f3512037c73e9f */
