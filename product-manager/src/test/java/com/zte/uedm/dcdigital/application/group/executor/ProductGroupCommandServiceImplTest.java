package com.zte.uedm.dcdigital.application.group.executor;

import com.zte.uedm.dcdigital.application.group.executor.impl.ProductGroupCommandServiceImpl;
import com.zte.uedm.dcdigital.domain.service.ProductGroupService;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupVo;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ProductGroupCommandServiceImplTest {

    @InjectMocks
    private ProductGroupCommandServiceImpl productGroupCommandService;

    @Mock
    private ProductGroupService productGroupService;

    @Before
    public void setUp() throws Exception{
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testAdd(){
        ProductGroupVo productGroupVo = new ProductGroupVo();
        when(productGroupService.add(any())).thenReturn(productGroupVo);
        ProductGroupAddDto productGroupAddDto = new ProductGroupAddDto();
        ProductGroupVo add = productGroupCommandService.add(productGroupAddDto);
        verify(productGroupService).add(any());
    }

    @Test
    public void testEdit(){
        ProductGroupVo productGroupVo = new ProductGroupVo();
        when(productGroupService.edit(any())).thenReturn(productGroupVo);
        ProductGroupEditDto editDto = new ProductGroupEditDto();
        ProductGroupVo edit = productGroupCommandService.edit(editDto);
        verify(productGroupService).edit(any());
    }

    @Test
    public void testDelete() {
        // Setup
        when(productGroupService.delete("id")).thenReturn(0);

        // Run the test
        final int result = productGroupCommandService.delete("id");

        // Verify the results
        assertEquals(0, result);
    }
}
