package com.zte.uedm.dcdigital.interfaces.inner.product.controller;

/* Started by AICoder, pid:x2766d3c9bq0cf71437f0a1cd0e7298a46e86bab */
import com.zte.uedm.dcdigital.application.category.ProductCategoryQueryService;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.inner.product.ProductInnerController;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductInnerControllerTest {

    @InjectMocks
    private ProductInnerController productInnerController;

    @Mock
    private ProductCategoryQueryService productCategoryQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常情况下的查询操作。
     */
    @Test
    public void given_validIds_when_queryByIds_then_returnSuccess() {
        // Given
        List<String> ids = Arrays.asList("1", "2", "3");
        List<ProductCategoryInfoVo> expectedDocuments = Arrays.asList(
                new ProductCategoryInfoVo(), new ProductCategoryInfoVo(), new ProductCategoryInfoVo()
        );
        when(productCategoryQueryService.queryByIds(ids)).thenReturn(expectedDocuments);

        // When
        BaseResult<List<ProductCategoryInfoVo>> result = productInnerController.queryByIds(ids);

        // Then
        assertEquals(BaseResult.success().getCode(), result.getCode());
        verify(productCategoryQueryService, times(1)).queryByIds(eq(ids));
    }
    /* Started by AICoder, pid:m0b9ec1a38we0fb14a390964f0d337139556a0d2 */
    @Test
    public void testQueryByPath_Success() {
        Set<String> pathList = new HashSet<>();

        // 模拟返回值
        List<ProductCategoryInfoVo> mockResult = new ArrayList<>();
        when(productCategoryQueryService.queryByPath(pathList)).thenReturn(mockResult);

        // 调用方法
        BaseResult<List<ProductCategoryInfoVo>> result = productInnerController.queryByPath(pathList);

        // 验证结果

        verify(productCategoryQueryService, times(1)).queryByPath(pathList);
    }
    /* Ended by AICoder, pid:m0b9ec1a38we0fb14a390964f0d337139556a0d2 */
}

/* Ended by AICoder, pid:x2766d3c9bq0cf71437f0a1cd0e7298a46e86bab */
