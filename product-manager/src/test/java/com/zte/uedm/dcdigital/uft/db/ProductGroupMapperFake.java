package com.zte.uedm.dcdigital.uft.db;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zte.udem.ft.util.FakeBranchFlag;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductGroupMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public class ProductGroupMapperFake implements ProductGroupMapper {

    @Override
    public List<ProductGroupPo> selectByNameAndProductCategoryId(String name, String productCategoryId) {
        if(FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_NULL)){
            return new ArrayList<>();
        }
        if(FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_NORMAL)){
            List<ProductGroupPo> productGroupPos = new ArrayList<>();
            ProductGroupPo productGroupPo1 = new ProductGroupPo();
            productGroupPo1.setId("1");
            productGroupPo1.setName("test");
            productGroupPo1.setPdmModelSpec("modelSpec");
            productGroupPo1.setProductCategoryId("123");
            productGroupPo1.setGroupLevel(1);
            productGroupPo1.setParentId(null);
            productGroupPo1.setPathName("");
            productGroupPo1.setPathId("1");
            productGroupPo1.setCreateTime("2024-12-01");
            productGroupPo1.setUpdateTime("2024-12-01");
            productGroupPo1.setCreateBy("user1");
            productGroupPo1.setUpdateBy("user1");
            productGroupPos.add(productGroupPo1);
            return productGroupPos;
        }

        if(FakeBranchFlag.FLAG.equals("Edit")){
            List<ProductGroupPo> productGroupPos = new ArrayList<>();
            ProductGroupPo productGroupPo1 = new ProductGroupPo();
            productGroupPo1.setId("2");
            productGroupPo1.setName("test");
            productGroupPo1.setPdmModelSpec("modelSpec");
            productGroupPo1.setProductCategoryId("123");
            productGroupPo1.setParentId(null);
            productGroupPo1.setGroupLevel(1);
            productGroupPo1.setPathName("test");
            productGroupPo1.setPathId("2");
            productGroupPos.add(productGroupPo1);
            return productGroupPos;
        }
        return null;
    }

    @Override
    public List<ProductGroupPo> selectByParentId(String id) {
        return null;
    }

    @Override
    public List<ProductGroupPo> selectNodeAllChildNode(String id) {
        return null;
    }

    @Override
    public List<ProductGroupPo> selectChildNodeByParentId(String parentId) {
        return null;
    }

    /* Started by AICoder, pid:2efcby31f6473151403a0ac4101f4b0c3d89cac1 */
    @Override
    public List<ProductGroupPo> selectChildNodeByParentIdList(List<String> parentIds) {
        return null;
    }

    @Override
    public ProductGroupEntity selectProductNodeById(String id) {
        return null;
    }
    /* Ended by AICoder, pid:2efcby31f6473151403a0ac4101f4b0c3d89cac1 */

    @Override
    public List<String> selectGroupIdByProductCategoryId(String categoryId) {
        return null;
    }

    @Override
    public List<String> selectAllChildNodeId(List<String> groupIds) {
        return null;
    }

    @Override
    public List<String> queryingLeafNodeGroups(String categoryId) {
        return null;
    }

    @Override
    public List<String> queryingLeafAllNodeGroups() {
        return null;
    }

    @Override
    public ProductGroupPo queryProductGroup(String productCategoryId, String groupName, String parentId) {
        return null;
    }

    @Override
    public List<ProductGroupPo> queryProductGroups(ProductGroupQueryDto queryDto) {
        if(FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_NULL)){
            return new ArrayList<>();
        }else if(FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_NORMAL)){
            List<ProductGroupPo> productGroupPos = new ArrayList<>();
            ProductGroupPo productGroupPo1 = new ProductGroupPo();
            productGroupPo1.setId("1");
            productGroupPo1.setName("Group 1");
            productGroupPo1.setPdmModelSpec("Model 1");
            productGroupPo1.setParentId(null);
            productGroupPo1.setPathName("");
            productGroupPo1.setPathId("1");
            productGroupPo1.setCreateTime("2024-12-01");
            productGroupPo1.setUpdateTime("2024-12-01");
            productGroupPo1.setCreateBy("user1");
            productGroupPo1.setUpdateBy("user1");
            productGroupPos.add(productGroupPo1);
            ProductGroupPo productGroupPo2 = new ProductGroupPo();
            productGroupPo2.setId("2");
            productGroupPo2.setName("Group 2");
            productGroupPo2.setPdmModelSpec("Model 2");
            productGroupPo2.setParentId("1");
            productGroupPo2.setPathName("Group 1");
            productGroupPo2.setPathId("1/2");
            productGroupPo2.setCreateTime("2024-12-02");
            productGroupPo2.setUpdateTime("2024-12-02");
            productGroupPo2.setCreateBy("user2");
            productGroupPo2.setUpdateBy("user2");
            productGroupPos.add(productGroupPo2);
            return productGroupPos;
        }
        return null;
    }

    @Override
    public List<ProductGroupPo> selectProductGroupAndMaterialByGroupId(String id) {
        if(FakeBranchFlag.FLAG.equals("Delete")) {
            List<ProductGroupPo> productGroupPos = new ArrayList<>();
            ProductGroupPo productGroupPo1 = new ProductGroupPo();
            productGroupPo1.setId("1");
            productGroupPo1.setName("Group 1");
            productGroupPo1.setPdmModelSpec("Model 1");
            productGroupPo1.setParentId(null);
            productGroupPo1.setPathName("");
            productGroupPo1.setPathId("1");
            productGroupPo1.setCreateTime("2024-12-01");
            productGroupPo1.setUpdateTime("2024-12-01");
            productGroupPo1.setCreateBy("user1");
            productGroupPo1.setUpdateBy("user1");
            productGroupPos.add(productGroupPo1);
            ProductGroupPo productGroupPo2 = new ProductGroupPo();
            productGroupPo2.setId("2");
            productGroupPo2.setName("Group 2");
            productGroupPo2.setPdmModelSpec("Model 2");
            productGroupPo2.setParentId("1");
            productGroupPo2.setPathName("Group 1");
            productGroupPo2.setPathId("1/2");
            productGroupPo2.setCreateTime("2024-12-02");
            productGroupPo2.setUpdateTime("2024-12-02");
            productGroupPo2.setCreateBy("user2");
            productGroupPo2.setUpdateBy("user2");
            productGroupPos.add(productGroupPo2);
            return productGroupPos;
        }
        return null;
    }

    @Override
    public ProductGroupPo selectMaterialApproveByGroupId(String parentId) {
        return null;
    }

    @Override
    public int insert(ProductGroupPo entity) {
        return 1;
    }

    @Override
    public int deleteById(Serializable id) {
        return 1;
    }

    @Override
    public int deleteById(ProductGroupPo entity) {
        return 0;
    }

    @Override
    public int deleteByMap(Map<String, Object> columnMap) {
        return 0;
    }

    @Override
    public int delete(Wrapper<ProductGroupPo> queryWrapper) {
        return 0;
    }

    @Override
    public int deleteBatchIds(Collection<?> idList) {
        return 0;
    }

    @Override
    public int updateById(ProductGroupPo entity) {
        return 1;
    }

    @Override
    public int update(ProductGroupPo entity, Wrapper<ProductGroupPo> updateWrapper) {
        return 0;
    }

    @Override
    public ProductGroupPo selectById(Serializable id) {
        if(FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_NORMAL)){
            ProductGroupPo productGroupPo = new ProductGroupPo();
            productGroupPo.setGroupLevel(3);
            productGroupPo.setProductCategoryId("123");
            return productGroupPo;
        }
        if(FakeBranchFlag.FLAG.equals("Edit")){
            ProductGroupPo productGroupPo = new ProductGroupPo();
            productGroupPo.setName("test1");
            productGroupPo.setId("1");
            productGroupPo.setPdmModelSpec("test1");
            productGroupPo.setGroupLevel(1);
            return productGroupPo;
        }
        if(FakeBranchFlag.FLAG.equals("Delete")){
            ProductGroupPo productGroupPo = new ProductGroupPo();
            productGroupPo.setName("test1");
            productGroupPo.setId("1");
            productGroupPo.setPdmModelSpec("test1");
            productGroupPo.setGroupLevel(1);
            return productGroupPo;
        }
        return null;
    }

    @Override
    public List<ProductGroupPo> selectBatchIds(Collection<? extends Serializable> idList) {
        List<ProductGroupPo> productGroupPos = new ArrayList<>();
        ProductGroupPo productGroupPo1 = new ProductGroupPo();
        productGroupPo1.setId("1");
        productGroupPo1.setName("Group 1");
        productGroupPo1.setPdmModelSpec("Model 1");
        productGroupPo1.setParentId(null);
        productGroupPo1.setPathName("");
        productGroupPo1.setPathId("1");
        productGroupPo1.setCreateTime("2024-12-01");
        productGroupPo1.setUpdateTime("2024-12-01");
        productGroupPo1.setCreateBy("user1");
        productGroupPo1.setUpdateBy("user1");
        productGroupPos.add(productGroupPo1);
        ProductGroupPo productGroupPo2 = new ProductGroupPo();
        productGroupPo2.setId("2");
        productGroupPo2.setName("Group 2");
        productGroupPo2.setPdmModelSpec("Model 2");
        productGroupPo2.setParentId("1");
        productGroupPo2.setPathName("Group 1");
        productGroupPo2.setPathId("1/2");
        productGroupPo2.setCreateTime("2024-12-02");
        productGroupPo2.setUpdateTime("2024-12-02");
        productGroupPo2.setCreateBy("user2");
        productGroupPo2.setUpdateBy("user2");
        productGroupPos.add(productGroupPo2);
        return productGroupPos;
    }

    @Override
    public List<ProductGroupPo> selectByMap(Map<String, Object> columnMap) {
        return null;
    }

    @Override
    public Long selectCount(Wrapper<ProductGroupPo> queryWrapper) {
        return null;
    }

    @Override
    public List<ProductGroupPo> selectList(Wrapper<ProductGroupPo> queryWrapper) {
        return null;
    }

    @Override
    public List<Map<String, Object>> selectMaps(Wrapper<ProductGroupPo> queryWrapper) {
        return null;
    }

    @Override
    public List<Object> selectObjs(Wrapper<ProductGroupPo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<ProductGroupPo>> P selectPage(P page, Wrapper<ProductGroupPo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<Map<String, Object>>> P selectMapsPage(P page, Wrapper<ProductGroupPo> queryWrapper) {
        return null;
    }
}
