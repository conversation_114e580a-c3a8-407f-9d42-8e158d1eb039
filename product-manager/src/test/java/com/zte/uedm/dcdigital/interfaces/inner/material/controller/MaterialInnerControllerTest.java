package com.zte.uedm.dcdigital.interfaces.inner.material.controller;

/* Started by AICoder, pid:57b2bd2e911cce6142ae0825b0383a30a565dd27 */
/* Started by AICoder, pid:u635e3ea7454e7d1480a0b5390c3ab97a5906afc */
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceCommandService;
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceQueryService;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.common.bean.dto.ApprovalInformationDto;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.product.MaterialWithExtendInfoVo;
import com.zte.uedm.dcdigital.common.enums.DocumentRelateResourceTypeEnum;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class MaterialInnerControllerTest {

    @InjectMocks
    private MaterialInnerController materialInnerController;

    @Mock
    private MaterialMaintenanceCommandService materialMaintenanceCommandService;
    private ApprovalInformationDto informationDto;
    @Mock
    private MaterialMaintenanceQueryService materialMaintenanceQueryService;

    @Before
    public void setUp() {
        informationDto = new ApprovalInformationDto();
        informationDto.setApprovalId("1");
        informationDto.setApprovalResult(GlobalConstants.ONE);
        informationDto.setApprovalStatus(2);
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testApprovalWithdrawal() {
        // 测试审批撤回方法
        BaseResult<Boolean> result = materialInnerController.approvalWithdrawal("approval123");
        assertEquals("0", String.valueOf(result.getCode()));  // 验证返回的结果是否成功
    }

    @Test(expected = BusinessException.class)
    public void testApprovalWithdrawal_EmptyApprovalId() {
        materialInnerController.approvalWithdrawal("");
    }

    @Test
    public void testApprovalWithdrawal_ValidApprovalId() {
        BaseResult<Boolean> result = materialInnerController.approvalWithdrawal("1");
        assertTrue(result.isSuccess());
        verify(materialMaintenanceCommandService).withdrawByApprovalId("1");
    }

    @Test(expected = BusinessException.class)
    public void testUpdateAfterApproval_NullInformationDto() {
        materialInnerController.updateAfterApproval(null);
    }

    @Test(expected = BusinessException.class)
    public void testUpdateAfterApproval_EmptyApprovalId() {
        informationDto.setApprovalId("");
        materialInnerController.updateAfterApproval(informationDto);
    }

    @Test(expected = BusinessException.class)
    public void testUpdateAfterApproval_InvalidApprovalResult() {
        informationDto.setApprovalResult(2);
        materialInnerController.updateAfterApproval(informationDto);
    }

    @Test(expected = BusinessException.class)
    public void testUpdateAfterApproval_InvalidApprovalStatus() {
        informationDto.setApprovalStatus(3);
        materialInnerController.updateAfterApproval(informationDto);
    }


    @Test
    public void testUpdateAfterApproval_Approved() {
        BaseResult<Boolean> result = materialInnerController.updateAfterApproval(informationDto);
        assertTrue(result.isSuccess());
        verify(materialMaintenanceCommandService).approvalInformationUpdate(informationDto);
    }

    @Test
    public void testUpdateAfterApproval_NotApproved() {
        informationDto.setApprovalResult(GlobalConstants.ZERO);
        informationDto.setApprovalStatus(2);
        BaseResult<Boolean> result = materialInnerController.updateAfterApproval(informationDto);
        assertTrue(result.isSuccess());
        verify(materialMaintenanceCommandService).withdrawByApprovalId("1");
    }

    @Test
    public void selectNamesByIds() {
        Mockito.when(materialMaintenanceCommandService.selectResourceInfo(Mockito.any(),
                Mockito.eq(DocumentRelateResourceTypeEnum.MATERIAL.getCode()))).thenReturn(buildResourceInfoList());
        BaseResult<List<IdNameBean>> baseResult = materialInnerController.queryNamesByIds(Collections.singletonList("id1"));
        Assert.assertEquals(1, baseResult.getData().size());
    }

    @Test
    public void selectNamesByIds_emptyList() {
        BaseResult<List<IdNameBean>> baseResult = materialInnerController.queryNamesByIds(Collections.emptyList());
        Assert.assertEquals(0, baseResult.getData().size());
    }

    private List<DocumentCitedVo> buildResourceInfoList() {
        List<DocumentCitedVo> list = new ArrayList<>();
        DocumentCitedVo item1 = new DocumentCitedVo();
        item1.setId("material1");
        item1.setName("material name");
        list.add(item1);
        return list;
    }

    @Test
    public void queryMaterialByIds() {
        Mockito.when(materialMaintenanceQueryService.queryMaterialByIds(Mockito.any())).thenReturn(Collections.emptyList());
        BaseResult<List<MaterialWithExtendInfoVo>> baseResult = materialInnerController.queryMaterialByIds(Collections.singletonList("id1"));
        Mockito.verify(materialMaintenanceQueryService, Mockito.times(1)).queryMaterialByIds(Mockito.any());
        Assert.assertTrue(baseResult.isSuccess());
    }
}
/* Ended by AICoder, pid:u635e3ea7454e7d1480a0b5390c3ab97a5906afc */
/* Ended by AICoder, pid:57b2bd2e911cce6142ae0825b0383a30a565dd27 */
