package com.zte.uedm.dcdigital.interfaces.web.product.dto;/* Started by AICoder, pid:y1c9599bf93d61a142040a903005ac782c11e457 */
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandDto;
import static org.junit.Assert.*;

import java.math.BigDecimal;
import java.time.LocalDate;

import org.junit.Before;
import org.junit.Test;

public class ProductBrandDtoTest {

    private ProductBrandDto productBrandDto;

    @Before
    public void setUp() {
        productBrandDto = new ProductBrandDto();
    }

    @Test
    public void testSettersAndGetters() {
        // 测试所有字段的设置和获取
        productBrandDto.setId("1");
        assertEquals("1", productBrandDto.getId());

        productBrandDto.setProductCategoryId("PC123");
        assertEquals("PC123", productBrandDto.getProductCategoryId());

        productBrandDto.setBrandName("Brand A");
        assertEquals("Brand A", productBrandDto.getBrandName());

        productBrandDto.setSelectionAttribute(0);
        assertEquals(Integer.valueOf(0), productBrandDto.getSelectionAttribute());

        productBrandDto.setTagName("Tag A");
        assertEquals("Tag A", productBrandDto.getTagName());

        productBrandDto.setSelectionScore(new BigDecimal("8.5"));
        assertEquals(new BigDecimal("8.5"), productBrandDto.getSelectionScore());

        productBrandDto.setProcurementMode("1");
        assertEquals("1", productBrandDto.getProcurementMode());

        productBrandDto.setExpiryDate("2025-12-31");
        assertEquals("2025-12-31", productBrandDto.getExpiryDate());

        productBrandDto.setQuoteId("Q123");
        assertEquals("Q123", productBrandDto.getQuoteId());

        productBrandDto.setCreateTime("2023-01-01 12:00:00");
        assertEquals("2023-01-01 12:00:00", productBrandDto.getCreateTime());

        productBrandDto.setUpdateTime("2023-01-02 12:00:00");
        assertEquals("2023-01-02 12:00:00", productBrandDto.getUpdateTime());

        productBrandDto.setCreateBy("user1");
        assertEquals("user1", productBrandDto.getCreateBy());

        productBrandDto.setUpdateBy("user2");
        assertEquals("user2", productBrandDto.getUpdateBy());
    }

    @Test
    public void testToStringMethod() {
        // 设置一些字段以测试toString方法
        productBrandDto.setId("1");
        productBrandDto.setBrandName("Brand A");

        String expectedString = "ProductBrandDto(id=1, productCategoryId=null, brandName=Brand A, selectionAttribute=null, tagName=null, selectionScore=null, procurementMode=null, expiryDate=null, quoteId=null, createTime=null, updateTime=null, createBy=null, updateBy=null)";
        assertEquals(expectedString, productBrandDto.toString());
    }
}

/* Ended by AICoder, pid:y1c9599bf93d61a142040a903005ac782c11e457 */