package com.zte.uedm.dcdigital.startup;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.context.event.ApplicationStartedEvent;

@ExtendWith(MockitoExtension.class)
class ApplicationStartedListenerTest {
    @InjectMocks
    private ApplicationStartedListener applicationStartedListener;
    @Mock
    private ApplicationStartedEvent event;

    @Test
    void onApplicationEvent() {
        Assertions.assertDoesNotThrow(() -> applicationStartedListener.onApplicationEvent(event));
    }
}