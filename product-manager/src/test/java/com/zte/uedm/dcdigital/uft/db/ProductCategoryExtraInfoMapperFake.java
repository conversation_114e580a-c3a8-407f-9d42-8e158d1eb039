/* Started by AICoder, pid:6987dn1216r6b8014d540810d1b19e4bc5329d4e */
package com.zte.uedm.dcdigital.uft.db;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.zte.udem.ft.util.FakeBranchFlag;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCategoryExtraInfoMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryExtraInfoPo;

import java.io.InputStream;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public class ProductCategoryExtraInfoMapperFake implements ProductCategoryExtraInfoMapper {

    private final ObjectMapper objectMapper = new ObjectMapper();


    public ProductCategoryExtraInfoMapperFake() {
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }
    private List<ProductCategoryExtraInfoPo> selectAll() {
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("data/ProductCategory/ProductCategoryExtraInfo.json");
        CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, ProductCategoryExtraInfoPo.class);
        try {
            List<ProductCategoryExtraInfoPo> pos = objectMapper.readValue(inputStream, collectionType);
            return pos;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    public ProductCategoryExtraInfoPo selectByProductCategoryId(String productCategoryId) {
        if (FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_NORMAL)) {
            List<ProductCategoryExtraInfoPo> extraInfoPos = selectAll();
            return extraInfoPos.stream().filter(p -> p.getProductCategoryId().equals(productCategoryId)).findFirst().orElse(null);
        }
        return null;
    }

    @Override
    public void deleteByProductCategoryId(String productCategoryId) {

    }

    @Override
    public void updateByProductCategoryId(ProductCategoryExtraInfoPo extraInfoPo) {

    }

    @Override
    public void updateExtendedWarrantyFactorAndNonStandardItems(ProductCategoryExtraInfoPo extraInfoPo) {

    }

    @Override
    public int insert(ProductCategoryExtraInfoPo entity) {
        return 0;
    }

    @Override
    public int deleteById(Serializable id) {
        return 0;
    }

    @Override
    public int deleteById(ProductCategoryExtraInfoPo entity) {
        return 0;
    }

    @Override
    public int deleteByMap(Map<String, Object> columnMap) {
        return 0;
    }

    @Override
    public int delete(Wrapper<ProductCategoryExtraInfoPo> queryWrapper) {
        return 0;
    }

    @Override
    public int deleteBatchIds(Collection<?> idList) {
        return 0;
    }

    @Override
    public int updateById(ProductCategoryExtraInfoPo entity) {
        return 0;
    }

    @Override
    public int update(ProductCategoryExtraInfoPo entity, Wrapper<ProductCategoryExtraInfoPo> updateWrapper) {
        return 0;
    }

    @Override
    public ProductCategoryExtraInfoPo selectById(Serializable id) {
        return null;
    }

    @Override
    public List<ProductCategoryExtraInfoPo> selectBatchIds(Collection<? extends Serializable> idList) {
        return null;
    }

    @Override
    public List<ProductCategoryExtraInfoPo> selectByMap(Map<String, Object> columnMap) {
        return null;
    }

    @Override
    public Long selectCount(Wrapper<ProductCategoryExtraInfoPo> queryWrapper) {
        return null;
    }

    @Override
    public List<ProductCategoryExtraInfoPo> selectList(Wrapper<ProductCategoryExtraInfoPo> queryWrapper) {
        return selectAll();
    }

    @Override
    public List<Map<String, Object>> selectMaps(Wrapper<ProductCategoryExtraInfoPo> queryWrapper) {
        return null;
    }

    @Override
    public List<Object> selectObjs(Wrapper<ProductCategoryExtraInfoPo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<ProductCategoryExtraInfoPo>> P selectPage(P page, Wrapper<ProductCategoryExtraInfoPo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<Map<String, Object>>> P selectMapsPage(P page, Wrapper<ProductCategoryExtraInfoPo> queryWrapper) {
        return null;
    }
}
/* Ended by AICoder, pid:6987dn1216r6b8014d540810d1b19e4bc5329d4e */