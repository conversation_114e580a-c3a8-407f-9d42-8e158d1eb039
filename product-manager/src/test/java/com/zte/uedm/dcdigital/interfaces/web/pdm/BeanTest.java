package com.zte.uedm.dcdigital.interfaces.web.pdm;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.uedm.dcdigital.common.util.PojoTestUtil;
import com.zte.uedm.dcdigital.domain.model.material.entity.MaterialWithExtendInfoEntity;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.CategoryDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.MaterialDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.ProductLineDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.SpecModelDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.*;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class BeanTest {

    /* Started by AICoder, pid:354937190ei715e149d80a10c08beb3e69b27456 */
    @Test
    public void test() throws Exception {
        ProductLineDto productLineDto = new ProductLineDto();
        PojoTestUtil.TestForPojo(ProductLineDto.class);
        Assert.assertNotEquals("", productLineDto.toString());
        productLineDto.setProductLineNo("123");
        Assert.assertNotNull(productLineDto.getProductLineNo());

        ProductLineVo productLineVo = new ProductLineVo();
        PojoTestUtil.TestForPojo(ProductLineVo.class);
        Assert.assertNotEquals("", productLineVo.toString());
        productLineVo.setProductLineNo("123");
        Assert.assertNotNull(productLineVo.getProductLineNo());

        CategoryDto categoryDto = new CategoryDto();
        PojoTestUtil.TestForPojo(CategoryDto.class);
        Assert.assertNotEquals("", categoryDto.toString());
        categoryDto.setCategoryNo("123");
        Assert.assertNotNull(categoryDto.getCategoryNo());

        CategoryVo categoryVo = new CategoryVo();
        PojoTestUtil.TestForPojo(CategoryVo.class);
        Assert.assertNotEquals("", categoryVo.toString());
        categoryVo.setCategoryNo("123");
        Assert.assertNotNull(categoryVo.getCategoryNo());

        SpecModelDto modelDto = new SpecModelDto();
        PojoTestUtil.TestForPojo(SpecModelDto.class);
        Assert.assertNotEquals("", modelDto.toString());
        modelDto.setProductLineNo("123");
        Assert.assertNotNull(modelDto.getProductLineNo());

        PojoTestUtil.TestForPojo(MaterialWithExtendInfoEntity.class);
    }
    /* Ended by AICoder, pid:354937190ei715e149d80a10c08beb3e69b27456 */

    /* Started by AICoder, pid:67a1fme64du9d46149d90a3d2033af293367112e */
    @Test
    public void basicQueryResponseVoTest() throws Exception {
        BasicQueryResponseVo basicQueryResponseVo = new BasicQueryResponseVo();
        PojoTestUtil.TestForPojo(BasicQueryResponseVo.class);
        Assert.assertNotEquals("", basicQueryResponseVo.toString());

        BasicQueryResponseVo.Code code = new BasicQueryResponseVo.Code();
        PojoTestUtil.TestForPojo(BasicQueryResponseVo.Code.class);
        Assert.assertNotEquals("", code.toString());
        code.setCode("123");
        Assert.assertNotNull(code.getCode());

        BasicQueryResponseVo.Bo bo = new BasicQueryResponseVo.Bo();
        PojoTestUtil.TestForPojo(BasicQueryResponseVo.Bo.class);
        Assert.assertNotEquals("", bo.toString());
        bo.setTotal(1);
        Assert.assertEquals(1, bo.getTotal());

        BasicQueryResponseVo.Rows rows = new BasicQueryResponseVo.Rows();
        PojoTestUtil.TestForPojo(BasicQueryResponseVo.Rows.class);
        Assert.assertNotEquals("", rows.toString());
        rows.setNo("123");
        Assert.assertNotNull(rows.getNo());

        basicQueryResponseVo.setCode(code);
        Assert.assertNotNull(basicQueryResponseVo.getCode());
        List< BasicQueryResponseVo.Rows> list = new ArrayList<>();
        list.add(rows);
        bo.setRows(list);
        basicQueryResponseVo.setBo(bo);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(basicQueryResponseVo);
        BasicQueryResponseVo deserializedResponse = objectMapper.readValue(jsonString, BasicQueryResponseVo.class);
        Assert.assertEquals("123", deserializedResponse.getCode().getCode());
    }
    /* Ended by AICoder, pid:67a1fme64du9d46149d90a3d2033af293367112e */

    @Test
    public void testBasicQueryResponseVoDeserializeWithStringBo() throws IOException {
        String json = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0004\",\n" +
                "        \"msgId\": \"RetCode.ValidationError\",\n" +
                "        \"msg\": \"validation error\"\n" +
                "    },\n" +
                "    \"bo\": \"输入编号不存在，请检查后重新输入\",\n" +
                "    \"other\": null\n" +
                "}";

        // 反序列化 JSON 字符串为 BasicQueryResponseVo 对象
        ObjectMapper objectMapper = new ObjectMapper();
        BasicQueryResponseVo response = objectMapper.readValue(json, BasicQueryResponseVo.class);

        // 验证 code 字段
        Assert.assertNotNull(response.getCode());

    }

    /* Started by AICoder, pid:h4b45h1b156e3b2140cc0bcfb1b6f41779b735fa */
    @Test
    public void categoryRelationVoTest() throws Exception {
        CategoryRelationVo categoryRelationVo = new CategoryRelationVo();
        PojoTestUtil.TestForPojo(CategoryRelationVo.class);
        Assert.assertNotEquals("", categoryRelationVo.toString());

        CategoryRelationVo.Code code = new CategoryRelationVo.Code();
        PojoTestUtil.TestForPojo(CategoryRelationVo.Code.class);
        Assert.assertNotEquals("", code.toString());
        code.setCode("123");
        Assert.assertNotNull(code.getCode());

        CategoryRelationVo.BO bo = new CategoryRelationVo.BO();
        PojoTestUtil.TestForPojo(CategoryRelationVo.BO.class);
        Assert.assertNotEquals("", bo.toString());
        bo.setItemNo("123");
        Assert.assertNotNull(bo.getItemNo());

        CategoryRelationVo.BO.Item item = new CategoryRelationVo.BO.Item();
        PojoTestUtil.TestForPojo(CategoryRelationVo.BO.Item.class);
        Assert.assertNotEquals("", item.toString());
        item.setItemNo("123");
        Assert.assertNotNull(item.getItemNo());

        categoryRelationVo.setCode(code);
        Assert.assertNotNull(categoryRelationVo.getCode());

        categoryRelationVo.setBo(bo);
        List<CategoryRelationVo.BO> boList = categoryRelationVo.getBoList();
        Assert.assertNotNull(boList);

        ArrayList<CategoryRelationVo.BO> bos = new ArrayList<>();
        bos.add(bo);
        categoryRelationVo.setBo(bos);
        List<CategoryRelationVo.BO> boList1 = categoryRelationVo.getBoList();
        Assert.assertEquals(1, boList1.size());
    }

    @Test
    public void categoryRelationVoSingleObjectSerializationAndDeserializatioTest() throws IOException {
        CategoryRelationVo.Code code = new CategoryRelationVo.Code();
        code.setCode("200");
        code.setMsgId("MSG-001");
        code.setMsg("Success");

        CategoryRelationVo.BO.Item parentItem = new CategoryRelationVo.BO.Item();
        parentItem.setItemNo("100");
        parentItem.setCnName("父类别1");
        parentItem.setItemLevel(0);
        parentItem.setStatus("Active");

        CategoryRelationVo.BO.Item childItem = new CategoryRelationVo.BO.Item();
        childItem.setItemNo("200");
        childItem.setCnName("子类别1");
        childItem.setItemLevel(2);
        childItem.setStatus("Active");

        CategoryRelationVo.BO bo = new CategoryRelationVo.BO();
        bo.setItemNo("12345");
        bo.setCnName("产品类别1");
        bo.setEnName("Product Category 1");
        bo.setEnNameAb("PC1");
        bo.setItemLevel(1);
        bo.setItemParentList(new ArrayList<>(Arrays.asList(parentItem)));
        bo.setItemChildList(new ArrayList<>(Arrays.asList(childItem)));
        bo.setProductModel("Model A");

        // 创建 CategoryRelationVo 对象并设置属性
        CategoryRelationVo response = new CategoryRelationVo();
        response.setCode(code);
        response.setBo(bo);
        response.setOther("Some other data");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(response);
        CategoryRelationVo deserializedResponse = objectMapper.readValue(jsonString, CategoryRelationVo.class);
        Assert.assertEquals("200", deserializedResponse.getCode().getCode());
    }

    @Test
    public void categoryRelationVoArraySerializationAndDeserializatioTest() throws IOException {
        CategoryRelationVo.Code code = new CategoryRelationVo.Code();
        code.setCode("200");
        code.setMsgId("MSG-001");
        code.setMsg("Success");

        CategoryRelationVo.BO.Item parentItem = new CategoryRelationVo.BO.Item();
        parentItem.setItemNo("100");
        parentItem.setCnName("父类别1");
        parentItem.setItemLevel(0);
        parentItem.setStatus("Active");

        CategoryRelationVo.BO.Item childItem = new CategoryRelationVo.BO.Item();
        childItem.setItemNo("200");
        childItem.setCnName("子类别1");
        childItem.setItemLevel(2);
        childItem.setStatus("Active");

        CategoryRelationVo.BO bo = new CategoryRelationVo.BO();
        bo.setItemNo("12345");
        bo.setCnName("产品类别1");
        bo.setEnName("Product Category 1");
        bo.setEnNameAb("PC1");
        bo.setItemLevel(1);
        bo.setItemParentList(new ArrayList<>(Arrays.asList(parentItem)));
        bo.setItemChildList(new ArrayList<>(Arrays.asList(childItem)));
        bo.setProductModel("Model A");

        CategoryRelationVo response = new CategoryRelationVo();
        response.setCode(code);
        response.setBo(Arrays.asList(bo));
        response.setOther("Some other data");
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(response);
        CategoryRelationVo deserializedResponse = objectMapper.readValue(jsonString, CategoryRelationVo.class);
        Assert.assertEquals("200", deserializedResponse.getCode().getCode());
    }
    /* Ended by AICoder, pid:h4b45h1b156e3b2140cc0bcfb1b6f41779b735fa */


    /* Started by AICoder, pid:955f5e6fbazc5cf143e50b8880537381fde33dd1 */
    @Test
    public void productResponseVoTest() throws Exception {
        ProductResponseVo productResponseVo = new ProductResponseVo();
        PojoTestUtil.TestForPojo(ProductResponseVo.class);
        Assert.assertNotEquals("", productResponseVo.toString());

        ProductResponseVo.BO bo = new ProductResponseVo.BO();
        PojoTestUtil.TestForPojo(ProductResponseVo.BO.class);
        Assert.assertNotEquals("", bo.toString());
        bo.setCode("123");
        Assert.assertNotNull(bo.getCode());

        ProductResponseVo.Code code = new ProductResponseVo.Code();
        PojoTestUtil.TestForPojo(ProductResponseVo.Code.class);
        Assert.assertNotEquals("", code.toString());
        code.setCode("123");
        Assert.assertNotNull(code.getCode());

        ProductResponseVo.Other other = new ProductResponseVo.Other();
        PojoTestUtil.TestForPojo(ProductResponseVo.Other.class);
        Assert.assertNotEquals("", other.toString());
        other.setTotal(123);
        Assert.assertEquals(123, other.getTotal());

        productResponseVo.setCode(code);
        Assert.assertNotNull(productResponseVo.getCode());

        productResponseVo.setBo(Arrays.asList(bo));
        List<ProductResponseVo.BO> boList = productResponseVo.getBoList();
        Assert.assertEquals(1, boList.size());

        productResponseVo.setBo(bo);
        List<ProductResponseVo.BO> boList1 = productResponseVo.getBoList();
        Assert.assertNotNull(boList1);
    }

    @Test
    public void productResponseVoSingleObjectSerializationAndDeserializatioTest() throws IOException {
        ProductResponseVo.Code code = new ProductResponseVo.Code();
        code.setCode("200");
        code.setMsgId("MSG-001");
        code.setMsg("Success");

        ProductResponseVo.BO bo = new ProductResponseVo.BO();
        bo.setCode("123");

        ProductResponseVo.Other other = new ProductResponseVo.Other();
        other.setTotal(123);

        ProductResponseVo productResponseVo = new ProductResponseVo();
        productResponseVo.setCode(code);
        productResponseVo.setBo(bo);
        productResponseVo.setOther(other);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(productResponseVo);
        ProductResponseVo deserializedResponse = objectMapper.readValue(jsonString, ProductResponseVo.class);
        Assert.assertEquals("200", deserializedResponse.getCode().getCode());
    }

    @Test
    public void productResponseVoListObjectSerializationAndDeserializatioTest() throws IOException {
        ProductResponseVo.Code code = new ProductResponseVo.Code();
        code.setCode("200");
        code.setMsgId("MSG-001");
        code.setMsg("Success");

        ProductResponseVo.BO bo = new ProductResponseVo.BO();
        bo.setCode("123");

        ProductResponseVo.Other other = new ProductResponseVo.Other();
        other.setTotal(123);

        ProductResponseVo productResponseVo = new ProductResponseVo();
        productResponseVo.setCode(code);
        productResponseVo.setBo(Collections.singletonList(bo));
        productResponseVo.setOther(other);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(productResponseVo);
        ProductResponseVo deserializedResponse = objectMapper.readValue(jsonString, ProductResponseVo.class);
        Assert.assertEquals("200", deserializedResponse.getCode().getCode());
    }
    /* Ended by AICoder, pid:955f5e6fbazc5cf143e50b8880537381fde33dd1 */

    /* Started by AICoder, pid:l9d2bec2922d4eb148dc0945805d6544fa395209 */
    @Test
    public void testSbomDetailResponseVo() throws Exception {
        SbomDetailResponseVo sbomDetailResponseVo = new SbomDetailResponseVo();
        PojoTestUtil.TestForPojo(SbomDetailResponseVo.class);
        Assert.assertNotEquals("", sbomDetailResponseVo.toString());

        SbomDetailResponseVo.Code code = new SbomDetailResponseVo.Code();
        PojoTestUtil.TestForPojo(SbomDetailResponseVo.Code.class);
        Assert.assertNotEquals("", code.toString());
        code.setCode("200");
        Assert.assertNotNull(code.getCode());

        SbomDetailResponseVo.BO bo = new SbomDetailResponseVo.BO();
        PojoTestUtil.TestForPojo(SbomDetailResponseVo.BO.class);
        Assert.assertNotEquals("", bo.toString());
        bo.setSbomNo("SBOM-001");
        bo.setNameCn("产品1");
        bo.setNameEn("Product 1");
        bo.setSaleStatusBefore("Active");
        bo.setUnitCn("个");
        bo.setDesInnerCn("内部描述1");
        Assert.assertNotNull(bo.getSbomNo());

        sbomDetailResponseVo.setCode(code);
        Assert.assertNotNull(sbomDetailResponseVo.getCode());

        List<SbomDetailResponseVo.BO> boList = new ArrayList<>();
        boList.add(bo);
        String result1 = SbomDetailResponseVo.getSaleStatusBeforBySbomNo(boList, "SBOM-001");
        Assert.assertEquals("Active", result1);
        String result2 = SbomDetailResponseVo.getSaleStatusBeforBySbomNo(boList, "SBOM-003");
        Assert.assertNull(result2);
        String result3 = SbomDetailResponseVo.getSaleStatusBeforBySbomNo(null, "SBOM-001");
        Assert.assertNull(result3);

        String result4 = SbomDetailResponseVo.getUnitforSbomNo(boList, "SBOM-001");
        Assert.assertEquals("个", result4);
        String result5 = SbomDetailResponseVo.getUnitforSbomNo(boList, "SBOM-003");
        Assert.assertNull(result5);
        String result6 = SbomDetailResponseVo.getUnitforSbomNo(null, "SBOM-001");
        Assert.assertNull(result6);

        String result7 = SbomDetailResponseVo.getDescriptionforSbomNo(boList, "SBOM-001");
        Assert.assertEquals("内部描述1", result7);
        String result8 = SbomDetailResponseVo.getDescriptionforSbomNo(boList, "SBOM-003");
        Assert.assertNull(result8);
        String result9 = SbomDetailResponseVo.getDescriptionforSbomNo(null, "SBOM-001");
        Assert.assertNull(result9);

        /* Started by AICoder, pid:w011ef3780wc0101440508f73068ea0164c63b7f */
        String result10 = SbomDetailResponseVo.getnameCnforSbomNo(boList, "SBOM-001");
        Assert.assertEquals("产品1", result10);
        String result11 = SbomDetailResponseVo.getnameCnforSbomNo(boList, "SBOM-003");
        Assert.assertNull(result11);
        String result12 = SbomDetailResponseVo.getnameCnforSbomNo(null, "SBOM-001");
        Assert.assertNull(result12);
        /* Ended by AICoder, pid:w011ef3780wc0101440508f73068ea0164c63b7f */
    }
    /* Ended by AICoder, pid:l9d2bec2922d4eb148dc0945805d6544fa395209 */

    /* Started by AICoder, pid:k8698u22eel78ed147ab0af2918e6d6a33331584 */
    @Test
    public void testMaterialVo() throws Exception {
        MaterialVo materialVo = new MaterialVo();
        PojoTestUtil.TestForPojo(MaterialVo.class);
        Assert.assertNotEquals("", materialVo.toString());
        materialVo.setPartNo("123");
        Assert.assertNotNull(materialVo.getPartNo());
        MaterialVo materialVo1 = new MaterialVo("123", "test", "123", "456", "test", "个", "test", "test");
        Assert.assertNotNull(materialVo1);
    }

    @Test
    public void testProductLineVo(){
        ProductLineVo productLineVo = new ProductLineVo("123", "test", "456", "test");
        Assert.assertNotNull(productLineVo);
    }

    @Test
    public void testSbomDirectComPosinfoResponseVo() throws Exception{
        SbomDirectComPosinfoResponseVo sbomDirectComPosinfoResponseVo = new SbomDirectComPosinfoResponseVo();
        PojoTestUtil.TestForPojo(SbomDirectComPosinfoResponseVo.class);
        Assert.assertNotEquals("", sbomDirectComPosinfoResponseVo.toString());

        SbomDirectComPosinfoResponseVo.Code code = new SbomDirectComPosinfoResponseVo.Code();
        PojoTestUtil.TestForPojo(SbomDirectComPosinfoResponseVo.Code.class);
        Assert.assertNotEquals("", code.toString());
        code.setCode("123");
        Assert.assertNotNull(code.getCode());

        SbomDirectComPosinfoResponseVo.Bo bo = new SbomDirectComPosinfoResponseVo.Bo();
        PojoTestUtil.TestForPojo(SbomDirectComPosinfoResponseVo.Bo.class);
        Assert.assertNotEquals("", bo.toString());
        bo.setCurrent(1);
        Assert.assertEquals(1, bo.getCurrent());

        SbomDirectComPosinfoResponseVo.Rows rows = new SbomDirectComPosinfoResponseVo.Rows();
        PojoTestUtil.TestForPojo(SbomDirectComPosinfoResponseVo.Rows.class);
        Assert.assertNotEquals("", rows.toString());
        rows.setPartNo("123");
        rows.setBaseNo("123");
        Assert.assertNotNull(rows.getPartNo());

        sbomDirectComPosinfoResponseVo.setBo(bo);
        sbomDirectComPosinfoResponseVo.setCode(code);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(sbomDirectComPosinfoResponseVo);
        SbomDirectComPosinfoResponseVo sbomDirectComPosinfoResponse = objectMapper.readValue(jsonString, SbomDirectComPosinfoResponseVo.class);
        Assert.assertEquals("123", sbomDirectComPosinfoResponse.getCode().getCode());

        List<SbomDirectComPosinfoResponseVo.Rows> list = new ArrayList<>();
        list.add(rows);
        String partNo = SbomDirectComPosinfoResponseVo.getPratNo(list, "123");
        Assert.assertEquals("123", partNo);

        String result = SbomDirectComPosinfoResponseVo.getPratNo(list, null);
        Assert.assertNull(result);
    }

    @Test
    public void testSbomInfoResponseVo() throws Exception {
        SbomInfoResponseVo sbomInfoResponseVo = new SbomInfoResponseVo();
        PojoTestUtil.TestForPojo(SbomInfoResponseVo.class);
        Assert.assertNotEquals("", sbomInfoResponseVo.toString());

        SbomInfoResponseVo.Code code = new SbomInfoResponseVo.Code();
        PojoTestUtil.TestForPojo(SbomInfoResponseVo.Code.class);
        Assert.assertNotEquals("", code.toString());
        code.setCode("200");
        Assert.assertNotNull(code.getCode());

        SbomInfoResponseVo.Bo bo = new SbomInfoResponseVo.Bo();
        PojoTestUtil.TestForPojo(SbomInfoResponseVo.Bo.class);
        Assert.assertNotEquals("", bo.toString());
        bo.setOrgId("1");
        Assert.assertEquals("1", bo.getOrgId());

        SbomInfoResponseVo.Other other = new SbomInfoResponseVo.Other();
        PojoTestUtil.TestForPojo(SbomInfoResponseVo.Other.class);
        Assert.assertNotEquals("", other.toString());
        other.setTotal(1);
        Assert.assertEquals(1, other.getTotal());

        sbomInfoResponseVo.setBo(Arrays.asList(bo));
        List<SbomInfoResponseVo.Bo> boList = sbomInfoResponseVo.getBoList();
        Assert.assertEquals(1, boList.size());

        sbomInfoResponseVo.setBo(bo);
        List<SbomInfoResponseVo.Bo> boList1 = sbomInfoResponseVo.getBoList();
        Assert.assertNotNull(boList1);

        sbomInfoResponseVo.setCode(code);
        Assert.assertNotNull(sbomInfoResponseVo.getCode());
    }

    @Test
    public void sbomInfoResponseVoSingleObjectSerializationAndDeserializatioTest() throws IOException {
        SbomInfoResponseVo.Code code = new SbomInfoResponseVo.Code();
        code.setCode("200");
        code.setMsgId("MSG-001");
        code.setMsg("Success");

        SbomInfoResponseVo.Bo bo = new SbomInfoResponseVo.Bo();
        bo.setSbomNo("123");

        SbomInfoResponseVo.Other other = new SbomInfoResponseVo.Other();
        other.setTotal(123);

        SbomInfoResponseVo sbomInfoResponseVo = new SbomInfoResponseVo();
        sbomInfoResponseVo.setCode(code);
        sbomInfoResponseVo.setBo(bo);
        sbomInfoResponseVo.setOther(other);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(sbomInfoResponseVo);
        SbomInfoResponseVo deserializedResponse = objectMapper.readValue(jsonString, SbomInfoResponseVo.class);
        Assert.assertEquals("200", deserializedResponse.getCode().getCode());
    }

    @Test
    public void sbomInfoResponseVoListObjectSerializationAndDeserializatioTest() throws IOException {
        SbomInfoResponseVo.Code code = new SbomInfoResponseVo.Code();
        code.setCode("200");
        code.setMsgId("MSG-001");
        code.setMsg("Success");

        SbomInfoResponseVo.Bo bo = new SbomInfoResponseVo.Bo();
        bo.setOrgId("123");

        SbomInfoResponseVo.Other other = new SbomInfoResponseVo.Other();
        other.setTotal(123);

        SbomInfoResponseVo sbomInfoResponseVo = new SbomInfoResponseVo();
        sbomInfoResponseVo.setCode(code);
        sbomInfoResponseVo.setBo(Collections.singletonList(bo));
        sbomInfoResponseVo.setOther(other);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(sbomInfoResponseVo);
        SbomInfoResponseVo deserializedResponse = objectMapper.readValue(jsonString, SbomInfoResponseVo.class);
        Assert.assertEquals("200", deserializedResponse.getCode().getCode());
    }

    @Test
    public void testSpecModelVo() throws Exception {
        SpecModelVo specModelVo = new SpecModelVo();
        PojoTestUtil.TestForPojo(SpecModelVo.class);
        Assert.assertNotEquals("", specModelVo.toString());
        specModelVo.setLargeCategoryNo("123456");
        Assert.assertEquals("123456", specModelVo.getLargeCategoryNo());

        SpecModelVo modelVo = new SpecModelVo("123", "test", "456", "test1", "789", "test2");
        Assert.assertEquals("456", modelVo.getLargeCategoryNo());
    }

    @Test
    public void testMaterialDto() throws Exception {
        MaterialDto materialDto = new MaterialDto();
        PojoTestUtil.TestForPojo(MaterialDto.class);
        Assert.assertNotEquals("", materialDto.toString());
        materialDto.setSbomNo("123456");
        Assert.assertEquals("123456", materialDto.getSbomNo());
    }
    /* Ended by AICoder, pid:k8698u22eel78ed147ab0af2918e6d6a33331584 */
}
