package com.zte.uedm.dcdigital.interfaces.web.material.controller;

import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceCommandService;
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.MaterialStatusEnums;
import com.zte.uedm.dcdigital.domain.common.enums.OperateEnums;
import com.zte.uedm.dcdigital.domain.common.enums.PurchaseModeEnums;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MaterialUportalControllerTest {
    @InjectMocks
    private MaterialUportalController materialUportalController;

    @Mock
    private MaterialMaintenanceCommandService materialMaintenanceCommandService;

    @Mock
    private MaterialMaintenanceQueryService materialMaintenanceQueryService;

    @Mock
    private HttpServletResponse response;

    @Mock
    private FormDataMultiPart file;

    /* Started by AICoder, pid:sfbc291dbbr93351457d0a4d703fc6315b408243 */
    @Test
    public void testGetById_ValidId() {
        String id = "123";
        MaterialDetailVo expectedDetail = new MaterialDetailVo();



        when(materialMaintenanceQueryService.queryMaterialDetailsById(id)).thenReturn(expectedDetail);

        BaseResult<MaterialDetailVo> result = materialUportalController.getById(id);

        assertNotNull(result);
        assertEquals(expectedDetail, result.getData());

        verify(materialMaintenanceQueryService, times(1)).queryMaterialDetailsById(id);
    }

    @Test
    public void testGetById_EmptyId() {
        String id = "";
        BaseResult<MaterialDetailVo> result = materialUportalController.getById(id);
        assertNotNull(result);
        assertNull(result.getData());

        verify(materialMaintenanceQueryService, never()).queryMaterialDetailsById(anyString());
    }
    /* Ended by AICoder, pid:sfbc291dbbr93351457d0a4d703fc6315b408243 */
    /* Started by AICoder, pid:edf0dfb462qe991141990ba521006826e0702e38 */
    @Test
    public void addMaterial() {
        MaterialAddDto testData = getTestData();
        BaseResult<Object> baseResult = materialUportalController.addMaterial(testData);
        Assert.assertTrue(baseResult.isSuccess());
    }
    @Test(expected = BusinessException.class)
    public void testUnknownPurchaseMode() {
        MaterialAddDto testData = getTestData();
        testData.setPurchaseMode("UNKNOWN_PURCHASE_MODE");
        materialUportalController.addMaterial(testData);
    }
    @Test(expected = BusinessException.class)
    public void testWarrantyPeriodOutOfRange_Lower() {
        MaterialAddDto testData = getTestData();
        testData.setWarrantyPeriod("-1");
        materialUportalController.addMaterial(testData);
    }
    @Test(expected = BusinessException.class)
    public void testWarrantyPeriodFormatError() {
        MaterialAddDto testData = getTestData();
        testData.setWarrantyPeriod("abc");
        materialUportalController.addMaterial(testData);
    }
    @Test(expected = BusinessException.class)
    public void testExpirationDateFormatError() {
        MaterialAddDto testData = getTestData();
        testData.setExpirationDate("2023/01/01");
        materialUportalController.addMaterial(testData);
    }

    @Test(expected = BusinessException.class)
    public void testExpirationDateExpired() {
        MaterialAddDto testData = getTestData();
        testData.setExpirationDate("2024-12-12");
        materialUportalController.addMaterial(testData);
    }
    @Test(expected = BusinessException.class)
    public void testUnsupportedOperationType() {
        MaterialAddDto testData = getTestData();
        testData.setOperate("INVALID_OPERATE");
        materialUportalController.addMaterial(testData);
    }
    @Test(expected = BusinessException.class)
    public void testNameLengthExceedsLimit() {
        MaterialAddDto testData = getTestData();
        testData.setName(StringUtils.repeat("a", GlobalConstants.NAME_LENGTH_LIMIT + 1));
        materialUportalController.addMaterial(testData);
    }

    @Test(expected = BusinessException.class)
    public void testSupplierLengthExceedsLimit() {
        MaterialAddDto testData = getTestData();
        testData.setSupplier(StringUtils.repeat("a", GlobalConstants.NAME_LENGTH_LIMIT + 1));
        materialUportalController.addMaterial(testData);
    }

    @Test(expected = BusinessException.class)
    public void testBrandLengthExceedsLimit() {
        MaterialAddDto testData = getTestData();
        testData.setBrand(StringUtils.repeat("a", GlobalConstants.NAME_LENGTH_LIMIT + 1));
        materialUportalController.addMaterial(testData);
    }
    @Test(expected = BusinessException.class)
    public void recommendedLevelLimit() {
        MaterialAddDto testData = getTestData();
        testData.setRecommendedLevel("E");
        materialUportalController.addMaterial(testData);
    }

    @Test
    public void editMaterial() {
        MaterialEditDto materialEditDto = new MaterialEditDto();
        materialEditDto.setOperate(OperateEnums.SUBMIT.getId());
        materialEditDto.setSalesCode("salesCode");
        materialEditDto.setName("name");
        materialEditDto.setProductionCode("productionCode");
        materialEditDto.setGroupId("groupId");
        materialEditDto.setUnit("id");
        materialEditDto.setPurchaseMode(PurchaseModeEnums.values()[0].getId());
        BaseResult<Object> baseResult = materialUportalController.editMaterial(materialEditDto);
        Assert.assertFalse(baseResult.isSuccess());
        materialEditDto.setId("id");
        BaseResult<Object> baseResult2 = materialUportalController.editMaterial(materialEditDto);
        Assert.assertTrue(baseResult2.isSuccess());
    }

    public MaterialAddDto getTestData() {
        MaterialAddDto materialAddDto = new MaterialAddDto();
        materialAddDto.setOperate(OperateEnums.SUBMIT.getId());
        materialAddDto.setSalesCode("salesCode");
        materialAddDto.setName("name");
        materialAddDto.setUnit("name");
        materialAddDto.setProductionCode("productionCode");
        materialAddDto.setGroupId("groupId");
        materialAddDto.setWarrantyPeriod("10");
        materialAddDto.setExpirationDate("2066-10-10");
        materialAddDto.setPurchaseMode(PurchaseModeEnums.values()[0].getId());
        return materialAddDto;
    }
    /* Ended by AICoder, pid:edf0dfb462qe991141990ba521006826e0702e38 */
    @Test
    public void deleteMaterial() {
        BaseResult<Object> baseResult = materialUportalController.deleteMaterial("1");
        Assert.assertTrue(baseResult.isSuccess());
    }

    @Test
    public void exportTemplate() {
        BaseResult<Object> baseResult = materialUportalController.exportTemplate(null);
        Assert.assertTrue(baseResult.isSuccess());
    }
    @Test
    public void testQueryAllMaterialStatus() {
        List<IdNameBean> list = Arrays.stream(MaterialStatusEnums.values())
                .map(e -> new IdNameBean(e.getId(), I18nUtil.getI18nFromString(e.getName())))
                .collect(Collectors.toList());
        Mockito.when(materialMaintenanceQueryService.queryAllMaterialStatus()).thenReturn(list);
        BaseResult<List<IdNameBean>> result = materialUportalController.queryAllMaterialStatus();

        assertNotNull(result);
    }

    @Test
    public void testQueryAllPurchaseMode() {

        BaseResult<List<IdNameBean>> result = materialUportalController.queryAllPurchaseMode();

        assertNotNull(result);
    }

    @Test
    public void testQueryAllSalesStatus() {

        List<String> stringList = new ArrayList<>();
        stringList.add("123");
        Mockito.when(materialMaintenanceQueryService.queryAllSalesStatus()).thenReturn(stringList);
        BaseResult<List<String>> result = materialUportalController.queryAllSalesStatus();

        assertEquals(2, result.getData().size());
    }
    @Test
    public void testPdmManualSync_Success() {
        String salesCode = "testSalesCode";
        String materialId = "materialId";
        doNothing().when(materialMaintenanceCommandService).pdmManualSync(salesCode,materialId);

        BaseResult result = materialUportalController.pdmManualSync(salesCode,materialId);

        verify(materialMaintenanceCommandService).pdmManualSync(salesCode,materialId);
        assertTrue(result.isSuccess());
    }

    @Test(expected = BusinessException.class)
    public void testPdmManualSync_fail() {
        materialUportalController.pdmManualSync("","");
    }

    @Test
    public void testSalesCodeUniquenessCheck_Unique() {
        String salesCode = "uniqueSalesCode";

        BaseResult<Boolean> result = materialUportalController.processOperation(salesCode,"id",0);

        assertTrue(result.isSuccess());
    }

    /* Started by AICoder, pid:of16csa4b0e312714223093ed066926ec6c5f857 */
    @Test
    public void testSalesCodeUniquenessCheck_ValidInput() {
        String id = "123";
        String operation = "add";

        BaseResult<Boolean> result = materialUportalController.processOperation(id, operation,0);

        verify(materialMaintenanceCommandService, times(1)).handlingMaterialOperation(id, operation,null);
        assertTrue(result.isSuccess());
    }

    @Test
    public void testSalesCodeUniquenessCheck_EmptyId() {
        String id = "";
        String operation = "add";

        BaseResult<Boolean> result = materialUportalController.processOperation(id, operation,0);

        verify(materialMaintenanceCommandService, never()).handlingMaterialOperation(anyString(), anyString(),null);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testSalesCodeUniquenessCheck_EmptyOperation() {
        String id = "123";
        String operation = "";

        BaseResult<Boolean> result = materialUportalController.processOperation(id, operation,0);

        verify(materialMaintenanceCommandService, never()).handlingMaterialOperation(anyString(), anyString(),null);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testBatchOperation_ValidInput() {

        BatchOperateDto batchOperateDto = new BatchOperateDto();
        batchOperateDto.setOperate("4");
        batchOperateDto.setIds(new ArrayList<>());
        BaseResult<Boolean> result = materialUportalController.batchOperation(batchOperateDto);

        verify(materialMaintenanceCommandService, times(1)).batchHandlingMaterial(Mockito.anyList(), Mockito.anyString(),null);
        assertTrue(result.isSuccess());
    }

    /* Ended by AICoder, pid:of16csa4b0e312714223093ed066926ec6c5f857 */
    /* Started by AICoder, pid:ca7bfi0a53pc94d1455c0800b056c22cc963b1ae */
    @Test
    public void testQueryMaterialVersionWithValidId() {
        String validId = "validId";
        List<MaterialVersionVo> expectedVersions = Arrays.asList(new MaterialVersionVo(), new MaterialVersionVo());

        when(materialMaintenanceQueryService.queryMaterialVersionById(validId)).thenReturn(expectedVersions);

        BaseResult<List<MaterialVersionVo>> result = materialUportalController.queryMaterialVersion(validId);


        verify(materialMaintenanceQueryService, times(1)).queryMaterialVersionById(validId);
    }

    @Test
    public void testQueryMaterialVersionWithEmptyId() {
        String emptyId = "";

        BaseResult<List<MaterialVersionVo>> result = materialUportalController.queryMaterialVersion(emptyId);

        assertEquals("1001", String.valueOf(result.getCode()));
        verify(materialMaintenanceQueryService, never()).queryMaterialVersionById(anyString());
    }
    /* Ended by AICoder, pid:ca7bfi0a53pc94d1455c0800b056c22cc963b1ae */

    @Test
    public void importTemplateTest() {

        try {
            materialUportalController.importTemplate(file,"");
        } catch (BusinessException e) {
            assertEquals(StatusCode.INVALID_PARAMETER.getMessage(), e.getMessage());
        }
        materialUportalController.importTemplate(file,"ca");
        verify(materialMaintenanceCommandService, times(1)).importTemplate(file,"ca");
    }
    /* Started by AICoder, pid:b78fftddb7768d51424b0826f0a12f2f7cc5e1b1 */
    @Test
    public void specialImportTest() {

        try {
            materialUportalController.specialImport(file,"");
        } catch (BusinessException e) {
            assertEquals(StatusCode.INVALID_PARAMETER.getMessage(), e.getMessage());
        }
        materialUportalController.specialImport(file,"ca");
        verify(materialMaintenanceCommandService, times(1)).specialBatchImport(file,"ca");
    }
    /* Ended by AICoder, pid:b78fftddb7768d51424b0826f0a12f2f7cc5e1b1 */
    @Test
    public void batchSubmitTest() {
        materialUportalController.batchSubmit(Arrays.asList(new MaterialBatchAddDto()));
        verify(materialMaintenanceCommandService, times(1)).batchSubmit(Mockito.anyList());
    }
    /* Started by AICoder, pid:958aer0d64m9683144b50a3eb1944c9fcdb1ad97 */


    @Test
    public void testQueryMaterialByCondition_InvalidParams() {
        try {
            materialUportalController.queryMaterialByCondition(null);
        } catch (BusinessException e) {
            assertEquals(StatusCode.INVALID_PARAMETER.getMessage(), e.getMessage());
        }
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        try {
            materialUportalController.queryMaterialByCondition(dto);
        } catch (BusinessException e) {
            assertEquals(StatusCode.INVALID_PARAMETER.getMessage(), e.getMessage());
        }
        dto.setProductCategoryId("id");
        dto.setStartDate("2024-12-24 12:30:20");
        try {
            materialUportalController.queryMaterialByCondition(dto);
        } catch (BusinessException e) {
            assertEquals(StatusCode.INVALID_PARAMETER.getMessage(), e.getMessage());
        }
        dto.setStartDate("2024-12-24");
        dto.setEndDate("2024-12-24 12:30:20");
        try {
            materialUportalController.queryMaterialByCondition(dto);
        } catch (BusinessException e) {
            assertEquals(StatusCode.INVALID_PARAMETER.getMessage(), e.getMessage());
        }
    }

    @Test
    public void testQueryMaterialByCondition_Paging() {
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        dto.setProductCategoryId("123");
        PageVO<MaterialVo> pageVO = new PageVO<>();
        Mockito.when(materialMaintenanceQueryService.pagingQueryMaterialByCondition(dto)).thenReturn(pageVO);
        BaseResult<Object> result = materialUportalController.queryMaterialByCondition(dto);
        assertEquals(StatusCode.SUCCESS.getCode(), result.getCode());
        assertEquals(pageVO, result.getData());
    }

    @Test
    public void testQueryMaterialByCondition_BatchOperation() {
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        dto.setProductCategoryId("id");
        dto.setOperation(OperateEnums.TEMPORARILY_STORE.getId());
        try {
            materialUportalController.queryMaterialByCondition(dto);
        } catch (BusinessException e) {
            assertEquals(StatusCode.INVALID_PARAMETER.getMessage(), e.getMessage());
        }
        dto.setGroupId(Collections.singletonList("123"));
        try {
            materialUportalController.queryMaterialByCondition(dto);
        } catch (BusinessException e) {
            assertEquals(StatusCode.INVALID_PARAMETER.getMessage(), e.getMessage());
        }

        dto.setOperation(OperateEnums.LISTING.getId());
        List<MaterialVo> materialList = Collections.singletonList(new MaterialVo());
        Mockito.when(materialMaintenanceQueryService.queryMaterialByCondition(dto)).thenReturn(materialList);
        BaseResult<Object> result = materialUportalController.queryMaterialByCondition(dto);
        assertEquals(StatusCode.SUCCESS.getCode(), result.getCode());
        assertEquals(materialList, result.getData());
    }
    @Test
    public void testQueryFuzzy() {
        MaterialFuzzyDto fuzzyDto = new MaterialFuzzyDto();
        PageVO<MaterialFuzzyVo> pageVO = new PageVO<>();
        Mockito.when(materialMaintenanceQueryService.queryMaterialFuzzy(fuzzyDto)).thenReturn(pageVO);
        BaseResult<Object> result = materialUportalController.queryFuzzy(fuzzyDto);
        assertEquals(StatusCode.SUCCESS.getCode(), result.getCode());
        assertEquals(pageVO, result.getData());
    }

    @Test
    public void testQueryAccurate() {
        MaterialAccurateDto accurateDto = new MaterialAccurateDto();
        PageVO<MaterialFuzzyVo> pageVO = new PageVO<>();
        Mockito.when(materialMaintenanceQueryService.queryMaterialAccurate(accurateDto)).thenReturn(pageVO);
        BaseResult<Object> result = materialUportalController.queryAccurate(accurateDto);
        assertEquals(StatusCode.SUCCESS.getCode(), result.getCode());
        assertEquals(pageVO, result.getData());
    }
    /* Ended by AICoder, pid:958aer0d64m9683144b50a3eb1944c9fcdb1ad97 */

    @Test
    public void fuzzyQuery() {
        Mockito.when(materialMaintenanceQueryService.pagingFuzzyQuery(Mockito.any())).thenReturn(new PageVO<>(0, new ArrayList<>()));
        BaseResult<PageVO<MaterialVo>> baseResult = materialUportalController.fuzzyQuery(new MaterialFuzzyQueryDto());
        Assert.assertTrue(baseResult.isSuccess());
    }


    @Test
    public void getSpecification() {
        List<MaterialSpecificationVo> result = materialMaintenanceQueryService.getSpecification("052902000369");

        // 返回成功结果，包含查询到的物料规格信息列表
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void importTemplateTest2() {
        List<TemplateImportVo> importVoList = new ArrayList<>();
        Mockito.when(materialMaintenanceCommandService.importTemplate(file,"123")).thenReturn(importVoList);
        materialUportalController.importTemplate(file,"123");
        verify(materialMaintenanceCommandService, times(1)).importTemplate(file,"123");
    }

}