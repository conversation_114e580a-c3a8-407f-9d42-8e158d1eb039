/* Started by AICoder, pid:j391dbaf76x6d1d14c010891f059cd4eac8388f1 */
package com.zte.uedm.dcdigital.interfaces.web.pdm.controller;

import com.zte.uedm.dcdigital.application.pdm.PdmApiQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.ProductLineDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.ProductLineVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

public class PdmAportalControllerTest {

    @InjectMocks
    private PdmAportalController pdmAportalController;

    @Mock
    private PdmApiQueryService pdmApiQueryService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSelectProductLineList(){
        PageVO<ProductLineVo> pageVO = new PageVO<>();
        ProductLineVo productLineVo = new ProductLineVo();
        productLineVo.setProductLineName("test");
        pageVO.setList(Arrays.asList(productLineVo));
        ProductLineDto productLineDto = new ProductLineDto();
        productLineDto.setProductLineNo("123");
        Mockito.when(pdmApiQueryService.getProductLineList(productLineDto)).thenReturn(pageVO);
        BaseResult<PageVO<ProductLineVo>> pageVOBaseResult = pdmAportalController.selectProductLineList(productLineDto);
        Assert.assertEquals(0, pageVOBaseResult.getCode().intValue());
    }
}
/* Ended by AICoder, pid:j391dbaf76x6d1d14c010891f059cd4eac8388f1 */
