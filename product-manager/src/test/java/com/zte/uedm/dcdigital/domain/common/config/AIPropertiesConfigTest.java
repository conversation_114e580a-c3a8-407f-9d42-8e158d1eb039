package com.zte.uedm.dcdigital.domain.common.config;

import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

public class AIPropertiesConfigTest {

    private AIPropertiesConfig aiPropertiesConfig;

    @Before
    public void setUp() {
        aiPropertiesConfig = new AIPropertiesConfig();
    }

    @Test
    public void testDefaultValues() {

        assertNull(aiPropertiesConfig.getUrl());
        assertNull(aiPropertiesConfig.getApiKey());
        assertNull(aiPropertiesConfig.getAppId());
    }

    @Test
    public void testSettersAndGetters() {

        aiPropertiesConfig.setUrl("https://example.com");
        aiPropertiesConfig.setAppId("name");
        aiPropertiesConfig.setApiKey("number");


        assertEquals("https://example.com", aiPropertiesConfig.getUrl());
        assertEquals("name", aiPropertiesConfig.getAppId());
        assertEquals("number", aiPropertiesConfig.getApiKey());
    }

}

