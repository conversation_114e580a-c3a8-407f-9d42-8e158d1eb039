/* Started by AICoder, pid:8d84a291e9ve81c1433a0bead0b3a540d1e0ffa2 */
package com.zte.uedm.dcdigital.domain.aggregate.model.convert;

import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.model.product.convert.ProductCategoryConverter;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import org.junit.Assert;
import org.junit.Test;

public class ProductCategoryConverterTest {



    @Test
    public void convertProductCategoryAddDtoToEntityTest() {
        ProductCategoryAddDto productCategoryAddDto = new ProductCategoryAddDto();
        productCategoryAddDto.setProductName("11");
        ProductCategoryEntity categoryEntity = ProductCategoryConverter.convertProductCategoryAddDtoToEntity(productCategoryAddDto);
        Assert.assertNotNull(categoryEntity.getId()); // Ensure ID is generated
        Assert.assertEquals("11", categoryEntity.getProductName());
        Assert.assertNull(categoryEntity.getDescription());  // Ensure default values for optional fields
    }

    @Test
    public void convertProductCategoryEntityToVoTest() {
        ProductCategoryEntity productCategoryEntity = new ProductCategoryEntity();
        productCategoryEntity.setId("testId");
        productCategoryEntity.setProductName("11");
        productCategoryEntity.setDescription("Description");
        ProductCategoryVo productCategoryVo = ProductCategoryConverter.convertProductCategoryVo(productCategoryEntity);
        Assert.assertEquals("testId", productCategoryVo.getId());
        Assert.assertEquals("11", productCategoryVo.getProductName());
        Assert.assertEquals("Description", productCategoryVo.getDescription());
    }
}
/* Ended by AICoder, pid:8d84a291e9ve81c1433a0bead0b3a540d1e0ffa2 */