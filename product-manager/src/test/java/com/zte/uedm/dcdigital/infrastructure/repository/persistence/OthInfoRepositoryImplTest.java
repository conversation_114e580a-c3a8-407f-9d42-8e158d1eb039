package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:53261f2f09c63a1147330898f1aeb53d4ef25f4c */
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zte.uedm.dcdigital.domain.model.material.entity.OthInfoEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.OthInfoMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.OthInfoPo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OthInfoRepositoryImplTest {

    @Mock
    private OthInfoMapper othInfoMapper;

    @InjectMocks
    private OthInfoRepositoryImpl othInfoRepository;

    @BeforeEach
    public void setUp() {
        // Ensure the baseMapper is correctly set to the mock mapper
    }

    @Test
    public void testQueryById() {
        String id = "1";
        OthInfoPo po = new OthInfoPo();
        OthInfoEntity entity = new OthInfoEntity();

        when(othInfoMapper.selectById(id)).thenReturn(po);

        OthInfoEntity result = othInfoRepository.queryById(id);

        verify(othInfoMapper, times(1)).selectById(id);
    }

    @Test
    public void testDeleteOthSalesCodeById() {
        String id = "1";
        try{
            othInfoRepository.deleteOthSalesCodeById(id);
        } catch (Exception e){
            Assertions.assertNull(e.getMessage());
        }

    }

    @Test
    public void testBatchAddEmptyList() {
        List<OthInfoEntity> list = Collections.emptyList();

        othInfoRepository.batchAdd(list);

        verify(othInfoMapper, never()).insert(any(OthInfoPo.class));
    }

    @Test
    public void testBatchAddNonEmptyList() {
        List<OthInfoEntity> list = Arrays.asList(new OthInfoEntity(), new OthInfoEntity());
        List<OthInfoPo> pos = Arrays.asList(new OthInfoPo(), new OthInfoPo());
        try{
            othInfoRepository.batchAdd(list);
        } catch (Exception e){
            Assertions.assertNotNull(e.getMessage());
        }


    }

    @Test
    public void testQueryOthSalesCodeBySalesCodeListEmpty() {
        List<String> salesCodeList = Collections.emptyList();

        List<OthInfoEntity> result = othInfoRepository.queryOthSalesCodeBySalesCodeList(salesCodeList);

        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryOthSalesCodeBySalesCodeListNonEmpty() {
        List<String> salesCodeList = Arrays.asList("code1", "code2");
        List<OthInfoPo> pos = Arrays.asList(new OthInfoPo(), new OthInfoPo());


        when(othInfoMapper.selectList(any())).thenReturn(pos);

        List<OthInfoEntity> result = othInfoRepository.queryOthSalesCodeBySalesCodeList(salesCodeList);

        verify(othInfoMapper, times(1)).selectList(any());
    }

    @Test
    public void testBatchDeleteByIdsEmptyList() {
        List<String> ids = Collections.emptyList();

        othInfoRepository.batchDeleteByIds(ids);

        verify(othInfoMapper, never()).deleteBatchIds(anyList());
    }

    @Test
    public void testBatchDeleteByIdsNonEmptyList() {
        List<String> ids = Arrays.asList("1", "2");
        try{
            othInfoRepository.batchDeleteByIds(ids);
        } catch (Exception e){
            Assertions.assertNull(e.getMessage());
        }
    }
    /* Started by AICoder, pid:b69cdu6d65i37ec14e290abe705ca8814e438f78 */
    @Test
    public void testQueryBySalesCode_Found() {
        String salesCode = "181234567890";
        OthInfoPo mockOthInfoPo = mock(OthInfoPo.class);
        when(mockOthInfoPo.getSalesCode()).thenReturn(salesCode);

        when(othInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(mockOthInfoPo);
        OthInfoEntity expectedEntity = new OthInfoEntity();

        OthInfoEntity result = othInfoRepository.queryBySalesCode(salesCode);

        assertNotNull(result);
        verify(othInfoMapper, times(1)).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testAddOthInfo() {
        OthInfoEntity entity = new OthInfoEntity();
        othInfoRepository.addOthInfo(entity);
        verify(othInfoMapper, times(1)).insert(Mockito.any());
    }

    @Test
    public void testEditOthInfo() {
        OthInfoEntity entity = new OthInfoEntity();
        othInfoRepository.editOthInfo(entity);
        verify(othInfoMapper, times(1)).updateById(Mockito.any());
    }
    /* Ended by AICoder, pid:b69cdu6d65i37ec14e290abe705ca8814e438f78 */
}
/* Ended by AICoder, pid:53261f2f09c63a1147330898f1aeb53d4ef25f4c */