/* Started by AICoder, pid:x6c0ef723fpa45914ba40865c12bfc4874f5564a */
package com.zte.uedm.dcdigital.uft.db;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.zte.udem.ft.util.FakeBranchFlag;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCategoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.MaterialStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.MaterialStatisticsVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductSubcategoryWithCategoryVo;

import java.io.InputStream;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

public class ProductCategoryMapperFake implements ProductCategoryMapper {

    private final ObjectMapper objectMapper = new ObjectMapper();


    public ProductCategoryMapperFake() {
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }
    private List<ProductCategoryPo> selectAll() {
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("data/ProductCategory/ProductCategories.json");
        CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, ProductCategoryPo.class);
        try {
            List<ProductCategoryPo> pos = objectMapper.readValue(inputStream, collectionType);
            return pos;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    public List<ProductCategoryPo> selectByCondition(String categoryName, Integer nodeType, String parentId) {

        if (FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_NORMAL)) {
            List<ProductCategoryPo> productCategoryPos = selectAll();
            return productCategoryPos.stream()
                    .filter(p-> Objects.equals(p.getNodeType(), nodeType))
                    .collect(Collectors.toList());

        }
        if (FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_EXP)) {
            throw new RuntimeException();
        }
        return null;
    }

    @Override
    public List<ProductCategoryVo> queryProductSubclasses(List<String> ids, Integer nodeType) {
        return null;
    }

    @Override
    public List<ProductSubcategoryVo> queryUserProductSubcategory(List<String> ids, Integer nodeType) {
        return null;
    }

    @Override
    public List<ProductCategoryEntity> selectCurentNodeAllChildNode(String currentNodeId) {
        return null;
    }

    @Override
    public int queryByIdAndName(String id, String name) {
        return 0;
    }

    @Override
    public List<ProductSubcategoryVo> queryAllProductSubcategory(Integer nodeType) {
        return null;
    }

    @Override
    public int insert(ProductCategoryPo entity) {
        return 0;
    }

    @Override
    public int deleteById(Serializable id) {
        return 0;
    }

    @Override
    public int deleteById(ProductCategoryPo entity) {
        return 0;
    }

    @Override
    public int deleteByMap(Map<String, Object> columnMap) {
        return 0;
    }

    @Override
    public int delete(Wrapper<ProductCategoryPo> queryWrapper) {
        return 0;
    }

    @Override
    public int deleteBatchIds(Collection<?> idList) {
        return 0;
    }

    @Override
    public int updateById(ProductCategoryPo entity) {
        return 0;
    }

    @Override
    public int update(ProductCategoryPo entity, Wrapper<ProductCategoryPo> updateWrapper) {
        return 0;
    }

    @Override
    public ProductCategoryPo selectById(Serializable id) {
        if (FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_NORMAL)) {
            List<ProductCategoryPo> productCategoryPos = selectAll();
            return productCategoryPos.stream().filter(p -> p.getId().equals(id)).findFirst().orElse(null);
        }
        return null;
    }

    @Override
    public List<ProductCategoryPo> selectBatchIds(Collection<? extends Serializable> idList) {
        return null;
    }

    @Override
    public List<ProductCategoryPo> selectByMap(Map<String, Object> columnMap) {
        return null;
    }

    @Override
    public Long selectCount(Wrapper<ProductCategoryPo> queryWrapper) {
        if (FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_NORMAL)) {
            return 0L;
        } else if (FakeBranchFlag.FLAG.equals(FakeBranchFlag.DATA_EXP)) {
            throw new RuntimeException();
        } else if (FakeBranchFlag.FLAG.equals("nameIsExists")) {
            return 1L;
        }
        return null;
    }

    @Override
    public List<ProductCategoryPo> selectList(Wrapper<ProductCategoryPo> queryWrapper) {
        return null;
    }

    @Override
    public List<Map<String, Object>> selectMaps(Wrapper<ProductCategoryPo> queryWrapper) {
        return null;
    }

    @Override
    public List<Object> selectObjs(Wrapper<ProductCategoryPo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<ProductCategoryPo>> P selectPage(P page, Wrapper<ProductCategoryPo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<Map<String, Object>>> P selectMapsPage(P page, Wrapper<ProductCategoryPo> queryWrapper) {
        return null;
    }
    /* Started by AICoder, pid:sf701865a0a587a14cff083ba0eb3f138b10f23c */
    @Override
    public List<String> selectByCategoryId(String productCategoryId) {
        /**
         * 根据产品类别ID查询品牌信息。
         *
         * @param productCategoryId 产品类别ID
         * @return 品牌信息列表
         */
        return null;
    }

    @Override
    public List<ProductSubcategoryVo> selectByName(String categoryName, Integer nodeType) {
        return null;
    }

    @Override
    public List<ProductCategoryPo> queryAllProductCategoryList() {
        return null;
    }

    /* Started by AICoder, pid:1ee72f95e6u140b14d480b23208ce0150af01160 */
    @Override
    public List<ProductSubcategoryVo> selectByNodeType(Integer nodeType) {
        /**
         * 根据节点类型查询产品子类别信息。
         *
         * @param nodeType 节点类型
         * @return 包含产品子类别信息的 ProductSubcategoryVo 对象列表
         */
        return null;
    }

    @Override
    public List<ProductCategoryPo> getAllBigProductCategoryByLineNo(String productLineNo) {
        return null;
    }

    @Override
    public List<ProductCategoryPo> getAllSmallProductNoByBigId(List<String> bigCategoryIds) {
        return null;
    }

    @Override
    public List<ProductCategoryInfoVo> selectIdsByParentId(String parentId) {
        return null;
    }

    @Override
    public List<String> getProductSubcategoryId() {
        return null;
    }

    @Override
    public List<ProductSubcategoryWithCategoryVo> querySubcategoriesWithCategoryByParentId(String parentId) {
        return Collections.emptyList();
    }

    @Override
    public List<MaterialStatisticsVo> queryMaterialStatisticsByTimeRange(MaterialStatisticsQueryDto queryDto, List<String> timePoints) {
        return Collections.emptyList();
    }
    /* Ended by AICoder, pid:1ee72f95e6u140b14d480b23208ce0150af01160 */
    /* Ended by AICoder, pid:sf701865a0a587a14cff083ba0eb3f138b10f23c */
}
/* Ended by AICoder, pid:x6c0ef723fpa45914ba40865c12bfc4874f5564a */