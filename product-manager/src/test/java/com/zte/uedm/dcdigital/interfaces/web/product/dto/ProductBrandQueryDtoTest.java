package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import org.junit.Before;
import org.junit.Test;
import java.math.BigDecimal;

import static org.junit.Assert.*;

public class ProductBrandQueryDtoTest {

    private ProductBrandQueryDto productBrandQueryDto;

    @Before
    public void setUp() {
        productBrandQueryDto = new ProductBrandQueryDto();
    }

    @Test
    public void testGettersAndSetters() {
        // Arrange
        String id = "123e4567";
        String productCategoryId = "123e4567-e89b-12d3-a456-426655440000";
        String brandName = "Apple";
        String tagName = "High,Quality";
        Integer selectionAttribute = 0;
        Integer quoteId = 123;
        BigDecimal selectionScoreMin = new BigDecimal("1.0");
        BigDecimal selectionScoreMax = new BigDecimal("10.0");
        String procurementMode = "1";
        String expiryDateBegin = "2023-01-01";
        String expiryDateEnd = "2023-12-31";
        String updateTimeBegin = "2023-01-01";
        String updateTimeEnd = "2023-12-31";

        // Act
        productBrandQueryDto.setId(id);
        productBrandQueryDto.setProductCategoryId(productCategoryId);
        productBrandQueryDto.setBrandName(brandName);
        productBrandQueryDto.setTagName(tagName);
        productBrandQueryDto.setSelectionAttribute(selectionAttribute);
        productBrandQueryDto.setQuoteId(quoteId);
        productBrandQueryDto.setSelectionScoreMin(selectionScoreMin);
        productBrandQueryDto.setSelectionScoreMax(selectionScoreMax);
        productBrandQueryDto.setProcurementMode(procurementMode);
        productBrandQueryDto.setExpiryDateBegin(expiryDateBegin);
        productBrandQueryDto.setExpiryDateEnd(expiryDateEnd);
        productBrandQueryDto.setUpdateTimeBegin(updateTimeBegin);
        productBrandQueryDto.setUpdateTimeEnd(updateTimeEnd);

        // Assert
        assertEquals(id, productBrandQueryDto.getId());
        assertEquals(productCategoryId, productBrandQueryDto.getProductCategoryId());
        assertEquals(brandName, productBrandQueryDto.getBrandName());
        assertEquals(tagName, productBrandQueryDto.getTagName());
        assertEquals(selectionAttribute, productBrandQueryDto.getSelectionAttribute());
        assertEquals(quoteId, productBrandQueryDto.getQuoteId());
        assertEquals(selectionScoreMin, productBrandQueryDto.getSelectionScoreMin());
        assertEquals(selectionScoreMax, productBrandQueryDto.getSelectionScoreMax());
        assertEquals(procurementMode, productBrandQueryDto.getProcurementMode());
        assertEquals(expiryDateBegin, productBrandQueryDto.getExpiryDateBegin());
        assertEquals(expiryDateEnd, productBrandQueryDto.getExpiryDateEnd());
        assertEquals(updateTimeBegin, productBrandQueryDto.getUpdateTimeBegin());
        assertEquals(updateTimeEnd, productBrandQueryDto.getUpdateTimeEnd());
    }

    @Test
    public void testToString() {
        // Arrange
        productBrandQueryDto.setId("123e4567");
        productBrandQueryDto.setProductCategoryId("123e4567-e89b-12d3-a456-426655440000");
        productBrandQueryDto.setBrandName("Apple");
        productBrandQueryDto.setTagName("High,Quality");
        productBrandQueryDto.setSelectionAttribute(0);
        productBrandQueryDto.setQuoteId(123);
        productBrandQueryDto.setSelectionScoreMin(new BigDecimal("1.0"));
        productBrandQueryDto.setSelectionScoreMax(new BigDecimal("10.0"));
        productBrandQueryDto.setProcurementMode("1");
        productBrandQueryDto.setExpiryDateBegin("2023-01-01");
        productBrandQueryDto.setExpiryDateEnd("2023-12-31");
        productBrandQueryDto.setUpdateTimeBegin("2023-01-01");
        productBrandQueryDto.setUpdateTimeEnd("2023-12-31");

        // Act
        String result = productBrandQueryDto.toString();

        // Assert
        assertTrue(result.contains("id=123e4567"));
        assertTrue(result.contains("productCategoryId=123e4567-e89b-12d3-a456-426655440000"));
        assertTrue(result.contains("brandName=Apple"));
        assertTrue(result.contains("tagName=High,Quality"));
        assertTrue(result.contains("selectionAttribute=0"));
        assertTrue(result.contains("quoteId=123"));
        assertTrue(result.contains("selectionScoreMin=1.0"));
        assertTrue(result.contains("selectionScoreMax=10.0"));
        assertTrue(result.contains("procurementMode=1"));
        assertTrue(result.contains("expiryDateBegin=2023-01-01"));
        assertTrue(result.contains("expiryDateEnd=2023-12-31"));
        assertTrue(result.contains("updateTimeBegin=2023-01-01"));
        assertTrue(result.contains("updateTimeEnd=2023-12-31"));
    }

    @Test
    public void testInheritedProperties() {
        // Arrange
        Integer page = 1;
        Integer size = 10;
        productBrandQueryDto.setPageNum(page);
        productBrandQueryDto.setPageSize(size);

        // Act & Assert
        assertEquals(page, productBrandQueryDto.getPageNum());
        assertEquals(size, productBrandQueryDto.getPageSize());
    }
}