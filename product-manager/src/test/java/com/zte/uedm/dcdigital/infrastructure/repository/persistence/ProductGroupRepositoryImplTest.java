package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductGroupMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ProductGroupRepositoryImplTest {

    @InjectMocks
    private ProductGroupRepositoryImpl productGroupRepository;


    @Mock
    private ProductGroupMapper productGroupMapper;

    private ProductGroupPo productGroupPo1;
    private ProductGroupPo productGroupPo2;
    private ProductGroupEntity productGroupEntity1;
    private ProductGroupEntity productGroupEntity2;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 创建模拟的 ProductGroupPo 实例
        productGroupPo1 = new ProductGroupPo();
        productGroupPo1.setId("1");
        productGroupPo1.setName("Group 1");
        productGroupPo1.setPdmModelSpec("Model 1");
        productGroupPo1.setParentId(null);
        productGroupPo1.setPathName("");
        productGroupPo1.setPathId("1");
        productGroupPo1.setCreateTime("2024-12-01");
        productGroupPo1.setUpdateTime("2024-12-01");
        productGroupPo1.setCreateBy("user1");
        productGroupPo1.setUpdateBy("user1");

        productGroupPo2 = new ProductGroupPo();
        productGroupPo2.setId("2");
        productGroupPo2.setName("Group 2");
        productGroupPo2.setPdmModelSpec("Model 2");
        productGroupPo2.setParentId("1");
        productGroupPo2.setPathName("Group 1");
        productGroupPo2.setPathId("1/2");
        productGroupPo2.setCreateTime("2024-12-02");
        productGroupPo2.setUpdateTime("2024-12-02");
        productGroupPo2.setCreateBy("user2");
        productGroupPo2.setUpdateBy("user2");

        // 创建模拟的 ProductGroupEntity 实例
        productGroupEntity1 = new ProductGroupEntity();
        productGroupEntity1.setId("1");
        productGroupEntity1.setName("Group 1");
        productGroupEntity1.setPdmModelSpec("Model 1");
        productGroupEntity1.setParentId(null);
        productGroupEntity1.setPathName("");
        productGroupEntity1.setPathId("1");
        productGroupEntity1.setCreateTime("2024-12-01");
        productGroupEntity1.setUpdateTime("2024-12-01");
        productGroupEntity1.setCreateBy("user1");
        productGroupEntity1.setUpdateBy("user1");

        productGroupEntity2 = new ProductGroupEntity();
        productGroupEntity2.setId("2");
        productGroupEntity2.setName("Group 2");
        productGroupEntity2.setPdmModelSpec("Model 2");
        productGroupEntity2.setParentId("1");
        productGroupEntity2.setPathName("Group 1");
        productGroupEntity2.setPathId("1/2");
        productGroupEntity2.setCreateTime("2024-12-02");
        productGroupEntity2.setUpdateTime("2024-12-02");
        productGroupEntity2.setCreateBy("user2");
        productGroupEntity2.setUpdateBy("user2");


    }


    @Test
    public void testSelectProductGroupById() {
        when(productGroupMapper.selectById("1")).thenReturn(productGroupPo1);
        ProductGroupEntity result = productGroupRepository.selectProductGroupById("1");
        assertNotNull(result);
    }

    @Test
    public void testselectByNameAndProductCategoryId() {
        List<ProductGroupPo> productGroupPos = new ArrayList<>();
        productGroupPos.add(productGroupPo1);
        productGroupPos.add(productGroupPo2);
        when(productGroupMapper.selectByNameAndProductCategoryId("test","1")).thenReturn(productGroupPos);
        List<ProductGroupEntity> groupEntities = productGroupRepository.selectByNameAndProductCategoryId("test", "1");
        assertEquals(2,groupEntities.size());
    }

    @Test
    public void testsaveOrUpdate() {

        when(productGroupMapper.updateById(any(ProductGroupPo.class))).thenReturn(1);
        ProductGroupEntity groupEntity = productGroupRepository.saveOrUpdateProductGroup(productGroupEntity1,true);
        assertNotNull(groupEntity);
        when(productGroupMapper.insert(any(ProductGroupPo.class))).thenReturn(1);
        productGroupEntity2.setId(null);
        ProductGroupEntity groupEntity2 = productGroupRepository.saveOrUpdateProductGroup(productGroupEntity2,false);
        assertNotNull(groupEntity2);
    }

    @Test
    public void testselectProductGroupByParentId() {
        List<ProductGroupPo> productGroupPos = new ArrayList<>();
        productGroupPos.add(productGroupPo1);
        productGroupPos.add(productGroupPo2);
        when(productGroupMapper.selectByParentId("1")).thenReturn(productGroupPos);
        List<ProductGroupEntity> groupEntities = productGroupRepository.selectProductGroupByParentId("1");
        assertEquals(2,groupEntities.size());
    }

    @Test
    public void testdeleteProductGroupById() {

        when(productGroupMapper.deleteById("1")).thenReturn(1);
        int i = productGroupRepository.deleteProductGroupById("1");
        assertEquals(1,i);
    }

    @Test
    public void testqueryPorductGroups() {
        List<ProductGroupPo> productGroupPos = new ArrayList<>();
        productGroupPos.add(productGroupPo1);
        productGroupPos.add(productGroupPo2);
        ProductGroupQueryDto queryDto = new ProductGroupQueryDto();
        queryDto.setProductCategoryId("1");
        queryDto.setGroupName("2");
        when(productGroupMapper.queryProductGroups(queryDto)).thenReturn(productGroupPos);
        List<ProductGroupEntity> groupEntities = productGroupRepository.queryPorductGroups(queryDto);
        assertEquals(2,groupEntities.size());
    }



    /* Started by AICoder, pid:ebfc6q9feb715b8140fc08277093a22a1003272d */
    @Test
    public void testSelectByIds(){
        List<ProductGroupPo> productGroupPos = new ArrayList<>();
        productGroupPos.add(productGroupPo1);
        productGroupPos.add(productGroupPo2);
        List<String> ids = new ArrayList<>();
        List<ProductGroupEntity> productGroupEntities = productGroupRepository.selectByIds(ids);
        assertEquals(0, productGroupEntities.size());
        ids.add("1");
        ids.add("2");
        when(productGroupMapper.selectBatchIds(ids)).thenReturn(productGroupPos);
        List<ProductGroupEntity> productGroupEntities1 = productGroupRepository.selectByIds(ids);
        assertEquals(2, productGroupEntities1.size());
    }

    @Test
    public void testSelectProductGroupAndMaterialByGroupId(){
        List<ProductGroupPo> productGroupPos = new ArrayList<>();
        productGroupPos.add(productGroupPo1);
        when(productGroupMapper.selectProductGroupAndMaterialByGroupId("1")).thenReturn(productGroupPos);
        List<ProductGroupEntity> groupEntities = productGroupRepository.selectProductGroupAndMaterialByGroupId("1");
        assertEquals(1, groupEntities.size());
    }

    @Test
    public void selectByNameAndLevelTest() {
        productGroupRepository.selectByNameAndLevel("name","55",2);
        // 验证是否正确调用了selectList方法，并且传入了任何参数（模拟列表）
        verify(productGroupMapper).selectOne(any());
    }

    @Test
    public void selectByParentNameAndNameAndLevelTest() {
        productGroupRepository.selectByParentNameAndNameAndLevel(null,"name","55",2);
        productGroupRepository.selectByParentNameAndNameAndLevel("parent","name","55",2);
        // 验证是否正确调用了selectList方法，并且传入了任何参数（模拟列表）
        verify(productGroupMapper).selectOne(any());
    }

    @Test
    public void selectCurrentNodesAllChildNodeTest() {
        productGroupRepository.selectCurrentNodesAllChildNode(Collections.singletonList("name"));
        // 验证是否正确调用了selectList方法，并且传入了任何参数（模拟列表）
        verify(productGroupMapper).selectAllChildNodeId(any());
    }
    /* Ended by AICoder, pid:ebfc6q9feb715b8140fc08277093a22a1003272d */

    @Test
    public void queryingLeafNodeGroups() {
        List<String> stringList = productGroupRepository.queryingLeafNodeGroups("");
        assertEquals(0, stringList.size());
        productGroupRepository.queryingLeafNodeGroups("name");
        // 验证是否正确调用了selectList方法，并且传入了任何参数（模拟列表）
        verify(productGroupMapper).queryingLeafNodeGroups(any());
    }
}
