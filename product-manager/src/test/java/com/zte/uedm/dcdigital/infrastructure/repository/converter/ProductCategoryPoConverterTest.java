package com.zte.uedm.dcdigital.infrastructure.repository.converter;

/* Started by AICoder, pid:f9484ra7c2ncfe614f9909fdd0d5c69af24228c4 */
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryExtraInfoEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryExtraInfoPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;
import static org.junit.Assert.*;

@RunWith(JUnit4.class)
public class ProductCategoryPoConverterTest {

    private ProductCategoryPoConverter converter;

    @Before
    public void setUp() {
        converter = new ProductCategoryPoConverter();
    }

    @Test
    public void testConvertToProductCategoryPo_NullEntity() {
        assertNull(converter.convertToProductCategoryPo(null));
    }

    @Test
    public void testConvertToProductCategoryPo_ValidEntity() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setId("1");
        entity.setProductName("Test Category");

        ProductCategoryPo po = converter.convertToProductCategoryPo(entity);
        assertNotNull(po);
        assertEquals("1", po.getId());
        assertEquals("Test Category", po.getProductName());
    }

    @Test
    public void testConvertToProductCategoryExtraInfoPo_NullEntity() {
        assertNull(converter.convertToProductCategoryExtraInfoPo(null));
    }

    @Test
    public void testConvertToProductCategoryExtraInfoPo_ValidEntity() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setId("1");
        entity.setProductLevel("Level 1");
        entity.setProductComponent("Component A");

        ProductCategoryExtraInfoPo extraInfoPo = converter.convertToProductCategoryExtraInfoPo(entity);
        assertNotNull(extraInfoPo);
        assertEquals("1", extraInfoPo.getProductCategoryId());
        assertEquals("Level 1", extraInfoPo.getProductLevel());
        assertEquals("Component A", extraInfoPo.getProductComponent());
    }

    @Test
    public void testConvertToProductCategoryEntity_NullPo() {
        assertNull(converter.convertToProductCategoryEntity(null));
    }

    @Test
    public void testConvertToProductCategoryEntity_ValidPo() {
        ProductCategoryPo po = new ProductCategoryPo();
        po.setId("1");
        po.setProductName("Test Category");

        ProductCategoryEntity entity = converter.convertToProductCategoryEntity(po);
        assertNotNull(entity);
        assertEquals("1", entity.getId());
        assertEquals("Test Category", entity.getProductName());
    }

    @Test
    public void testConvertToProductCategoryExtraInfoEntity_NullPo() {
        assertNull(converter.convertToProductCategoryExtraInfoEntity(null));
    }

    @Test
    public void testConvertToProductCategoryExtraInfoEntity_ValidPo() {
        ProductCategoryExtraInfoPo extraInfoPo = new ProductCategoryExtraInfoPo();
        extraInfoPo.setProductCategoryId("1");
        extraInfoPo.setProductLevel("Level 1");
        extraInfoPo.setProductComponent("Component A");

        ProductCategoryExtraInfoEntity entity = converter.convertToProductCategoryExtraInfoEntity(extraInfoPo);
        assertNotNull(entity);
        assertEquals("1", entity.getProductCategoryId());
        assertEquals("Level 1", entity.getProductLevel());
        assertEquals("Component A", entity.getProductComponent());
    }
}
/* Ended by AICoder, pid:f9484ra7c2ncfe614f9909fdd0d5c69af24228c4 */