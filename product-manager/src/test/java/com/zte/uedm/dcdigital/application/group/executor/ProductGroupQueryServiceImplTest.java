package com.zte.uedm.dcdigital.application.group.executor;

import com.zte.uedm.dcdigital.application.group.executor.impl.ProductGroupQueryServiceImpl;
import com.zte.uedm.dcdigital.domain.service.ProductGroupService;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

/* Started by AICoder, pid:ud2d20e504y0af2146220acc60255147dd926824 */
public class ProductGroupQueryServiceImplTest {

    @InjectMocks
    private ProductGroupQueryServiceImpl productGroupQueryService;

    @Mock
    private ProductGroupService productGroupService;

    private ProductGroupVo productGroupVo;

    private ProductGroupDetailVo productGroupDetailVo;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        productGroupVo = new ProductGroupVo();
        productGroupVo.setId("1");
        productGroupVo.setName("Group");
        productGroupVo.setParentId("123");

        productGroupDetailVo = new ProductGroupDetailVo();
        productGroupDetailVo.setId("1");
        productGroupDetailVo.setGroupLevel(1);
        productGroupDetailVo.setName("group");
    }

    @Test
    public void queryProductGroupsTest() {
        List<ProductGroupVo> list = new ArrayList<>();
        list.add(productGroupVo);
        Mockito.when(productGroupService.queryProductGroups(Mockito.any())).thenReturn(list);
        List<ProductGroupVo> productGroupVos = productGroupQueryService.queryProductGroups(new ProductGroupQueryDto());
        Assert.assertEquals(1, productGroupVos.size());
    }

    @Test
    public void queryProductGroupDetailTest() {
        Mockito.when(productGroupService.queryProductGroupDetail(Mockito.any())).thenReturn(productGroupDetailVo);
        ProductGroupDetailVo groupDetailVo = productGroupQueryService.queryProductGroupDetail("1");
        Assert.assertNotNull(groupDetailVo);
    }
}
/* Ended by AICoder, pid:ud2d20e504y0af2146220acc60255147dd926824 */