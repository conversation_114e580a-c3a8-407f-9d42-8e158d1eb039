package com.zte.uedm.dcdigital.application.material.impl;

/* Started by AICoder, pid:o8fc5offd64dce414f7c08c28074af8cf1a06085 */
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.product.MaterialWithExtendInfoVo;
import com.zte.uedm.dcdigital.domain.common.enums.PurchaseModeEnums;
import com.zte.uedm.dcdigital.domain.service.MaterialDomainService;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialAccurateDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialConditionQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialFuzzyDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialFuzzyQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MaterialMaintenanceQueryServiceImplTest {

    @Mock
    private MaterialDomainService materialDomainService;

    @InjectMocks
    private MaterialMaintenanceQueryServiceImpl materialMaintenanceQueryService;

    /* Started by AICoder, pid:b3fd8yc5e1y4e3314e5a0bc370a7440c9b22a956 */
    private static final int PAGE_NUM = 1;
    private static final int PAGE_SIZE = 10;

    @Before
    public void setUp() {
        // Setup any necessary configurations before each test
    }

    /* Started by AICoder, pid:wa620e65eb0ceda149e20b6970fd91130161913a */
    @Test
    public void testQueryMaterialVersionById_ValidList() {
        MaterialVersionVo mockVersionVo = new MaterialVersionVo();
        mockVersionVo.setVersion("1.0");

        List<MaterialVersionVo> mockList = Collections.singletonList(mockVersionVo);
        when(materialDomainService.queryMaterialVersionById(anyString())).thenReturn(mockList);

        List<MaterialVersionVo> result = materialMaintenanceQueryService.queryMaterialVersionById("1");

        assertEquals(1, result.size());
        assertEquals("1.0", result.get(0).getVersion());
        verify(materialDomainService).queryMaterialVersionById("1");
    }
    /* Ended by AICoder, pid:wa620e65eb0ceda149e20b6970fd91130161913a */
    /* Started by AICoder, pid:n9b84t5b69qfaae140d50bfee0748a5dac803245 */
    @Test
    public void testQueryMaterialDetailsById_ValidId() {
        String validId = "123";
        MaterialDetailVo expectedVo = new MaterialDetailVo();


        Mockito.when(materialDomainService.queryMaterialDetails(validId)).thenReturn(expectedVo);

        MaterialDetailVo result = materialMaintenanceQueryService.queryMaterialDetailsById(validId);

        assertEquals(expectedVo, result);
    }
    /* Ended by AICoder, pid:n9b84t5b69qfaae140d50bfee0748a5dac803245 */
    @Test
    public void testQueryAllSalesStatus() {

        List<String> stringList = new ArrayList<>();
        stringList.add("123");
        Mockito.when(materialDomainService.queryAllSalesStatus()).thenReturn(stringList);

        List<String> list = materialMaintenanceQueryService.queryAllSalesStatus();

        assertEquals(1, list.size());
    }

    @Test
    public void queryAllMaterialStatusest() {

        LocaleContextHolder.setLocale(Locale.CHINA);
        List<IdNameBean> idNameBeanList = materialMaintenanceQueryService.queryAllMaterialStatus();

        assertEquals(7, idNameBeanList.size());
    }
    @Test
    public void queryAllPurchaseModeTest() {

        LocaleContextHolder.setLocale(Locale.US);
        List<IdNameBean> idNameBeanList = materialMaintenanceQueryService.queryAllPurchaseMode();

        assertEquals(5, idNameBeanList.size());
    }

    @Test
    public void testPagingQueryMaterialByCondition() {
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();

        PageVO<MaterialVo> expectedPageVO = new PageVO<>(10, Arrays.asList(new MaterialVo(), new MaterialVo()));

        when(materialDomainService.pagingQueryByCondition(dto)).thenReturn(expectedPageVO);

        PageVO<MaterialVo> result = materialMaintenanceQueryService.pagingQueryMaterialByCondition(dto);

        assertEquals(expectedPageVO, result);
        verify(materialDomainService, times(1)).pagingQueryByCondition(dto);
    }

    @Test
    public void testQueryMaterialByCondition() {
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        List<MaterialVo> expectedMaterials = Arrays.asList(new MaterialVo(), new MaterialVo());

        when(materialDomainService.queryMaterialByCondition(dto)).thenReturn(expectedMaterials);

        List<MaterialVo> result = materialMaintenanceQueryService.queryMaterialByCondition(dto);

        assertEquals(expectedMaterials, result);
        verify(materialDomainService, times(1)).queryMaterialByCondition(dto);
    }

    @Test
    public void testQueryMaterialFuzzy() {
        MaterialFuzzyDto fuzzyDto = new MaterialFuzzyDto();
        fuzzyDto.setPageNum(PAGE_NUM);
        fuzzyDto.setPageSize(PAGE_SIZE);

        List<MaterialFuzzyVo> fuzzyVos = new ArrayList<>();

        MaterialFuzzyVo materialFuzzyVo = new MaterialFuzzyVo();
        materialFuzzyVo.setId("111");
        materialFuzzyVo.setMaterialStatus("1");
        materialFuzzyVo.setPurchaseMode("1");
        MaterialFuzzyVo materialFuzzyVo1 = new MaterialFuzzyVo();
        materialFuzzyVo1.setId("222");
        materialFuzzyVo1.setMaterialStatus("10");
        materialFuzzyVo1.setPurchaseMode("10");
        fuzzyVos.add(materialFuzzyVo);
        fuzzyVos.add(materialFuzzyVo1);

        when(materialDomainService.queryMaterialFuzzy(fuzzyDto)).thenReturn(fuzzyVos);

        PageVO<MaterialFuzzyVo> result = materialMaintenanceQueryService.queryMaterialFuzzy(fuzzyDto);

        assertEquals(2, result.getList().size());

        verify(materialDomainService, times(1)).queryMaterialFuzzy(fuzzyDto);
    }

    @Test
    public void testQueryMaterialFuzzyWithDefaultPagination() {
        MaterialFuzzyDto fuzzyDto = new MaterialFuzzyDto();

        List<MaterialFuzzyVo> fuzzyVos = Collections.emptyList();

        when(materialDomainService.queryMaterialFuzzy(fuzzyDto)).thenReturn(fuzzyVos);

        PageVO<MaterialFuzzyVo> result = materialMaintenanceQueryService.queryMaterialFuzzy(fuzzyDto);

        assertEquals(0, result.getTotal());
        assertEquals(PAGE_NUM, fuzzyDto.getPageNum().intValue());
        assertEquals(PAGE_SIZE, fuzzyDto.getPageSize().intValue());
        verify(materialDomainService, times(1)).queryMaterialFuzzy(fuzzyDto);
    }

    @Test
    public void testQueryMaterialAccurate() {
        MaterialAccurateDto accurateDto = new MaterialAccurateDto();
        accurateDto.setPageNum(PAGE_NUM);
        accurateDto.setPageSize(PAGE_SIZE);

        List<MaterialFuzzyVo> fuzzyVos = new ArrayList<>();
        MaterialFuzzyVo materialFuzzyVo = new MaterialFuzzyVo();
        materialFuzzyVo.setId("111");
        materialFuzzyVo.setMaterialStatus("1");
        materialFuzzyVo.setPurchaseMode("1");
        MaterialFuzzyVo materialFuzzyVo1 = new MaterialFuzzyVo();
        materialFuzzyVo1.setId("222");
        materialFuzzyVo1.setMaterialStatus("20");
        materialFuzzyVo1.setPurchaseMode("20");
        fuzzyVos.add(materialFuzzyVo);
        fuzzyVos.add(materialFuzzyVo1);

        when(materialDomainService.queryMaterialAccurate(accurateDto)).thenReturn(fuzzyVos);

        PageVO<MaterialFuzzyVo> result = materialMaintenanceQueryService.queryMaterialAccurate(accurateDto);

        assertEquals(2, result.getList().size());
        verify(materialDomainService, times(1)).queryMaterialAccurate(accurateDto);
    }

    @Test
    public void testQueryMaterialAccurateWithDefaultPagination() {
        MaterialAccurateDto accurateDto = new MaterialAccurateDto();

        List<MaterialFuzzyVo> fuzzyVos = Collections.emptyList();

        when(materialDomainService.queryMaterialAccurate(accurateDto)).thenReturn(fuzzyVos);

        PageVO<MaterialFuzzyVo> result = materialMaintenanceQueryService.queryMaterialAccurate(accurateDto);

        assertEquals(0, result.getTotal());
        assertEquals(PAGE_NUM, accurateDto.getPageNum().intValue());
        assertEquals(PAGE_SIZE, accurateDto.getPageSize().intValue());
        verify(materialDomainService, times(1)).queryMaterialAccurate(accurateDto);
    }
    /* Ended by AICoder, pid:n3fd80c5e1p4e3314e5a0bc371a7440c9b23a956 */

    @Test
    public void queryMaterialByIds() {
        Mockito.when(materialDomainService.queryByIds(Mockito.any())).thenReturn(new ArrayList<>());
        List<MaterialWithExtendInfoVo> list = materialMaintenanceQueryService.queryMaterialByIds(Mockito.any());
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void pagingFuzzyQuery() {
        MaterialVo vo = new MaterialVo();
        vo.setPurchaseModeId(PurchaseModeEnums.ECOLOGICAL.getId());
        List<MaterialVo> materialVos = Collections.singletonList(vo);
        PageVO<MaterialVo> pageInfo = new PageVO<>(0,materialVos);
        Mockito.when(materialDomainService.fuzzyQuery(Mockito.any())).thenReturn(pageInfo);
        PageVO<MaterialVo> pageVO = materialMaintenanceQueryService.pagingFuzzyQuery(new MaterialFuzzyQueryDto());
        Assert.assertEquals(1, pageVO.getList().size());
    }
}
/* Ended by AICoder, pid:o8fc5offd64dce414f7c08c28074af8cf1a06085 */
