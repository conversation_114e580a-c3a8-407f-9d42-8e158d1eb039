package com.zte.uedm.dcdigital.domain.common.enums;/* Started by AICoder, pid:bd13d535cfea8bf14e5d08e6f0ffd148aed4b404 */
import com.zte.uedm.dcdigital.domain.common.enums.BoundaryValueEnums;
import org.junit.Test;
import static org.junit.Assert.assertEquals;

import java.math.BigDecimal;

public class BoundaryValueEnumsTest {

    /**
     * 测试 MIN_VALUE 枚举值的 getValue 方法。
     */
    @Test
    public void given_MIN_VALUE_when_getValue_then_returnBigDecimalOne() {
        // 预置条件：MIN_VALUE 枚举值
        // 执行：调用 getValue 方法
        BigDecimal result = BoundaryValueEnums.MIN_VALUE.getValue();
        // 返回结果：应为 BigDecimal("1")
        assertEquals(new BigDecimal("1"), result);
    }

    /**
     * 测试 MAX_VALUE 枚举值的 getValue 方法。
     */
    @Test
    public void given_MAX_VALUE_when_getValue_then_returnBigDecimalTen() {
        // 预置条件：MAX_VALUE 枚举值
        // 执行：调用 getValue 方法
        BigDecimal result = BoundaryValueEnums.MAX_VALUE.getValue();
        // 返回结果：应为 BigDecimal("10")
        assertEquals(new BigDecimal("10"), result);
    }

    /**
     * 测试 FRACTION_VALUE 枚举值的 getValue 方法。
     */
    @Test
    public void given_FRACTION_VALUE_when_getValue_then_returnBigDecimalPointOne() {
        // 预置条件：FRACTION_VALUE 枚举值
        // 执行：调用 getValue 方法
        BigDecimal result = BoundaryValueEnums.FRACTION_VALUE.getValue();
        // 返回结果：应为 BigDecimal("0.1")
        assertEquals(new BigDecimal("0.1"), result);
    }
}

/* Ended by AICoder, pid:bd13d535cfea8bf14e5d08e6f0ffd148aed4b404 */