package com.zte.uedm.dcdigital.domain.service.impl;

/* Started by AICoder, pid:m75abs5096v0b6314cda0aa360432c66d446658f */
/* Started by AICoder, pid:dbe6b4b7d350c1914c8808839102d65556e2a36a */
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo;
import com.zte.uedm.dcdigital.common.bean.dto.ApprovalInformationDto;
import com.zte.uedm.dcdigital.common.bean.dto.MaterialDto;
import com.zte.uedm.dcdigital.common.bean.product.MaterialWithExtendInfoVo;
import com.zte.uedm.dcdigital.common.enums.ApprovalTypeEnums;
import com.zte.uedm.dcdigital.common.enums.DocumentRelateResourceTypeEnum;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.MaterialStatusEnums;
import com.zte.uedm.dcdigital.domain.common.enums.OperateEnums;
import com.zte.uedm.dcdigital.domain.common.enums.PurchaseModeEnums;
import com.zte.uedm.dcdigital.domain.common.enums.TemplateCheckResultEnum;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductCategoryStatusCode;
import com.zte.uedm.dcdigital.domain.gateway.PdmApiService;
import com.zte.uedm.dcdigital.domain.model.material.entity.*;
import com.zte.uedm.dcdigital.domain.model.material.event.MaterialReadListener;
import com.zte.uedm.dcdigital.domain.model.material.vobj.TemplateObj;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.domain.repository.*;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DemandMapper;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVersionVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.TemplateImportVo;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.process.service.ProcessService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import org.apache.commons.collections4.CollectionUtils;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class MaterialDomainServiceImplTest {

    @InjectMocks
    private MaterialDomainServiceImpl materialDomainService;

    @Mock
    private MaterialRepository materialRepository;

    @Mock
    private MaterialTemporaryRepository materialTemporaryRepository;

    @Mock
    private PdmInfoRepository pdmInfoRepository;

    @Mock
    private OthInfoRepository othSalesCodeRepository;
    /* Started by AICoder, pid:69040h3fa6fc52e14fd0090350ecab0089e6edfb */
    @Mock
    private MaterialHistoryRepository materialHistoryRepository;

    @Mock
    private AuthService authService;

    @Mock
    private SystemService systemService;
    @Mock
    private DocumentService documentService;

    @Mock
    private PdmApiService pdmApiService;

    @Mock
    private ProductGroupRepository productGroupRepository;

    @Mock
    private ProductCategoryRepository productCategoryRepository;
    @Mock
    private ProcessService processService;

    @Mock
    private ProcurementPriceHistoryRepository priceHistoryRepository;

    @Mock
    private DemandMapper demandMapper;

    /* Ended by AICoder, pid:69040h3fa6fc52e14fd0090350ecab0089e6edfb */

    private static final String APPROVAL_ID = "approval123";

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testWithdrawByApprovalId_NoMaterials() {
       when(materialTemporaryRepository.queryMaterialTemporaryByApprovalId("1")).thenReturn(Collections.emptyList());
        try {
            materialDomainService.withdrawByApprovalId("1");
        } catch (BusinessException e) {
            assertEquals(ProductCategoryStatusCode.MATERIAL_NOT_EXIST.getCode(), e.getCode());
        }
    }

    @Test
    public void testWithdrawByApprovalId_WithMaterials() {
        List<MaterialTemporaryEntity> tempEntities = new ArrayList<>();
        MaterialTemporaryEntity temporary = new MaterialTemporaryEntity();
        temporary.setId("1");
        temporary.setMaterialId("1");
        tempEntities.add(temporary);
        when(materialTemporaryRepository.queryMaterialTemporaryByApprovalId("1")).thenReturn(tempEntities);
        List<MaterialEntity> list = new ArrayList<>();
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE_APPROVAL.getId());
        list.add(materialEntity);
        MaterialEntity materialEntity2 = new MaterialEntity();
        materialEntity2.setId("2");
        materialEntity2.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE_APPROVAL.getId());
        list.add(materialEntity2);
        when(materialRepository.queryMaterialByIds(Mockito.anyList())).thenReturn(list);

        materialDomainService.withdrawByApprovalId("1");

        verify(materialRepository).batchUpdateMaterials(anyList());
        verify(materialTemporaryRepository).deleteByMaterialIds(anyList());
    }

    @Test
    public void testUpdateByApprovalInformation_NoMaterials() {
        ApprovalInformationDto informationDto = new ApprovalInformationDto();
        informationDto.setApprovalId("1");
        when(materialTemporaryRepository.queryMaterialTemporaryByApprovalId("1")).thenReturn(Collections.emptyList());
        try {
            materialDomainService.updateByApprovalInformation(informationDto);
        } catch (BusinessException e) {
            assertEquals(ProductCategoryStatusCode.MATERIAL_NOT_EXIST.getCode(), e.getCode());
        }
    }

    @Test
    public void testUpdateByApprovalInformation_InApproval() {
        ApprovalInformationDto informationDto = new ApprovalInformationDto();
        informationDto.setApprovalId("1");
        MaterialDto materialDto = new MaterialDto();
        materialDto.setId("1");
        materialDto.setCost("100.0");
        materialDto.setDeliveryDays("5");
        informationDto.setApprovalType(ApprovalTypeEnums.AVAILABLE_APPROVAL.getId());

        informationDto.setMaterial(Arrays.asList(materialDto));
        List<MaterialEntity> currentList = new ArrayList<>();
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        currentList.add(materialEntity);
        when(materialTemporaryRepository.queryMaterialTemporaryByApprovalId("1")).thenReturn(Collections.emptyList());
        when(materialRepository.queryByApprovalId("1")).thenReturn(currentList);

        materialDomainService.updateByApprovalInformation(informationDto);

        verify(materialRepository, atLeastOnce()).batchUpdateMaterials(anyList());

    }

    @Test
    public void testUpdateByApprovalInformation_Approved() {
        ApprovalInformationDto informationDto = new ApprovalInformationDto();
        informationDto.setApprovalId("1");
        informationDto.setApprovalStatus(GlobalConstants.APPROVAL_COMPLETED);
        informationDto.setApprovalType(GlobalConstants.SHELVES_APPROVAL);
        informationDto.setSubmitter("user1");
        informationDto.setApprovalTime("2023-10-01");

        MaterialDto materialDto = new MaterialDto();
        materialDto.setId("1");
        materialDto.setCost("100.0");
        materialDto.setDeliveryDays("5");
        informationDto.setMaterial(Arrays.asList(materialDto));
        List<MaterialTemporaryEntity> tempEntities = new ArrayList<>();
        MaterialTemporaryEntity temporary = new MaterialTemporaryEntity();
        temporary.setId("1");
        temporary.setMaterialId("1");
        String[] documentIds = {"1","2"};
        temporary.setDocumentIds(documentIds);
        tempEntities.add(temporary);
        when(materialTemporaryRepository.queryMaterialTemporaryByApprovalId("1")).thenReturn(tempEntities);
        List<MaterialEntity> list = new ArrayList<>();
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        list.add(materialEntity);

        when(materialRepository.queryMaterialByIds(anyList())).thenReturn(list);
        materialDomainService.updateByApprovalInformation(informationDto);
        verify(materialRepository).batchUpdateMaterials(anyList());
        verify(materialHistoryRepository).batchAddHistoryMaterial(anyList());

    }

    /* Started by AICoder, pid:z9805sa669x161314e8e080f1088f80399064d42 */
    @Test
    public void selectMaterialAccurateTest() {
        materialDomainService.selectByIds(new ArrayList<>());
        verify(materialRepository, times(1)).selectByIds(Mockito.anyList());
    }

    @Test
    public void queryMaterialFuzzyTest() {
        materialDomainService.queryMaterialFuzzy(new MaterialFuzzyDto());
        verify(materialRepository, times(1)).selectMaterialFuzzy(Mockito.any());
    }

    @Test
    public void queryMaterialAccurateTest() {
        materialDomainService.queryMaterialAccurate(new MaterialAccurateDto());
        verify(materialRepository, times(1)).selectMaterialAccurate(Mockito.any());
    }

    @Test
    public void checkUniqueTest() {
        materialDomainService.checkUnique("id","name");
        verify(materialRepository, times(1)).selectByIdAndName(Mockito.anyString(),Mockito.anyString());
    }
    /* Ended by AICoder, pid:z9805sa669x161314e8e080f1088f80399064d42 */
    /* Started by AICoder, pid:22822z3209k8db014d200bf7600d3216cae8f0f4 */
    @Test
    public void testUpdateMaterialByGroupId_AllEntitiesPresent() {
        // Mocking the repositories to return non-empty lists
        when(materialRepository.queryByGroupId("oldGroupId")).thenReturn(Arrays.asList(new MaterialEntity()));
        when(materialHistoryRepository.queryByGroupId("oldGroupId")).thenReturn(Arrays.asList(new MaterialHistoryEntity()));
        when(materialTemporaryRepository.queryByGroupId("oldGroupId")).thenReturn(Arrays.asList(new MaterialTemporaryEntity()));

        // Call the method under test
        boolean result = materialDomainService.updateMaterialByGroupId("oldGroupId", "newGroupId");

        // Verify that all batch updates were called
        verify(materialRepository).updateBatchMaterials(anyList());
        verify(materialHistoryRepository).updateBatchMaterials(anyList());
        verify(materialTemporaryRepository).updateBatchMaterials(anyList());

        // Assert the result
        assertTrue(result);
    }
    /* Ended by AICoder, pid:22822z3209k8db014d200bf7600d3216cae8f0f4 */
    @Test
    public void testUpdateByApprovalInformation_TakeDownApproved() {
        ApprovalInformationDto informationDto = new ApprovalInformationDto();
        informationDto.setApprovalId("1");
        informationDto.setApprovalStatus(GlobalConstants.APPROVAL_COMPLETED);
        informationDto.setApprovalType(GlobalConstants.REMOVAL_APPROVAL);
        informationDto.setSubmitter("user1");
        informationDto.setApprovalTime("2023-10-01");

        List<MaterialEntity> list = new ArrayList<>();
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        list.add(materialEntity);
        List<MaterialTemporaryEntity> tempEntities = new ArrayList<>();
        MaterialTemporaryEntity temporary = new MaterialTemporaryEntity();
        temporary.setId("1");
        temporary.setMaterialId("1");
        String[] documentIds = {"1","2"};
        temporary.setDocumentIds(documentIds);
        tempEntities.add(temporary);
        when(materialTemporaryRepository.queryMaterialTemporaryByApprovalId("1")).thenReturn(tempEntities);
        when(materialRepository.queryMaterialByIds(anyList())).thenReturn(list);

        materialDomainService.updateByApprovalInformation(informationDto);

        verify(materialRepository).batchUpdateMaterials(anyList());
        verify(materialTemporaryRepository).deleteByIds(anyList());
    }
    /* Ended by AICoder, pid:dbe6b4b7d350c1914c8808839102d65556e2a36a */
    /* Started by AICoder, pid:p3d38wcf7817c3f149d20841718d3c4297019d0f */

    /* Started by AICoder, pid:fa517ia844b877c140cf0b164023e72c13d5bd36 */
    @Test
    public void testPagingQueryByCondition_CustomPagination() {
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();

        dto.setPageNum(2);
        dto.setPageSize(20);

        // Mocking the repository to return an empty list
        PageVO<MaterialVo> result = materialDomainService.pagingQueryByCondition(dto);
        assertEquals(0, result.getTotal());
    }

    @Test
    public void testPagingQueryByCondition_NoGroupId() {
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        dto.setProductCategoryId("123");
        when(productGroupRepository.queryingLeafNodeGroups("123")).thenReturn(Arrays.asList("group1", "group2"));
        when(materialRepository.queryMaterialByCondition(any())).thenReturn(Collections.emptyList());
        PageVO<MaterialVo> result = materialDomainService.pagingQueryByCondition(dto);
        assertEquals(0, result.getTotal());
    }
    /* Ended by AICoder, pid:fa517ia844b877c140cf0b164023e72c13d5bd36 */

    /* Started by AICoder, pid:tc210h057cwae4914a3908a0707f96512bc98bc0 */
    @Test
    public void testQueryMaterialByCondition_SubmitOperation() {
        // Given
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        dto.setOperation(OperateEnums.SUBMIT.getId());
        dto.setProductCategoryId("id");
        MaterialVo materialVo = new MaterialVo();
        materialVo.setMaterialStatus("1");
        materialVo.setPurchaseMode("1");
        materialVo.setPdmInfoId("123456");
        List<MaterialVo> mockMaterials = Arrays.asList(materialVo);
        when(productGroupRepository.queryingLeafNodeGroups(Mockito.anyString())).thenReturn(Arrays.asList("group1", "group2"));
        when(materialRepository.queryMaterialByCondition(any())).thenReturn(mockMaterials);

        // When
        List<MaterialVo> result = materialDomainService.queryMaterialByCondition(dto);

        // Then
        assertEquals(1, result.size());

        verify(materialRepository).queryMaterialByCondition(argThat(arg ->
                arg.getMaterialStatus().contains(MaterialStatusEnums.LISTING.getId())));
    }

    @Test
    public void testQueryMaterialByCondition_DeListOperation() {
        // Given
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        dto.setOperation(OperateEnums.DE_LIST.getId());
        dto.setGroupId(Collections.singletonList("id"));
        MaterialVo materialVo = new MaterialVo();
        materialVo.setMaterialStatus("1");
        materialVo.setPurchaseMode("1");
        materialVo.setPdmInfoId("123456");
        List<MaterialVo> mockMaterials = Arrays.asList(materialVo);
        when(materialRepository.queryMaterialByCondition(any())).thenReturn(mockMaterials);
        when(productGroupRepository.selectCurrentNodesAllChildNode(Mockito.anyList())).thenReturn(Arrays.asList("group1", "group2"));
        // When
        List<MaterialVo> result = materialDomainService.queryMaterialByCondition(dto);

        // Then
        assertEquals(1, result.size());
        verify(materialRepository).queryMaterialByCondition(argThat(arg ->
                arg.getMaterialStatus().contains(MaterialStatusEnums.AVAILABLE.getId())));
    }

    @Test
    public void testQueryMaterialByCondition_OtherOperations() {
        // Given
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        dto.setGroupId(Collections.singletonList("id"));
        dto.setOperation("OTHER");
        MaterialVo materialVo = new MaterialVo();
        materialVo.setMaterialStatus("1");
        materialVo.setPurchaseMode("1");

        // When
        List<MaterialVo> result = materialDomainService.queryMaterialByCondition(dto);
        // Then
        assertEquals(0, result.size());

    }

    /* Started by AICoder, pid:27e61o7365u0989145060a97b036409a4c4594b9 */
    @Test(expected = BusinessException.class)
    public void testPdmSync_MaterialNotFound() {
        when(materialRepository.queryMaterialById("1")).thenReturn(null);
        materialDomainService.pdmSync("1","2");
    }

    @Test(expected = BusinessException.class)
    public void testPdmSync_MaterialNotAvailable() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE.getId());
        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);
        materialDomainService.pdmSync("1","2");
    }

    @Test(expected = BusinessException.class)
    public void testPdmSync_PdmInfoNotFound() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setPdmInfoId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);
        when(pdmInfoRepository.queryById("1")).thenReturn(null);
        materialDomainService.pdmSync("1","2");
    }

    @Test
    public void testPdmSync_SuccessWithoutNameChange() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setPdmInfoId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());

        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setId("1");
        pdmInfoEntity.setSalesCode("code1");

        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);
        when(pdmInfoRepository.queryBySalesCode("1")).thenReturn(pdmInfoEntity);
        when(pdmApiService.getMaterialListByCode(Collections.singletonList("code1"))).thenReturn(Collections.emptyList());

        materialDomainService.pdmSync("1","2");

        verify(materialRepository, never()).batchUpdateMaterials(anyList());
        verify(materialHistoryRepository, never()).batchAddHistoryMaterial(anyList());
    }
    @Test
    public void testPdmSync_SuccessWithNameChange() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setPdmInfoId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());

        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setId("1");
        pdmInfoEntity.setSalesCode("code1");
        pdmInfoEntity.setName("oldName");

        com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo materialVo = new com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo();
        materialVo.setPartCnName("newName");
        materialVo.setSalesStatus("active");
        materialVo.setProductionCode("prodCode");
        materialVo.setDescription("desc");
        materialVo.setUnit("unit");

        MaterialEntity relatedMaterial = new MaterialEntity();
        relatedMaterial.setId("2");
        relatedMaterial.setVersion("1");
        relatedMaterial.setPdmInfoId("1");
        relatedMaterial.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());

        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);
        when(pdmInfoRepository.queryBySalesCode("1")).thenReturn(pdmInfoEntity);
        when(pdmApiService.getMaterialListByCode(Collections.singletonList("code1"))).thenReturn(Collections.singletonList(materialVo));
        when(materialRepository.queryByPdmInfoIdAndStatus("1", MaterialStatusEnums.AVAILABLE.getId())).thenReturn(Collections.singletonList(relatedMaterial));


        materialDomainService.pdmSync("1","2");

        verify(pdmInfoRepository, times(1)).editPdmInfo(pdmInfoEntity);
        assertEquals("active", pdmInfoEntity.getSalesStatus());
        assertEquals("prodCode", pdmInfoEntity.getProductionCode());

        List<MaterialEntity> updatedMaterials = materialRepository.queryByPdmInfoIdAndStatus("1", MaterialStatusEnums.AVAILABLE.getId());
        assertEquals("2", updatedMaterials.get(0).getId());
        assertEquals("2", updatedMaterials.get(0).getVersion()); // Version should be incremented

    }
    /* Ended by AICoder, pid:27e61o7365u0989145060a97b036409a4c4594b9 */


    /* Started by AICoder, pid:k9040q3fa6vc52e14fd0090351ecab5089e4edfb */

    @Test
    public void given_addMaterial_when_addMaterial_then_addMaterial() {
        // Given
        MaterialAddDto addDto = new MaterialAddDto();
        addDto.setName("Test Material");
        addDto.setBrand("Brand A");
        addDto.setSupplier("Supplier X");
        addDto.setPurchaseMode("Direct");
        addDto.setExpirationDate("2025-12-31");
        addDto.setWarrantyPeriod("1 Year");
        addDto.setGroupId("Group A");
        addDto.setOperate(OperateEnums.SUBMIT.getId());
        when(authService.getUserId()).thenReturn("user123");

        // When
        String result = materialDomainService.addMaterial(addDto, "pdm123", null);

        // Then
        assertNotNull(result);
        verify(materialRepository, times(1)).addMaterial(any(MaterialEntity.class));
    }

    @Test
    public void given_addPdmInfo_when_addPdmInfo_then_addPdmInfo() {
        // Given
        MaterialAddDto addDto = new MaterialAddDto();
        addDto.setName("Test Product");
        addDto.setSalesCode("ABC123");
        addDto.setSalesStatus("Active");
        addDto.setUnit("Unit A");
        addDto.setDescription("Description");

        // When
        String result = materialDomainService.addPdmInfo(addDto);

        // Then
        assertNotNull(result);
        verify(pdmInfoRepository, times(1)).addPdmInfo(any(PdmInfoEntity.class));
    }

    @Test
    public void given_documentIds_when_relateDocuments_then_relateDocuments() {
        // Given
        String materialId = "material123";
        List<String> documentIds = Arrays.asList("doc1", "doc2");

        // When
        materialDomainService.relateDocuments(materialId, documentIds);

        // Then
        verify(documentService, times(1)).relateDocuments(eq(materialId), eq(documentIds), eq(DocumentRelateResourceTypeEnum.MATERIAL.getCode()));

        // Given
        documentIds = Collections.emptyList();

        // When
        materialDomainService.relateDocuments(materialId, documentIds);

        // Then
        verify(documentService, times(1)).deleteByResourceId(materialId);
    }

    @Test
    public void given_materialId_when_deleteMaterial_then_deleteMaterial() {
        // Given
        String id = "material123";
        MaterialEntity entity = new MaterialEntity();
        entity.setOthInfoId("id");
        entity.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE.getId());
        when(materialRepository.queryMaterialById(id)).thenReturn(entity);

        // When
        materialDomainService.deleteMaterial(id);

        // Then
        verify(materialRepository, times(1)).deleteMaterial(id);
        verify(documentService, times(1)).deleteByResourceId(id);

    }

    @Test
    public void given_editDto_when_editMaterial_then_editMaterial() {
        // Given
        MaterialEditDto editDto = new MaterialEditDto();
        editDto.setId("material123");
        editDto.setOperate(OperateEnums.TEMPORARILY_STORE.getId());
        editDto.setDocumentIds(Arrays.asList("doc1", "doc2"));
        editDto.setSalesCode("CODE");
        when(authService.getUserId()).thenReturn("user123");

        MaterialEntity entity = new MaterialEntity();
        entity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());
        when(materialRepository.queryMaterialById("material123")).thenReturn(entity);

        // When
        materialDomainService.editMaterial(editDto, null);

        // Then
        verify(materialRepository, times(1)).editMaterial(any(MaterialEntity.class));
        verify(documentService, times(1)).relateDocuments(eq("material123"), anyList(), eq(DocumentRelateResourceTypeEnum.MATERIAL.getCode()));

        // Given
        editDto.setOperate(OperateEnums.SUBMIT.getId());
        entity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());

        // When
        materialDomainService.editMaterial(editDto, null);

        // Then
        verify(materialRepository, atLeastOnce()).editMaterial(any(MaterialEntity.class));
        verify(documentService, atLeastOnce()).relateDocuments(eq("material123"), anyList(), eq(DocumentRelateResourceTypeEnum.MATERIAL.getCode()));

        // Given
        editDto.setOperate(OperateEnums.SUBMIT.getId());
        entity.setMaterialStatus(MaterialStatusEnums.LISTING.getId());

        // When
        materialDomainService.editMaterial(editDto, null);

        // Then
        verify(materialRepository, atLeastOnce()).editMaterial(any(MaterialEntity.class));
        verify(materialTemporaryRepository, atLeastOnce()).addTempMaterials(any(MaterialTemporaryEntity.class));

        // Given
        editDto.setOperate(OperateEnums.SUBMIT.getId());
        entity.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE.getId());
        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setId("123456");
        when(pdmInfoRepository.queryBySalesCode(Mockito.anyString())).thenReturn(pdmInfoEntity);

        // When
        materialDomainService.editMaterial(editDto, null);

        // Then
        verify(materialRepository, atLeastOnce()).editMaterial(any(MaterialEntity.class));
        verify(materialTemporaryRepository, atLeastOnce()).addTempMaterials(any(MaterialTemporaryEntity.class));

        // Given
        editDto.setOperate("INVALID");

        // When
        try {
            materialDomainService.editMaterial(editDto, null);
            fail("Expected exception");
        } catch (BusinessException e) {
            assertEquals(ProductCategoryStatusCode.OPERATION_TYPE_NOT_SUPPORTED.getCode(),e.getCode());
        }
    }
    /* Ended by AICoder, pid:k9040q3fa6vc52e14fd0090351ecab5089e4edfb */

    @Test
    public void given_editDto_when_editMaterial_then_editMaterial1() {
        // Given
        MaterialEditDto editDto = new MaterialEditDto();
        editDto.setId("material123");
        editDto.setOperate(OperateEnums.TEMPORARILY_STORE.getId());
        editDto.setDocumentIds(Arrays.asList("doc1", "doc2"));
        when(authService.getUserId()).thenReturn("user123");

        MaterialEntity entity = new MaterialEntity();
        entity.setMaterialStatus(MaterialStatusEnums.LISTING.getId());
        when(materialRepository.queryMaterialById("material123")).thenReturn(entity);
        when(materialTemporaryRepository.selectByMaterialId("material123")).thenReturn(new MaterialTemporaryEntity());

        // When
        materialDomainService.editMaterial(editDto, null);

        // Given
        entity.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE.getId());

        // When
        materialDomainService.editMaterial(editDto, null);

        // Then


        // Given
        entity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());

        // When
        materialDomainService.editMaterial(editDto, null);

        // Then

        // Given
        entity.setMaterialStatus(MaterialStatusEnums.UNAVAILABLE_APPROVAL.getId());

        // When
        assertThrows(BusinessException.class,()->materialDomainService.editMaterial(editDto, null));
    }
    /* Started by AICoder, pid:85bcdf5db92213514104085ef1db9544bf334ba7 */
    @Test(expected = BusinessException.class)
    public void testHandlingMaterialOperation_MaterialNotFound() {
        when(materialRepository.queryMaterialById("1")).thenReturn(null);
        materialDomainService.handlingMaterialOperation("1", "1",null);
    }

    @Test
    public void testHandlingMaterialOperation_Submit() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.LISTING.getId());
        MaterialTemporaryEntity temporaryEntity = new MaterialTemporaryEntity();
        temporaryEntity.setId("1");
        temporaryEntity.setMaterialId("1");
        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);
        when(materialTemporaryRepository.queryTempMaterialByMaterialId("1")).thenReturn(temporaryEntity);

        materialDomainService.handlingMaterialOperation("1", "1",null);

        verify(materialRepository, times(1)).editMaterial(any());
        verify(materialTemporaryRepository, times(1)).updateTempMaterial(any());
    }

    @Test
    public void testHandlingMaterialOperation_DeList() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);

        materialDomainService.handlingMaterialOperation("1", "5",null);

        verify(materialRepository, times(1)).editMaterial(any());

    }

    @Test
    public void testHandlingMaterialOperation_Listing() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setPdmInfoId("123456");
        materialEntity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());
        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);
        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setId("1");
        when(pdmInfoRepository.queryById(Mockito.anyString())).thenReturn(pdmInfoEntity);
        materialDomainService.handlingMaterialOperation("1", "4",0);

        verify(materialRepository, times(1)).editMaterial(any());
        verify(materialTemporaryRepository, atMost(1)).addTempMaterial(any());
    }

    @Test
    public void testHandlingMaterialOperation_CancelChange() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.LISTING.getId());
        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);

        materialDomainService.handlingMaterialOperation("1", "3",null);

        verify(materialRepository, times(1)).editMaterial(any());
        verify(materialTemporaryRepository, times(1)).deleteByMaterialIds(any());
    }

    @Test(expected = BusinessException.class)
    public void testHandlingMaterialOperation_UnsupportedOperation() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);

        materialDomainService.handlingMaterialOperation("1", "UNSUPPORTED",null);
    }

    @Test(expected = BusinessException.class)
    public void testBatchHandlingMaterial_NoMaterialsFound() {
        when(materialRepository.queryMaterialByIds(any())).thenReturn(Collections.emptyList());

        materialDomainService.batchHandlingMaterial(Arrays.asList("1", "2"), "1",null);
    }

    @Test
    public void testBatchHandlingMaterial_Submit() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setGroupId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.LISTING.getId());
        List<MaterialEntity> entities = Collections.singletonList(materialEntity);
        when(materialRepository.queryMaterialByIds(any())).thenReturn(entities);

        materialDomainService.batchHandlingMaterial(Arrays.asList("1"), "1",null);

        verify(materialRepository, times(1)).batchUpdateMaterials(any());
    }

    @Test
    public void testBatchHandlingMaterial_DeList() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setGroupId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
        List<MaterialEntity> entities = Collections.singletonList(materialEntity);
        when(materialRepository.queryMaterialByIds(any())).thenReturn(entities);

        materialDomainService.batchHandlingMaterial(Arrays.asList("1"), "5",null);

        verify(materialRepository, times(1)).batchUpdateMaterials(any());
        verify(materialTemporaryRepository, times(1)).batchAddMaterialTemporary(any());
    }

    @Test
    public void testBatchHandlingMaterial_Listing() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setGroupId("1");
        materialEntity.setPdmInfoId("123456");
        materialEntity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());
        List<MaterialEntity> entities = Collections.singletonList(materialEntity);
        when(materialRepository.queryMaterialByIds(any())).thenReturn(entities);

        materialDomainService.batchHandlingMaterial(Arrays.asList("1"), "4",0);

        verify(materialRepository, times(1)).batchUpdateMaterials(any());
        verify(materialTemporaryRepository, atMost(1)).batchAddMaterialTemporary(any());
    }

    @Test(expected = BusinessException.class)
    public void testBatchHandlingMaterial_UnsupportedOperation() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setGroupId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
        List<MaterialEntity> entities = Collections.singletonList(materialEntity);
        when(materialRepository.queryMaterialByIds(any())).thenReturn(entities);

        materialDomainService.batchHandlingMaterial(Arrays.asList("1"), "UNSUPPORTED",null);
    }
    /* Ended by AICoder, pid:85bcdf5db92213514104085ef1db9544bf334ba7 */
    /* Started by AICoder, pid:g9dd58dcfcm052914aae0bd16021042531c61a44 */
    @Test
    public void testQueryMaterialVersionById_EmptyList() {
        when(materialHistoryRepository.queryMaterialVersionById(anyString())).thenReturn(Collections.emptyList());

        materialDomainService.queryMaterialVersionById("1");

        verify(materialHistoryRepository).queryMaterialVersionById("1");
    }

    @Test
    public void testQueryMaterialVersionById_ValidList() {
        MaterialHistoryEntity mockEntity = new MaterialHistoryEntity();
        mockEntity.setId("1");
        List<MaterialHistoryEntity> mockList = Collections.singletonList(mockEntity);
        when(materialHistoryRepository.queryMaterialVersionById(anyString())).thenReturn(mockList);

        List<MaterialVersionVo> result = materialDomainService.queryMaterialVersionById("1");

        assertEquals(1, result.size());
        assertEquals(mockEntity.getVersion(), result.get(0).getVersion());
        assertEquals(mockEntity.getSubmitter(), result.get(0).getInitiator());
        assertEquals(mockEntity.getApprovalId(), result.get(0).getApprovalId());
        assertEquals(mockEntity.getApprovalTime(), result.get(0).getApprovalTime());

        verify(materialHistoryRepository).queryMaterialVersionById("1");
    }
    /* Ended by AICoder, pid:g9dd58dcfcm052914aae0bd16021042531c61a44 */
    @Test(expected = BusinessException.class)
    public void testImportTemplate_CategoryNotFound() {
        FormDataBodyPart part = mock(FormDataBodyPart.class);
        when(productCategoryRepository.queryById("1")).thenReturn(null);
        materialDomainService.importTemplate(part, "1");
    }

    @Test(expected = BusinessException.class)
    public void testImportTemplate_ExcelReadError() {
        FormDataBodyPart part = mock(FormDataBodyPart.class);
        ProductCategoryEntity category = new ProductCategoryEntity();
        category.setProductName("TestProduct");
        when(productCategoryRepository.queryById("1")).thenReturn(category);

        when(part.getValueAs(InputStream.class)).thenThrow(new RuntimeException("Excel read error"));
        materialDomainService.importTemplate(part, "1");
    }

    @Test(expected = BusinessException.class)
    public void testImportTemplate_SuccessfulImport() throws Exception {
        FormDataBodyPart part = mock(FormDataBodyPart.class);
        ProductCategoryEntity category = new ProductCategoryEntity();
        category.setProductName("TestProduct");
        when(productCategoryRepository.queryById("1")).thenReturn(category);

        MaterialReadListener listener = mock(MaterialReadListener.class);
        String excelContent = "[{\"sheet\":\"物料清单\",\"data\":[{\"productName\":\"value1\",\"groupL1\":\"value2\",\"groupL2\":\"value2\",\"materialName\":\"value2\",\"brand\":\"value2\",\"specificationModel\":\"value2\",\"purchaseMode\":\"value2\"}]}]";
        try (InputStream inputStream = new ByteArrayInputStream(excelContent.getBytes(StandardCharsets.UTF_8))) {
            when(part.getValueAs(InputStream.class)).thenReturn(inputStream);
            EasyExcel.read(inputStream, listener)
                    .head(TemplateObj.class)
                    .charset(StandardCharsets.UTF_8)
                    .sheet(1)
                    .doRead();
            List<TemplateImportVo> result = materialDomainService.importTemplate(part, "1");
        }
    }

    /* Ended by AICoder, pid:ia74djf1cc32d521418809ce9171617478d3e240 */
    /* Started by AICoder, pid:z71b4990ad9e3fb143750a2061452205c7457bd6 */
    @Test
    public void testObtainOthCheckedData_SuccessfulProcessing() {
        // Mocking dependencies
        try (MockedStatic<I18nUtil> i18nUtilMockedStatic = Mockito.mockStatic(I18nUtil.class)) {
            Locale mockLocale = Locale.ENGLISH;
            i18nUtilMockedStatic.when(I18nUtil::getLanguage).thenReturn(mockLocale);

            TemplateImportVo vo1 = new TemplateImportVo();
            vo1.setMaterialName("Brand1");
            vo1.setBrand("Brand1");
            vo1.setSalesCode("code1");
            vo1.setGroupPathName("ada/ac");
            List<TemplateImportVo> successPdmList = Arrays.asList(vo1);

            List<MaterialEntity> materialEntityList = new ArrayList<>();
            when(materialRepository.queryMaterialByOthName(anyList(),Mockito.anyString())).thenReturn(materialEntityList);

            OthInfoEntity entity = new OthInfoEntity();
            entity.setId("123");
            entity.setSalesCode("code1325");
            when(othSalesCodeRepository.queryOthSalesCodeBySalesCodeList(anyList())).thenReturn(Arrays.asList(entity));
            MaterialEntity materialEntity = new MaterialEntity();
            materialEntity.setName("asada");
            materialEntity.setOthInfoId("123");
            when(materialRepository.batchQueryByOthIds(anyList(),anyString())).thenReturn(Arrays.asList(materialEntity));
            ProductGroupEntity groupEntity1 = new ProductGroupEntity();
            groupEntity1.setName("ID1");
            groupEntity1.setPathName("ada/ac");
            List<String> othSalesCodes = Arrays.asList("code1325","code1");
            List<String> othNameList = Arrays.asList("asada","Brand1");
            Map<String, ProductGroupEntity> groupEntityMap = new HashMap<>();
            groupEntityMap.put("ada/ac",groupEntity1);
            Set<String> parentIds = new HashSet<>();
            materialDomainService.obtainOthCheckedData(successPdmList,groupEntityMap ,parentIds,othSalesCodes,new HashMap<>(),new HashMap<>(),othNameList);
        }
    }

    @Test
    public void testObtainOthCheckedData_FailProcessing() {
        // Mocking dependencies
        try (MockedStatic<I18nUtil> i18nUtilMockedStatic = Mockito.mockStatic(I18nUtil.class)) {
            Locale mockLocale = Locale.ENGLISH;
            i18nUtilMockedStatic.when(I18nUtil::getLanguage).thenReturn(mockLocale);
            when(I18nUtil.getI18n(Mockito.anyString(),any(Locale.class))).thenReturn("sos");
            when(I18nUtil.getI18nWithArgs(Mockito.anyString(),any(),any(Locale.class))).thenReturn("sos");
            TemplateImportVo vo1 = new TemplateImportVo();
            vo1.setMaterialName("Brand1");
            vo1.setBrand("Brand1");
            vo1.setSalesCode("OTH123");
            vo1.setGroupPathName("ada/ac");
            List<TemplateImportVo> successPdmList = new ArrayList<>();
            successPdmList.add(vo1);
            TemplateImportVo vo2 = new TemplateImportVo();
            vo2.setMaterialName("Brand1");
            vo2.setBrand("Brand2");
            vo2.setSalesCode("OTH123");
            vo2.setGroupPathName("ada/ad");
            successPdmList.add(vo2);
            OthInfoEntity entity = new OthInfoEntity();
            entity.setId("123");
            entity.setSalesCode("OTH123");
            when(othSalesCodeRepository.queryOthSalesCodeBySalesCodeList(anyList())).thenReturn(Collections.singletonList(entity));
            MaterialEntity materialEntity = new MaterialEntity();
            materialEntity.setName("asada");
            materialEntity.setOthInfoId("123");
            when(materialRepository.batchQueryByOthIds(anyList(),anyString())).thenReturn(Collections.singletonList(materialEntity));
            ProductGroupEntity groupEntity1 = new ProductGroupEntity();
            groupEntity1.setName("ID1");
            groupEntity1.setPathName("ada/ac");
            MaterialEntity entity1 = new MaterialEntity();
            entity1.setName("Brand1");
            when(materialRepository.queryMaterialByOthName(anyList(),Mockito.anyString())).thenReturn(Collections.singletonList(entity1));
            //物料名称已被使用
            List<String> othSalesCodes = Arrays.asList("code1325","code1");
            List<String> othNameList = new ArrayList<>();
            Map<String, List<TemplateImportVo>> nameRepeatMap = new HashMap<>();
            nameRepeatMap.put("Brand1",successPdmList);
            Set<String> parentIds = new HashSet<>();
            Map<String,List<TemplateImportVo>> pdmRepeatMap = new HashMap<>();
            pdmRepeatMap.put("OTH123",successPdmList);
            Map<String, ProductGroupEntity> groupEntityMap = new HashMap<>();
            groupEntityMap.put("ada/ad",groupEntity1);
            try {
                materialDomainService.obtainOthCheckedData(successPdmList, groupEntityMap,parentIds,othSalesCodes,pdmRepeatMap,nameRepeatMap,othNameList);
            } catch (Exception e) {
                Assert.assertNotNull(e);
            }
        }
    }
    @Test
    public void testObtainOthCheckedData_Fail_group_Processing() {
        // Mocking dependencies
        try (MockedStatic<I18nUtil> i18nUtilMockedStatic = Mockito.mockStatic(I18nUtil.class)) {
            Locale mockLocale = Locale.ENGLISH;
            i18nUtilMockedStatic.when(I18nUtil::getLanguage).thenReturn(mockLocale);
            when(I18nUtil.getI18n(Mockito.anyString(),any(Locale.class))).thenReturn("sos");
            when(I18nUtil.getI18nWithArgs(Mockito.anyString(),any(),any(Locale.class))).thenReturn("sos");
            TemplateImportVo vo1 = new TemplateImportVo();
            vo1.setMaterialName("Brand1");
            vo1.setBrand("Brand1");
            vo1.setSalesCode("OTH123");
            vo1.setGroupPathName("ada/ac");
            List<TemplateImportVo> successPdmList = new ArrayList<>();
            successPdmList.add(vo1);
            TemplateImportVo vo2 = new TemplateImportVo();
            vo2.setMaterialName("Brand2");
            vo2.setBrand("Brand2");
            vo2.setSalesCode("OTH1234");
            vo2.setGroupPathName("ada/ad");
            successPdmList.add(vo2);
            OthInfoEntity entity = new OthInfoEntity();
            entity.setId("123");
            entity.setSalesCode("OTH123");
            when(othSalesCodeRepository.queryOthSalesCodeBySalesCodeList(anyList())).thenReturn(Collections.singletonList(entity));
            MaterialEntity materialEntity = new MaterialEntity();
            materialEntity.setName("asada");
            materialEntity.setOthInfoId("123");
            when(materialRepository.batchQueryByOthIds(anyList(),anyString())).thenReturn(Collections.singletonList(materialEntity));
            ProductGroupEntity groupEntity1 = new ProductGroupEntity();
            groupEntity1.setName("ID1");
            groupEntity1.setPathName("ada/ac");
            groupEntity1.setParentId("parent123");
            MaterialEntity entity1 = new MaterialEntity();
            entity1.setName("Brand1");
            when(materialRepository.queryMaterialByOthName(anyList(),Mockito.anyString())).thenReturn(Collections.singletonList(entity1));
            //物料名称已被使用
            List<String> othSalesCodes = Arrays.asList("code1325","code1");
            List<String> othNameList = new ArrayList<>();
            Map<String, List<TemplateImportVo>> nameRepeatMap = new HashMap<>();
            nameRepeatMap.put("Brand1",successPdmList);
            Set<String> parentIds = new HashSet<>();
            parentIds.add("parent123");
            Map<String,List<TemplateImportVo>> pdmRepeatMap = new HashMap<>();
            pdmRepeatMap.put("OTH123",successPdmList);
            Map<String, ProductGroupEntity> groupEntityMap = new HashMap<>();
            groupEntityMap.put("ada/ad",groupEntity1);
            try {
                materialDomainService.obtainOthCheckedData(successPdmList, groupEntityMap,parentIds,othSalesCodes,pdmRepeatMap,nameRepeatMap,othNameList);
            } catch (Exception e) {
                Assert.assertNotNull(e);
            }
        }
    }
    /* Ended by AICoder, pid:z71b4990ad9e3fb143750a2061452205c7457bd6 */

    /* Started by AICoder, pid:4d0749e0a3z17e8149b70b918382a81f5867260a */
    @Test
    public void testObtainCheckedData_EmptyList() {
        List<TemplateImportVo> result = materialDomainService.obtainCheckedData(Collections.emptyList(), null,null,null,null);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testObtainCheckedData_SuccessfulProcessing() {
        // Mocking dependencies
        try (MockedStatic<I18nUtil> i18nUtilMockedStatic = Mockito.mockStatic(I18nUtil.class)) {
            Locale mockLocale = Locale.ENGLISH;
            i18nUtilMockedStatic.when(I18nUtil::getLanguage).thenReturn(mockLocale);

            TemplateImportVo vo1 = new TemplateImportVo();
            vo1.setBrand("Brand1");
            vo1.setSalesCode("code1");
            vo1.setGroupPathName("ada/ac");
            List<TemplateImportVo> successPdmList = Collections.singletonList(vo1);
            List<String> salesCodeList = Arrays.asList("code1","code2");

            PdmInfoEntity pdmInfoEntity1 = new PdmInfoEntity();
            pdmInfoEntity1.setId("sds");
            pdmInfoEntity1.setName("SC1");
            pdmInfoEntity1.setSalesCode("code1");

            PdmInfoEntity pdmInfoEntity2 = new PdmInfoEntity();
            pdmInfoEntity2.setId("sdss");
            pdmInfoEntity2.setName("SC2");
            pdmInfoEntity2.setSalesCode("code2");


            com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo materialVo = new com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo();
            materialVo.setSalesCode("code1");
            materialVo.setPartCnName("code1");
            when(pdmApiService.getMaterialListByCode(Mockito.anyList())).thenReturn(Collections.singletonList(materialVo));
            when(pdmInfoRepository.queryPdmInfoBySalesCodeList(anyList())).thenReturn(Arrays.asList(pdmInfoEntity1, pdmInfoEntity2));
            ProductGroupEntity groupEntity1 = new ProductGroupEntity();
            groupEntity1.setName("ID1");
            groupEntity1.setPathName("ada/ac");
            groupEntity1.setParentId("parent123");
            Map<String, ProductGroupEntity> groupEntityMap = new HashMap<>();
            groupEntityMap.put("ada/ac",groupEntity1);
            Set<String> parentIds = new HashSet<>();
            materialDomainService.obtainCheckedData(successPdmList, groupEntityMap,parentIds,salesCodeList,new HashMap<>());

        }
    }

    @Test
    public void testCheckTemplateData_FailOnGroupData() {
        try (MockedStatic<I18nUtil> i18nUtilMockedStatic = Mockito.mockStatic(I18nUtil.class)) {
            Locale mockLocale = Locale.ENGLISH;
            i18nUtilMockedStatic.when(I18nUtil::getLanguage).thenReturn(mockLocale);

            TemplateImportVo vo1 = new TemplateImportVo();
            vo1.setBrand("Brand1");
            vo1.setSalesCode("code1");
            vo1.setGroupPathName("ada/ac");
            List<TemplateImportVo> successPdmList = Collections.singletonList(vo1);
            List<String> salesCodeList = Arrays.asList("code1","code2");

            PdmInfoEntity pdmInfoEntity1 = new PdmInfoEntity();
            pdmInfoEntity1.setId("sds");
            pdmInfoEntity1.setName("SC1");
            pdmInfoEntity1.setSalesCode("code1");

            PdmInfoEntity pdmInfoEntity2 = new PdmInfoEntity();
            pdmInfoEntity2.setId("sdss");
            pdmInfoEntity2.setName("SC2");
            pdmInfoEntity2.setSalesCode("code2");


            com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo materialVo = new com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo();
            materialVo.setSalesCode("code1");
            materialVo.setPartCnName("code1");
            when(pdmApiService.getMaterialListByCode(Mockito.anyList())).thenReturn(Collections.singletonList(materialVo));
            when(pdmInfoRepository.queryPdmInfoBySalesCodeList(anyList())).thenReturn(Arrays.asList(pdmInfoEntity1, pdmInfoEntity2));
            ProductGroupEntity groupEntity1 = new ProductGroupEntity();
            groupEntity1.setName("ID1");
            groupEntity1.setPathName("ada/ac");
            groupEntity1.setParentId("parent123");
            Map<String, ProductGroupEntity> groupEntityMap = new HashMap<>();
            groupEntityMap.put("ada/ac",groupEntity1);
            Set<String> parentIds = new HashSet<>();
            parentIds.add("parent123");
            Map<String, List<TemplateImportVo>> pdmRepeatMap =  new HashMap<>();
            pdmRepeatMap.put("code1"+"Brand1",Arrays.asList(vo1,vo1));
            List<TemplateImportVo> result = materialDomainService.obtainCheckedData(successPdmList, groupEntityMap, parentIds, salesCodeList,pdmRepeatMap);


            assertTrue(result.get(0).isFail());
            assertEquals(TemplateCheckResultEnum.FAIL.getCode(), result.get(0).getCheckResult());
        }
    }

    /* Ended by AICoder, pid:4d0749e0a3z17e8149b70b918382a81f5867260a */


    /* Started by AICoder, pid:ydd9ba7d03j5a751428d089e60274c4690b03efe */
    @Test(expected = BusinessException.class)
    public void testQueryMaterialDetails_MaterialNotFound() {
        when(materialRepository.multiTableQueryById("123")).thenReturn(null);

        try {
            materialDomainService.queryMaterialDetails("123");
        } catch (BusinessException e) {
            assertEquals(StatusCode.DATA_NOT_FOUND.getCode(), e.getCode());
            throw e; // 重新抛出异常以满足JUnit 4的预期检查
        }
    }

    @Test
    public void testQueryMaterialDetails_NoDocuments() {
        MaterialVo mockMaterialVo = new MaterialVo();
        mockMaterialVo.setId("123");
        when(materialRepository.multiTableQueryById("123")).thenReturn(mockMaterialVo);
        when(authService.getUserId()).thenReturn("user123");
        List<String> list = new ArrayList<>();
        list.add("123");
        when(systemService.getEntityIdsByUserId(Mockito.anyString(),Mockito.anyInt())).thenReturn(list);
        List<DocumentInfoVo> mockDocumentInfoVos = new ArrayList<>();
        mockDocumentInfoVos.add(new DocumentInfoVo());
        when(documentService.queryByResourceId("123")).thenReturn(mockDocumentInfoVos);

        MaterialDetailVo result = materialDomainService.queryMaterialDetails("123");

        assertNotNull(result);
        assertEquals(mockMaterialVo, result.getMaterial());
        assertEquals(mockDocumentInfoVos, result.getDocumentInfoList());
    }

    @Test
    public void testQueryMaterialDetails_WithDocuments() {
        String[] documentIds = {"doc1", "doc2"};
        MaterialVo mockMaterialVo = new MaterialVo();
        mockMaterialVo.setId("123");
        mockMaterialVo.setDocumentIds(documentIds);

        when(materialRepository.multiTableQueryById("123")).thenReturn(mockMaterialVo);
        when(authService.getUserId()).thenReturn("user123");
        List<String> list = new ArrayList<>();
        list.add("123");
        when(systemService.getEntityIdsByUserId(Mockito.anyString(),Mockito.anyInt())).thenReturn(null);

        MaterialDetailVo result = materialDomainService.queryMaterialDetails("123");

        assertNotNull(result);
        assertEquals(mockMaterialVo, result.getMaterial());
        assertTrue(result.getDocumentInfoList().isEmpty()); // 因为没有调用documentService，所以应该是空的
    }
    /* Ended by AICoder, pid:ydd9ba7d03j5a751428d089e60274c4690b03efe */
    @Test
    public void testQueryAllSalesStatus() {
        List<String> list = materialDomainService.queryAllSalesStatus();
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    /* Started by AICoder, pid:x5e917d32bn32581409d0a6300bd4f7fb1303dea */

    /**
     * 测试正常情况下的批量提交操作。
     */
    @Test
    public void given_validList_when_batchSubmit_then_success() {
        // Given
        String userId = "testUser";
        when(authService.getUserId()).thenReturn(userId);

        MaterialBatchAddDto dto1 = new MaterialBatchAddDto();
        dto1.setSalesCode("180000497071");
        dto1.setGroupId("group1");
        dto1.setBrand("brand1");
        dto1.setSupplier("supplier1");
        dto1.setPurchaseMode("自研");
        dto1.setExpirationDate("2023/12/31");
        dto1.setWarrantyPeriod("1 year");
        MaterialBatchAddDto dto2 = new MaterialBatchAddDto();
        dto2.setSalesCode("OTH123456");
        dto2.setGroupId("group1");
        dto2.setBrand("brand1");
        dto2.setSupplier("supplier1");
        dto2.setPurchaseMode("自研");
        dto2.setExpirationDate("2023/12/31");
        dto2.setWarrantyPeriod("1 year");

        List<MaterialBatchAddDto> list = Arrays.asList(dto1,dto2);

        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setId("ID");
        pdmInfoEntity.setName("Test Name");
        com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo materialVo = new com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo();
        materialVo.setSalesCode("180000497071");
        materialVo.setPartCnName("code1");
        when(pdmApiService.getMaterialListByCode(Mockito.anyList())).thenReturn(Collections.singletonList(materialVo));
        // When
        materialDomainService.batchSubmit(list);
        materialDomainService.specialBatchSubmit(list);

        // Then
        verify(pdmInfoRepository, times(2)).batchAdd(anyList());
        verify(materialRepository, times(2)).batchAdd(anyList());
    }

    /**
     * 测试当PDM信息为空时的情况。
     */
    @Test(expected = BusinessException.class)
    public void given_emptyPdmInfo_when_batchSubmit_then_exception() {
        // Given
        MaterialBatchAddDto dto1 = new MaterialBatchAddDto();
        dto1.setSalesCode("18000008");
        dto1.setGroupId("1");
        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setSalesCode("123");
        pdmInfoEntity.setId("3333");
        when(pdmInfoRepository.queryPdmInfoBySalesCodeList(Mockito.anyList())).thenReturn(Collections.singletonList(pdmInfoEntity));

        List<MaterialBatchAddDto> list = Arrays.asList(dto1);

        MaterialEntity material=new MaterialEntity();
        material.setPdmInfoId("3333");
        when(materialRepository.batchQueryByPdmInfoIds(Mockito.anyList())).thenReturn(Collections.singletonList(material));
        MaterialTemporaryEntity temporary = new MaterialTemporaryEntity();
        temporary.setPdmInfoId("3333");
        when(materialTemporaryRepository.batchQueryByPdmInfoIds(Mockito.anyList())).thenReturn(Collections.singletonList(temporary));

        // When & Then
        materialDomainService.batchSubmit(list);
    }

    /**
     * 测试当输入列表为空时的情况。
     */
    @Test(expected = BusinessException.class)
    public void given_emptyList_when_batchSubmit_then_noOperation() {
        // Given
        MaterialBatchAddDto dto1 = new MaterialBatchAddDto();
        dto1.setSalesCode("18000008");
        dto1.setGroupId("1");
        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setId("ID");
        pdmInfoEntity.setName("Test Name");
        com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo materialVo = new com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo();
        materialVo.setSalesCode("18000008");

        // When
        materialDomainService.batchSubmit(Collections.singletonList(dto1));
    }
    /* Ended by AICoder, pid:x5e917d32bn32581409d0a6300bd4f7fb1303dea */
    @Test(expected = BusinessException.class)
    public void given_editDto_when_status_not_support() {
        MaterialEditDto editDto = new MaterialEditDto();
        editDto.setId("material123");
        editDto.setOperate(OperateEnums.TEMPORARILY_STORE.getId());
        editDto.setDocumentIds(Arrays.asList("doc1", "doc2"));
        editDto.setSalesCode("CODE");
        when(authService.getUserId()).thenReturn("user123");

        MaterialEntity entity = new MaterialEntity();
        entity.setMaterialStatus(MaterialStatusEnums.AVAILABLE_APPROVAL.getId());
        when(materialRepository.queryMaterialById("material123")).thenReturn(entity);
        // When
        materialDomainService.editMaterial(editDto, "othInfoId");
    }
    @Test(expected = BusinessException.class)
    public void given_editDto_when_material_not_support_submit() {
        MaterialEditDto editDto = new MaterialEditDto();
        editDto.setId("material123");
        editDto.setOperate(OperateEnums.SUBMIT.getId());
        editDto.setDocumentIds(Arrays.asList("doc1", "doc2"));
        editDto.setSalesCode("CODE");
        editDto.setApplyOth(0);
        when(authService.getUserId()).thenReturn("user123");

        MaterialEntity entity = new MaterialEntity();
        entity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
        when(materialRepository.queryMaterialById("material123")).thenReturn(entity);
        // When
        materialDomainService.editMaterial(editDto, "othInfoId");
    }
    @Test
    public void given_editDto_when_material_only_document_change() {
        MaterialEditDto editDto = new MaterialEditDto();
        editDto.setId("material123");
        editDto.setOperate(OperateEnums.SUBMIT.getId());
        editDto.setDocumentIds(Arrays.asList("doc1", "doc2"));
        editDto.setSalesCode("CODE");
        editDto.setName("name");
        when(authService.getUserId()).thenReturn("user123");

        MaterialEntity entity = new MaterialEntity();
        entity.setMaterialStatus(MaterialStatusEnums.AVAILABLE.getId());
        entity.setName("name");
        when(materialRepository.queryMaterialById("material123")).thenReturn(entity);
        // When
        materialDomainService.editMaterial(editDto, "");
    }
    @Test(expected = BusinessException.class)
    public void testBatchHandlingMaterial_Listing_error() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setGroupId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());
        List<MaterialEntity> entities = Collections.singletonList(materialEntity);
        when(materialRepository.queryMaterialByIds(any())).thenReturn(entities);
        materialDomainService.batchHandlingMaterial(Arrays.asList("1"), "4",0);
    }
    @Test(expected = BusinessException.class)
    public void testHandlingMaterialOperation_Listing_no_pdm_id() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());
        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);
        materialDomainService.handlingMaterialOperation("1", "4",0);
    }
    @Test(expected = BusinessException.class)
    public void testHandlingMaterialOperation_Listing_no_pdm() {
        MaterialEntity materialEntity = new MaterialEntity();
        materialEntity.setId("1");
        materialEntity.setPdmInfoId("123");
        materialEntity.setMaterialStatus(MaterialStatusEnums.DRAFT.getId());
        when(materialRepository.queryMaterialById("1")).thenReturn(materialEntity);
        when(pdmInfoRepository.queryById(Mockito.anyString())).thenReturn(null);
        materialDomainService.handlingMaterialOperation("1", "4",0);
    }
    @Test(expected = BusinessException.class)
    public void given_addMaterial_when_addMaterial_then_not_suporrt() {
        // Given
        MaterialAddDto addDto = new MaterialAddDto();
        addDto.setName("Test Material");
        addDto.setGroupId("Group A");
        addDto.setOperate(OperateEnums.SUBMIT.getId());
        addDto.setApplyOth(0);
        when(authService.getUserId()).thenReturn("user123");
        // When
        String result = materialDomainService.addMaterial(addDto, null, null);
    }
    /* Started by AICoder, pid:n730663b0dl214d14d4a0ac3b1b8ef13bef063cc */
    @Test
    public void testUniqueCheckForPdm_NotUsed() {
        MaterialAddDto addDto =getMaterialAddDto();
        when(pdmInfoRepository.queryBySalesCode(addDto.getSalesCode())).thenReturn(null);
        assertDoesNotThrow(() -> materialDomainService.uniqueCheckForPdm(addDto.getSalesCode(), addDto.getBrand(), null));
    }

    @Test(expected = BusinessException.class)
    public void testUniqueCheckForPdm_UsedInMainTable() {
        MaterialAddDto addDto =getMaterialAddDto();
        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setId("1");
        when(pdmInfoRepository.queryBySalesCode(addDto.getSalesCode())).thenReturn(pdmInfoEntity);
        MaterialEntity materialEntity = new MaterialEntity();

        when(materialRepository.queryByPdmInfoIdAndBrand(anyString(), anyString(), anyString()))
                .thenReturn(Collections.singletonList(materialEntity));
        materialDomainService.uniqueCheckForPdm(addDto.getSalesCode(), addDto.getBrand(), "null");
    }

    @Test(expected = BusinessException.class)
    public void testUniqueCheckForPdm_UsedInTemporaryTable() {
        MaterialAddDto addDto =getMaterialAddDto();
        PdmInfoEntity pdmInfoEntity = new PdmInfoEntity();
        pdmInfoEntity.setId("1");
        when(pdmInfoRepository.queryBySalesCode(addDto.getSalesCode())).thenReturn(pdmInfoEntity);
        when(materialRepository.queryByPdmInfoIdAndBrand(anyString(), anyString(), anyString()))
                .thenReturn(Collections.emptyList());
        when(materialTemporaryRepository.queryByPdmInfoIdAndBrand(anyString(), anyString(), anyString()))
                .thenReturn(Collections.singletonList(new MaterialTemporaryEntity()));

        materialDomainService.uniqueCheckForPdm(addDto.getSalesCode(), addDto.getBrand(), "null");
    }
    @Test(expected = BusinessException.class)
    public void testNonPdmNameUniqueCheck_NameExists() {
        when(materialRepository.checkNonPdmMaterialName(anyString(), anyString())).thenReturn(true);
        materialDomainService.nonPdmNameUniqueCheck("name", "id","id");
    }
    @Test
    public void testAddOrUpdateOthInfo_New() {
        MaterialAddDto addDto =getMaterialAddDto();
        when(othSalesCodeRepository.queryBySalesCode(anyString())).thenReturn(null);
        String result = materialDomainService.addOrUpdateOthInfo(addDto);
        assertNotNull(result);
        verify(othSalesCodeRepository, times(1)).addOthInfo(any(OthInfoEntity.class));
    }

    @Test
    public void testAddOrUpdateOthInfo_Update() {
        MaterialAddDto addDto =getMaterialAddDto();
        OthInfoEntity existingEntity = new OthInfoEntity();
        existingEntity.setId("existingId");
        when(othSalesCodeRepository.queryOthByName(anyString())).thenReturn(existingEntity);
        String result = materialDomainService.addOrUpdateOthInfo(addDto);
        assertEquals("existingId", result);
        verify(othSalesCodeRepository, times(1)).editOthInfo(any(OthInfoEntity.class));
    }
    public MaterialAddDto getMaterialAddDto() {
        MaterialAddDto addDto = new MaterialAddDto();
        addDto.setSalesCode("181234567890");
        addDto.setBrand("BrandA");
        addDto.setName("NameA");
        addDto.setDocumentIds(Collections.singletonList("doc1"));
        return addDto;
    }
    /* Ended by AICoder, pid:n730663b0dl214d14d4a0ac3b1b8ef13bef063cc */
    /* Started by AICoder, pid:gea70z5476te4f114c850bea1064741e665443ff */
    @Test(expected = BusinessException.class)
    public void testCheckAllData_ThrowsExceptionWhenSizeExceedsLimit() {

        List<TemplateImportVo> pdmSalesCodeList = Arrays.asList(new TemplateImportVo(), new TemplateImportVo());
        List<TemplateImportVo> othSalesCodeList = Arrays.asList(new TemplateImportVo(), new TemplateImportVo());
        /* Ended by AICoder, pid:bea7085476be4f114c850bea1064740e665243ff */
        pdmSalesCodeList = Collections.nCopies(GlobalConstants.MAX_IMPORT_NUMBER + 1, new TemplateImportVo());
        othSalesCodeList = Collections.emptyList();
        MaterialReadListener materialReadListener = new MaterialReadListener("categoryId");
        materialReadListener.setPdmSalesCodeList(pdmSalesCodeList);
        materialReadListener.setOthSalesCodeList(othSalesCodeList);
        materialDomainService.checkAllData(materialReadListener, new HashSet<>(),new HashMap<>());
    }

    @Test
    public void testCheckAllData_ReturnsCombinedList() {
        MaterialReadListener materialReadListener = new MaterialReadListener("categoryId");
        List<TemplateImportVo> result = materialDomainService.checkAllData(materialReadListener, new HashSet<>(),new HashMap<>());

        assertEquals(0, result.size());
    }
    /* Ended by AICoder, pid:gea70z5476te4f114c850bea1064741e665443ff */

    @Test
    public void queryByIds() {
        Mockito.when(materialRepository.queryMaterialWithExtendInfoByIds(Mockito.any())).thenReturn(buildMaterialEntityList());
        Mockito.when(productGroupRepository.selectByIds(Mockito.any())).thenReturn(buildProductGroupEntityList());
        List<MaterialWithExtendInfoVo> resultList = materialDomainService.queryByIds(Collections.singletonList("material1"));
        Assert.assertEquals(1, resultList.size());
    }

    @Test
    public void queryByIds_inputEmptyList() {
        List<MaterialWithExtendInfoVo> resultList = materialDomainService.queryByIds(Collections.emptyList());
        Assert.assertEquals(0, resultList.size());
    }

    @Test
    public void queryByIds_inputWrongMaterialIds() {
        Mockito.when(materialRepository.queryMaterialWithExtendInfoByIds(Mockito.any())).thenReturn(Collections.emptyList());
        List<MaterialWithExtendInfoVo> resultList = materialDomainService.queryByIds(Collections.singletonList("material1"));
        Mockito.verify(productGroupRepository, Mockito.never()).selectByIds(Mockito.any());
        Assert.assertEquals(0, resultList.size());
    }

    @Test
    public void fuzzyQuery() {
        Mockito.when(productGroupRepository.queryingLeafNodeGroups(Mockito.any())).thenReturn(Collections.singletonList("groupid"));
        MaterialFuzzyQueryDto dto = new MaterialFuzzyQueryDto();
        dto.setProductCategoryId("categoryId");
        MaterialVo vo = new MaterialVo();
        vo.setPurchaseModeId(PurchaseModeEnums.ECOLOGICAL.getId());
        List<MaterialVo> materialVos = Collections.singletonList(vo);
        PageInfo<MaterialVo> pageInfo = new PageInfo<>(materialVos);
        Mockito.when(materialRepository.fuzzyQuery(Mockito.any())).thenReturn(pageInfo);
        materialDomainService.fuzzyQuery(dto);
        Mockito.verify(materialRepository, Mockito.times(1)).fuzzyQuery(Mockito.any());
    }

    @Test
    public void fuzzyQuery_noGroupIds() {
        Mockito.when(productGroupRepository.queryingLeafNodeGroups(Mockito.any())).thenReturn(Collections.emptyList());
        materialDomainService.fuzzyQuery(new MaterialFuzzyQueryDto());
        Mockito.verify(materialRepository, Mockito.never()).fuzzyQuery(Mockito.any());
    }

    private List<ProductGroupEntity> buildProductGroupEntityList() {
        List<ProductGroupEntity> list = new ArrayList<>();
        ProductGroupEntity entity1 = new ProductGroupEntity();
        entity1.setId("group1");
        entity1.setName("groupName");
        entity1.setPathName("/all/path/groupName");
        list.add(entity1);
        return list;
    }

    private List<MaterialWithExtendInfoEntity> buildMaterialEntityList() {
        List<MaterialWithExtendInfoEntity> list = new ArrayList<>();
        MaterialWithExtendInfoEntity entity1 = new MaterialWithExtendInfoEntity();
        entity1.setId("material1");
        entity1.setGroupId("group1");
        entity1.setPurchaseMode(PurchaseModeEnums.SELF_DEV.getId());
        entity1.setPdmInfoEntity(new PdmInfoEntity());
        entity1.setOthInfoEntity(new OthInfoEntity());
        list.add(entity1);
        return list;
    }

}
/* Ended by AICoder, pid:m75abs5096v0b6314cda0aa360432c66d446658f */