package com.zte.uedm.dcdigital.application.brand;/* Started by AICoder, pid:ad7e9670d3r9c8e14e2d08ee703012736be3ad08 */
import com.zte.uedm.dcdigital.application.brand.ProductBrandCommandService;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandAddDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductBrandCommandServiceTest {

    @Mock
    private ProductBrandCommandService productBrandCommandService;

    private ProductBrandAddDto brandDto;

    @Before
    public void setUp() {
        // 初始化代码（如果需要）
        brandDto = new ProductBrandAddDto();
    }

    /**
     * 测试新增品牌信息。
     */
    @Test
    public void given_brandDto_when_addBrandAndTag_then_return1() {
        when(productBrandCommandService.addBrandAndTag(brandDto)).thenReturn(1);
        assertEquals(1, productBrandCommandService.addBrandAndTag(brandDto));
    }

    /**
     * 测试更新品牌信息。
     */
    @Test
    public void given_brandDto_when_updateBrandAndTag_then_return1() {
        when(productBrandCommandService.updateBrandAndTag(brandDto)).thenReturn(1);
        assertEquals(1, productBrandCommandService.updateBrandAndTag(brandDto));
    }

    /**
     * 测试删除品牌信息。
     */
    @Test
    public void given_id_when_deleteBrandAndTag_then_return1() {
        when(productBrandCommandService.deleteBrandAndTag("1")).thenReturn(1);
        assertEquals(1, productBrandCommandService.deleteBrandAndTag("1"));
    }

    /**
     * 测试添加品牌关联文档信息。
     */
    @Test
    public void given_ids_and_documentIds_when_addBrandAssociatedDocument_then_noException() {
        doNothing().when(productBrandCommandService).addBrandAssociatedDocument("1", "1,2");
        productBrandCommandService.addBrandAssociatedDocument("1", "1,2");
        verify(productBrandCommandService, times(1)).addBrandAssociatedDocument("1", "1,2");
    }

    /**
     * 测试删除品牌关联文档信息。
     */
    @Test
    public void given_brandId_when_deleteBrandAssociatedDocument_then_noException() {
        doNothing().when(productBrandCommandService).deleteBrandAssociatedDocument("1");
        productBrandCommandService.deleteBrandAssociatedDocument("1");
        verify(productBrandCommandService, times(1)).deleteBrandAssociatedDocument("1");
    }
}

/* Ended by AICoder, pid:ad7e9670d3r9c8e14e2d08ee703012736be3ad08 */