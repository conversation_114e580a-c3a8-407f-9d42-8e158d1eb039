package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:tdc94dc1b24db3714c37081d00712e7d44a119bb */
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.model.material.entity.PdmInfoEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.PdmInfoConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.PdmInfoMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PdmInfoPo;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialAccurateDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PdmInfoRepositoryImplTest {

    @InjectMocks
    private PdmInfoRepositoryImpl pdmInfoRepository;

    @Mock
    private PdmInfoMapper pdmInfoMapper;

    private static final String VALID_ID = "validId";
    private static final String INVALID_ID = "invalidId";

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testQueryById_ValidId() {
        PdmInfoPo po = new PdmInfoPo();
        po.setId(VALID_ID);

        when(pdmInfoMapper.selectById(anyString())).thenReturn(po);

        PdmInfoEntity result = pdmInfoRepository.queryById(VALID_ID);

        assertEquals(VALID_ID, result.getId());
        verify(pdmInfoMapper, times(1)).selectById(VALID_ID);
    }

    @Test
    public void testQueryById_InvalidId() {
        when(pdmInfoMapper.selectById(anyString())).thenReturn(null);

        PdmInfoEntity result = pdmInfoRepository.queryById(INVALID_ID);

        assertNull(result);
        verify(pdmInfoMapper, times(1)).selectById(INVALID_ID);
    }

    @Test
    public void testQueryById_Exception() {
        doThrow(new RuntimeException("Database error")).when(pdmInfoMapper).selectById(anyString());

        try {
            pdmInfoRepository.queryById(VALID_ID);
        } catch (BusinessException e) {
            assertEquals("1005",String.valueOf(e.getCode()));
        }
    }
    /* Started by AICoder, pid:3b1c75bb60g403a143fd09fac004bf8b1484db58 */

    /**
     * 测试添加PDM信息。
     */
    @Test
    public void given_pdmInfoEntity_when_addPdmInfo_then_mapperIsCalled() {
        // Given
        PdmInfoEntity entity = new PdmInfoEntity();

        // When
        pdmInfoRepository.addPdmInfo(entity);

        // Then
        verify(pdmInfoMapper, times(1)).insert(any(PdmInfoPo.class));
    }

    /**
     * 测试编辑PDM信息。
     */
    @Test
    public void given_pdmInfoEntity_when_editPdmInfo_then_mapperIsCalled() {
        // Given
        PdmInfoEntity entity = new PdmInfoEntity();

        // When
        pdmInfoRepository.editPdmInfo(entity);

        // Then
        verify(pdmInfoMapper, times(1)).updateById(any(PdmInfoPo.class));
    }

    /**
     * 测试删除PDM信息。
     */
    @Test
    public void given_pdmInfoId_when_deletePdmInfo_then_mapperIsCalled() {
        // Given
        String pdmInfoId = "testId";

        // When
        pdmInfoRepository.deletePdmInfo(pdmInfoId);

        // Then
        verify(pdmInfoMapper, times(1)).deleteById(pdmInfoId);
    }

    /**
     * 测试根据销售代码查询PDM信息。
     */
    @Test
    public void given_salesCode_when_queryBySalesCode_then_returnsCorrectEntity() {
        // Given
        String salesCode = "testCode";
        PdmInfoPo pdmInfoPo = new PdmInfoPo();

        when(pdmInfoMapper.queryBySalesCode(salesCode)).thenReturn(pdmInfoPo);

        // When
        PdmInfoEntity result = pdmInfoRepository.queryBySalesCode(salesCode);

        // Then
        verify(pdmInfoMapper, times(1)).queryBySalesCode(salesCode);
        assertEquals(pdmInfoPo.getId(), result.getId());
    }

    /**
     * 测试根据销售代码查询PDM信息，但未找到的情况。
     */
    @Test
    public void given_nonexistentSalesCode_when_queryBySalesCode_then_returnsNull() {
        // Given
        String salesCode = "nonexistentCode";

        when(pdmInfoMapper.queryBySalesCode(salesCode)).thenReturn(null);

        // When
        PdmInfoEntity result = pdmInfoRepository.queryBySalesCode(salesCode);

        // Then
        assertEquals(null, result);
    }
    /* Ended by AICoder, pid:3b1c75bb60g403a143fd09fac004bf8b1484db58 */

    @Test
    public void testQueryAllSalesCode() {
        List<String> salesCodes = Arrays.asList("code1", "code2", "code3");

        when(pdmInfoMapper.queryAllSalesCode()).thenReturn(salesCodes);

        List<String> result = pdmInfoRepository.queryAllSalesCode();

        assertEquals(salesCodes, result);
        verify(pdmInfoMapper, times(1)).queryAllSalesCode();
    }

    /* Started by AICoder, pid:ad250l776a8a7ed1414e0907c02dcf493f978e1e */
    @Test
    public void given_emptyList_when_batchAdd_then_noOperation() {
        // Given
        List<PdmInfoEntity> pdmInfoEntityList = Collections.emptyList();

        // When
        pdmInfoRepository.batchAdd(pdmInfoEntityList);

        // Then
        verifyZeroInteractions(pdmInfoMapper);
    }

    /**
     * 测试当输入列表为null时的情况。
     */
    @Test
    public void given_nullList_when_batchAdd_then_noOperation() {
        // Given
        List<PdmInfoEntity> pdmInfoEntityList = null;

        // When
        pdmInfoRepository.batchAdd(pdmInfoEntityList);

        // Then
        verifyZeroInteractions(pdmInfoMapper);
    }

    /**
     * 测试正常情况下的批量添加操作。
     */
    @Test
    public void given_validList_when_batchAdd_then_performBatchInsert() {
        // Given
        PdmInfoEntity entity1 = new PdmInfoEntity();
        PdmInfoEntity entity2 = new PdmInfoEntity();
        List<PdmInfoEntity> pdmInfoEntityList = Arrays.asList(entity1, entity2);

        // Mocking conversion
        List<PdmInfoPo> pdmInfoPoList = Arrays.asList(new PdmInfoPo(), new PdmInfoPo());

        // When
        pdmInfoRepository.batchAdd(pdmInfoEntityList);
    }
    /* Ended by AICoder, pid:ad250l776a8a7ed1414e0907c02dcf493f978e1e */
    @Test
    public void queryAllSalesStatusTest() {
        pdmInfoRepository.queryAllSalesStatus();
        verify(pdmInfoMapper, times(1)).queryAllSalesStatus();
    }
    /* Started by AICoder, pid:i4bfct50e35b7cc145450be130d5b04d41a73eb0 */
    @Test
    public void queryAllSalesStatus_NotEmptyTest() {
        Mockito.when(pdmInfoMapper.queryAllSalesStatus()).thenReturn(Arrays.asList("","sss","www",null));
        List<String> list = pdmInfoRepository.queryAllSalesStatus();
        Assert.assertNotNull(list);
        verify(pdmInfoMapper, times(1)).queryAllSalesStatus();
    }

    @Test
    public void batchUpdateTest() {
        pdmInfoRepository.batchUpdate(new ArrayList<>());
        verify(pdmInfoMapper, times(1)).batchUpdateByIds(Mockito.anyList());
    }
    /* Ended by AICoder, pid:i4bfct50e35b7cc145450be130d5b04d41a73eb0 */
    @Test
    public void selectPdmInfoByIdsTest() {
        List<PdmInfoEntity> pdmInfoEntities = pdmInfoRepository.selectPdmInfoByIds(new ArrayList<>());
        Assert.assertTrue(pdmInfoEntities.isEmpty());
         pdmInfoRepository.selectPdmInfoByIds(Collections.singletonList("ids"));
        verify(pdmInfoMapper, times(1)).selectBatchIds(Mockito.anyList());
    }
    @Test
    public void deletePdmInfoByIdsTest() {
        // 使用空列表调用批量查询方法
        ArrayList<String> list = new ArrayList<>();
        list.add("111");
        pdmInfoRepository.deletePdmInfoByIds(list);
        // 验证是否正确调用了selectList方法，并且传入了任何参数（模拟列表）
        verify(pdmInfoMapper).deleteBatchIds(any());
    }
    @Test
    public void queryPdmInfoBySalesCodeListTest() {
        List<PdmInfoEntity> pdmInfoEntities = pdmInfoRepository.queryPdmInfoBySalesCodeList(new ArrayList<>());
        assertTrue(pdmInfoEntities.isEmpty());
    }
    @Test
    public void queryPdmInfoBySalesCodeListTest2() {
        pdmInfoRepository.queryPdmInfoBySalesCodeList(Collections.singletonList("123"));
        verify(pdmInfoMapper, times(1)).queryPdmInfoBySalesCodeList(Mockito.anyList());
    }
}
/* Ended by AICoder, pid:tdc94dc1b24db3714c37081d00712e7d44a119bb */
