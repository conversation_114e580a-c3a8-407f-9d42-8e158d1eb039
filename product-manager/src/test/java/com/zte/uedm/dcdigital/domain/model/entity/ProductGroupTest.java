package com.zte.uedm.dcdigital.domain.model.entity;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.domain.common.utils.PojoTestUtil;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupVo;
import org.junit.Assert;
import org.junit.Test;

public class ProductGroupTest {


    /* Started by AICoder, pid:19058d2f8bv76e214bd009bac09d1e3048c9f226 */
    @Test
    public void test() throws Exception {
        ProductGroupEntity groupEntity = new ProductGroupEntity();
        PojoTestUtil.TestForPojo(ProductGroupEntity.class);
        Assert.assertNotEquals("", groupEntity.toString());

        groupEntity.setId("id");
        Assert.assertNotNull(groupEntity.getId());

        ProductGroupPo groupPo = new ProductGroupPo();
        PojoTestUtil.TestForPojo(ProductGroupPo.class);
        Assert.assertNotEquals("", groupPo.toString());
        groupPo.setId("id");
        Assert.assertNotNull(groupPo.getId());

        ProductGroupVo groupVo = new ProductGroupVo();
        PojoTestUtil.TestForPojo(ProductGroupVo.class);
        Assert.assertNotEquals("", groupVo.toString());
        groupVo.setId("id");
        Assert.assertNotNull(groupVo.getId());

        ProductGroupAddDto productGroupAddDto = new ProductGroupAddDto();
        PojoTestUtil.TestForPojo(ProductGroupAddDto.class);
        Assert.assertNotEquals("", productGroupAddDto.toString());
        productGroupAddDto.setName("name");
        Assert.assertNotNull(productGroupAddDto.getName());

        ProductGroupEditDto productGroupEditDto = new ProductGroupEditDto();
        PojoTestUtil.TestForPojo(ProductGroupEditDto.class);
        Assert.assertNotEquals("", productGroupEditDto.toString());
        productGroupEditDto.setName("name");
        Assert.assertNotNull(productGroupEditDto.getName());

        ProductGroupQueryDto queryDto = new ProductGroupQueryDto();
        PojoTestUtil.TestForPojo(ProductGroupQueryDto.class);
        Assert.assertNotEquals("", queryDto.toString());
        queryDto.setGroupName("name");
        Assert.assertNotNull(queryDto.getGroupName());
    }
    /* Ended by AICoder, pid:19058d2f8bv76e214bd009bac09d1e3048c9f226 */


    @Test
    public void parameterVerificationTest(){
        ProductGroupEditDto editDto1 = new ProductGroupEditDto();
        try {
            editDto1.setName("123");
            editDto1.parameterVerification();
        }catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }

        ProductGroupEditDto editDto2 = new ProductGroupEditDto();
        try {
            editDto2.setName("supercalifragilisticexpialidocious supercalifragilisticexpialidocious supercalifragilisticexpialidocious");
            editDto2.setId("123");
        }catch (BusinessException e){
            Assert.assertEquals("1110",String.valueOf(e.getCode()));
        }

        ProductGroupAddDto addDto1 = new ProductGroupAddDto();
        try {
            addDto1.setName("123");
            addDto1.parameterVerification();
        }catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }

        ProductGroupAddDto addDto2 = new ProductGroupAddDto();
        try {
            addDto2.setName("supercalifragilisticexpialidocious supercalifragilisticexpialidocious supercalifragilisticexpialidocious");
            addDto2.setProductCategoryId("123");
            addDto2.parameterVerification();
        }catch (BusinessException e) {
            Assert.assertEquals("1110",String.valueOf(e.getCode()));
        }
    }
}
