package com.zte.uedm.dcdigital.application.category.impl;

/* Started by AICoder, pid:m8bb613cd9623c2146b008194103a62648252279 */
import com.zte.uedm.dcdigital.domain.service.ProductCategoryDomainService;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.UserDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductCategoryCommandServiceImplTest {

    @InjectMocks
    private ProductCategoryCommandServiceImpl productCategoryService;

    @Mock
    private ProductCategoryDomainService productCategoryDomainService;

    private ProductCategoryAddDto addDto;
    private ProductCategoryEditDto editDto;

    @Before
    public void setUp() {
        addDto = new ProductCategoryAddDto();
        addDto.setProductName("New Category");

        editDto = new ProductCategoryEditDto();
        editDto.setId("1");
        editDto.setProductName("Updated Category");
    }

    @Test
    public void testAddProductCategory() {
        doNothing().when(productCategoryDomainService).isProductCategoryNameUnique(anyString(), anyString(),anyInt(),anyString());
        doNothing().when(productCategoryDomainService).addProductCategory(any(ProductCategoryAddDto.class));
        addDto.setNodeType(2);
        addDto.setParentId("parent");
        productCategoryService.addProductCategory(addDto);
        verify(productCategoryDomainService, times(1)).isProductCategoryNameUnique(anyString(),anyString(),anyInt(),anyString());
        verify(productCategoryDomainService, times(1)).addProductCategory(addDto);
    }

    @Test
    public void testEditProductCategory() {
        doNothing().when(productCategoryDomainService).editProductCategory(any(ProductCategoryEditDto.class));

        productCategoryService.editProductCategory(editDto);

        verify(productCategoryDomainService, times(1)).editProductCategory(editDto);
    }

    @Test
    public void testDeleteProductCategoryById() {
        doNothing().when(productCategoryDomainService).deleteById(anyString());

        productCategoryService.deleteProductCategoryById("1");

        verify(productCategoryDomainService, times(1)).deleteById("1");
    }

    /* Started by AICoder, pid:jc18fn5c48yafe414ae50a8520a3850fac483567 */
    @Test
    public void testUpdateRelatedUser() {

         String id = "1";
         String nonStandardItems = "item1,item2";
         String ma = "item1,item2";
         String materialType="这是材料类型测试字符";
         List<UserDto> users = Arrays.asList(new UserDto(), new UserDto());
        // 模拟方法调用
        productCategoryService.updateRelatedUser(id, users, nonStandardItems, BigDecimal.TEN,materialType);

        // 验证方法是否被正确调用
        verify(productCategoryDomainService, times(1)).updateRelatedUser(eq(id), eq(users), eq(nonStandardItems),eq(BigDecimal.TEN),materialType);
    }
    /* Ended by AICoder, pid:jc18fn5c48yafe414ae50a8520a3850fac483567 */
}
/* Ended by AICoder, pid:m8bb613cd9623c2146b008194103a62648252279 */
