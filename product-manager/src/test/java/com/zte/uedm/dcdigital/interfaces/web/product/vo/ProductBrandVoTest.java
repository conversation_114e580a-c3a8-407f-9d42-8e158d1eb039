package com.zte.uedm.dcdigital.interfaces.web.product.vo;/* Started by AICoder, pid:ddc3a6d1a716ea4141a9082fd0c2bc8245e25b0f */
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandTagDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductBrandTagVo;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

public class ProductBrandVoTest {

    private ProductBrandVo productBrandVo;

    @Before
    public void setUp() {
        productBrandVo = new ProductBrandVo();
    }

    @Test
    public void testSettersAndGetters() {
        // 测试所有字段的设置和获取
        productBrandVo.setId("1");
        assertEquals("1", productBrandVo.getId());

        productBrandVo.setProductCategoryId("PC123");
        assertEquals("PC123", productBrandVo.getProductCategoryId());

        productBrandVo.setBrandName("Brand A");
        assertEquals("Brand A", productBrandVo.getBrandName());

        productBrandVo.setSelectionAttribute(0);
        assertEquals(Integer.valueOf(0), productBrandVo.getSelectionAttribute());

        productBrandVo.setSelectionScore(new BigDecimal("8.5"));
        assertEquals(new BigDecimal("8.5"), productBrandVo.getSelectionScore());

        productBrandVo.setProcurementMode("1");
        assertEquals("1", productBrandVo.getProcurementMode());

        productBrandVo.setExpiryDate("2025-12-31");
        assertEquals("2025-12-31", productBrandVo.getExpiryDate());

        productBrandVo.setQuoteId("Q123");
        assertEquals("Q123", productBrandVo.getQuoteId());

        productBrandVo.setCreateTime("2023-01-01 12:00:00");
        assertEquals("2023-01-01 12:00:00", productBrandVo.getCreateTime());

        productBrandVo.setUpdateTime("2023-01-02 12:00:00");
        assertEquals("2023-01-02 12:00:00", productBrandVo.getUpdateTime());

        productBrandVo.setCreateBy("user1");
        assertEquals("user1", productBrandVo.getCreateBy());

        productBrandVo.setUpdateBy("user2");
        assertEquals("user2", productBrandVo.getUpdateBy());

        // 测试标签信息集合
        ProductBrandTagVo tag1 = new ProductBrandTagVo();
        tag1.setId("T1");
        tag1.setTagName("Tag A");

        ProductBrandTagVo tag2 = new ProductBrandTagVo();
        tag2.setId("T2");
        tag2.setTagName("Tag B");

        List<ProductBrandTagVo> tagList = Arrays.asList(tag1, tag2);
        productBrandVo.setTagInfoList(tagList);
        assertEquals(tagList, productBrandVo.getTagInfoList());
    }

    @Test
    public void testToStringMethod() {
        // 设置一些字段以测试toString方法
        productBrandVo.setId("1");
        productBrandVo.setBrandName("Brand A");

        String expectedString = "ProductBrandVo(id=1, productCategoryId=null, brandName=Brand A, selectionAttribute=null, selectionScore=null, procurementMode=null, expiryDate=null, quoteId=null, createTime=null, updateTime=null, createBy=null, updateBy=null, tagInfoList=null)";
        assertEquals(expectedString, productBrandVo.toString());
    }
}

/* Ended by AICoder, pid:ddc3a6d1a716ea4141a9082fd0c2bc8245e25b0f */