package com.zte.uedm.dcdigital.domain.common.enums;/* Started by AICoder, pid:7ee29neb3bwb655149480a0550945b4751d18ba6 */
import com.zte.uedm.dcdigital.domain.common.enums.SelectionAttributeEnums;
import org.junit.Test;
import static org.junit.Assert.*;

public class SelectionAttributeEnumsTest {

    @Test
    public void testGetId() {
        assertEquals(Integer.valueOf(0), SelectionAttributeEnums.PREFERENCE.getId());
        assertEquals(Integer.valueOf(1), SelectionAttributeEnums.OTHER.getId());
    }

    @Test
    public void testGetName() {
        assertEquals("{\"zh-CN\":\"优选\",\"en-US\":\"Preference\"}", SelectionAttributeEnums.PREFERENCE.getName());
        assertEquals("{\"zh_CN\":\"其它\",\"en_US\":\"Other\"}", SelectionAttributeEnums.OTHER.getName());
    }

    @Test
    public void testGetById() {
        assertEquals(SelectionAttributeEnums.PREFERENCE, SelectionAttributeEnums.getById(0));
        assertEquals(SelectionAttributeEnums.OTHER, SelectionAttributeEnums.getById(1));
        assertNull(SelectionAttributeEnums.getById(2)); // 测试不存在的ID
    }

    @Test
    public void testIsInRange() {
        assertTrue(SelectionAttributeEnums.isInRange(0));
        assertTrue(SelectionAttributeEnums.isInRange(1));
        assertFalse(SelectionAttributeEnums.isInRange(2)); // 测试不存在的ID
        assertFalse(SelectionAttributeEnums.isInRange(null)); // 测试null值
    }

    @Test
    public void testEnumValues() {
        SelectionAttributeEnums[] values = SelectionAttributeEnums.values();
        assertEquals(2, values.length);
        assertEquals(SelectionAttributeEnums.PREFERENCE, values[0]);
        assertEquals(SelectionAttributeEnums.OTHER, values[1]);
    }
}

/* Ended by AICoder, pid:7ee29neb3bwb655149480a0550945b4751d18ba6 */