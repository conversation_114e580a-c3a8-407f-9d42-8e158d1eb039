package com.zte.uedm.dcdigital.interfaces.web.product.controller;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.udem.ft.util.FakeBranchFlag;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryRepository;
import com.zte.uedm.dcdigital.domain.repository.ProductGroupRepository;
import com.zte.uedm.dcdigital.domain.service.MaterialDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductGroupMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.persistence.ProductGroupRepositoryImpl;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import com.zte.uedm.dcdigital.uft.db.ProductGroupMapperFake;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.function.Executable;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertThrows;

public class ProductGroupUportalControllerFTest {

    @InjectMocks
    private ProductGroupUportalController productGroupUportalController;

    @Mock
    private ProductGroupMapper productGroupMapper = new ProductGroupMapperFake();

    @Resource
    private ProductGroupRepository productGroupRepository = new ProductGroupRepositoryImpl();

    @Mock
    private ProductCategoryRepository productCategoryRepository;

    @Mock
    private AuthService authService;

    @Mock
    private MaterialDomainService materialDomainService;

    @Mock
    private PermissionUtil permissionUtil;

    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
    }

    @Test
    public void EPM_38029_given_产品小类下无分组_when_查询分组_then_返回空列表(){
        //
        //given
        //产品小类id
        ProductGroupQueryDto queryDto = new ProductGroupQueryDto();
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NULL);
        //when
        BaseResult responseBean = productGroupUportalController.getGroup(queryDto);
        //then
        Assert.assertEquals(0, responseBean.getCode().intValue());

    }

    @Test
    public void EPM_38032_given_产品小类下有分组_when_查询分组_then_返回分组列表() {
        //
        //given
        //产品小类id
        ProductGroupQueryDto queryDto = new ProductGroupQueryDto();
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        ProductCategoryEntity productCategoryEntity = new ProductCategoryEntity();
        productCategoryEntity.setPathId("1");
        productCategoryEntity.setPathName("1");
        //when
        Mockito.when(productCategoryRepository.queryById(Mockito.any())).thenReturn(productCategoryEntity);
        BaseResult responseBean = productGroupUportalController.getGroup(queryDto);
        //then
        //
        Assert.assertEquals(0, responseBean.getCode().intValue());

    }

    @Test
    public void EPM_38017_given_当前分组树中不存在同名分组_when_新增分组_then_成功创建并返回新分组信息()  {
        //
        //given
        //分组名称、型号规格、父id
        /* Started by AICoder, pid:5413a638074e1851449908e490edbf12e3f5c5ae */
        ProductGroupAddDto addDto = new ProductGroupAddDto();
        addDto.setName("test");
        addDto.setProductCategoryId("123");
        addDto.setPdmModelSpec("modelSpec");
        addDto.setParentId(null);
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NULL);
        ProductCategoryEntity productCategoryEntity = new ProductCategoryEntity();
        productCategoryEntity.setPathId("1");
        productCategoryEntity.setPathName("1");
        //when
        Mockito.when(productCategoryRepository.queryById(Mockito.any())).thenReturn(productCategoryEntity);
        Mockito.when(authService.getUserId()).thenReturn("123");
        BaseResult responseBean = productGroupUportalController.add(addDto);
        //then
        Assert.assertEquals(0, responseBean.getCode().intValue());
        /* Ended by AICoder, pid:5413a638074e1851449908e490edbf12e3f5c5ae */

    }

    @Test
    public void EPM_38031_given_当前分组树中已存在同名分组_when_新增分组_then_返回错误提示(){
        //
        //given
        //分组名称、型号规格、父id
        ProductGroupAddDto addDto = new ProductGroupAddDto();
        addDto.setName("test");
        addDto.setProductCategoryId("123");
        addDto.setPdmModelSpec("modelSpec");
        addDto.setParentId(null);
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        //when
        Executable executable = () -> {
            BaseResult responseBean = productGroupUportalController.add(addDto);
        };
        assertThrows(BusinessException.class, executable);

        //then
        //
    }

    @Test
    public void EPM_38027_given_分组层级已达三层_when_尝试新增子分组_then_返回错误提示(){
        //
        //given
        //分组名称、型号规格、父id
        ProductGroupAddDto addDto = new ProductGroupAddDto();
        addDto.setName("test");
        addDto.setProductCategoryId("123");
        addDto.setPdmModelSpec("modelSpec");
        addDto.setParentId("3");
        //when
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        Executable executable = () -> {
            productGroupUportalController.add(addDto);
        };
        assertThrows(BusinessException.class, executable);

        //then
        //
    }

    @Test
    public void EPM_38015_given_分组下有物料_when_新增分组_then_物料被正确移动到新分组(){

        /* Started by AICoder, pid:9588debed9yc9fe14c92096a70ac1417a396c2f3 */
        ProductGroupAddDto addDto = new ProductGroupAddDto();
        addDto.setName("test");
        addDto.setProductCategoryId("123");
        addDto.setPdmModelSpec("modelSpec");
        addDto.setParentId(null);
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NULL);
        ProductCategoryEntity productCategoryEntity = new ProductCategoryEntity();
        productCategoryEntity.setPathId("1");
        productCategoryEntity.setPathName("1");

        Mockito.when(productCategoryRepository.queryById(Mockito.any())).thenReturn(productCategoryEntity);
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(),Mockito.any());
        Mockito.when(authService.getUserId()).thenReturn("123");
        Mockito.when(materialDomainService.updateMaterialByGroupId(Mockito.any(), Mockito.any())).thenReturn(true);

        BaseResult responseBean = productGroupUportalController.add(addDto);

        Assert.assertEquals(0, responseBean.getCode().intValue());
        /* Ended by AICoder, pid:9588debed9yc9fe14c92096a70ac1417a396c2f3 */
    }

    @Test
    public void EPM_38021_given_当前分组树中不存在同名分组_when_修改分组_then_成功更新并返回更新后的分组信息(){
        //
        //given
        //分组id、新名称、新型号规格
        /* Started by AICoder, pid:73206jb278oc115143db0b90a075101e9a301ee4 */
        ProductGroupEditDto editDto = new ProductGroupEditDto();
        editDto.setId("1");
        editDto.setName("test1");
        editDto.setPdmModelSpec("123");
        FakeBranchFlag.setFLAG("Edit");
        //when
        Mockito.when(authService.getUserId()).thenReturn("123");
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(),Mockito.any());
        BaseResult responseBean = productGroupUportalController.edit(editDto);
        //then
        Assert.assertEquals(0, responseBean.getCode().intValue());
        /* Ended by AICoder, pid:73206jb278oc115143db0b90a075101e9a301ee4 */
    }

    @Test
    public void EPM_38022_given_当前分组树中已存在同名分组_when_修改分组_then_返回错误提示(){
        //
        //given
        //分组id、新名称、新型号规格
        ProductGroupEditDto editDto = new ProductGroupEditDto();
        editDto.setId("1");
        editDto.setName("test");
        editDto.setPdmModelSpec("123");
        FakeBranchFlag.setFLAG("Edit");
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(),Mockito.any());
        //when
        Executable executable = () -> {
             productGroupUportalController.edit(editDto);
        };
        assertThrows(BusinessException.class, executable);

        //then
        //
    }

    @Test
    public void EPM_38025_given_分组下无物料且无子分组有物料_when_删除分组_then_成功删除(){
        //
        //given
        //分组id
        String id = "1";
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        //when
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(),Mockito.any());
        BaseResult responseBean = productGroupUportalController.delete(id);
        //then
        //
        Assert.assertEquals(0, responseBean.getCode().intValue());

    }

    @Test
    public void EPM_38018_given_分组下有物料或子分组有物料_when_删除分组_then_返回错误提示(){
        //
        //given
        //分组id
        String id = "1";
        FakeBranchFlag.setFLAG("Delete");
        //when
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(),Mockito.any());
        Executable executable = () -> {
            productGroupUportalController.delete(id);
        };
        assertThrows(BusinessException.class, executable);
        //then
        //

    }
}
