package com.zte.uedm.dcdigital.application.brand.impl;/* Started by AICoder, pid:sc844xa3cetd0e314d420b3ea1543740951511d5 */
import com.zte.uedm.dcdigital.application.brand.ProductBrandQueryService;
import com.zte.uedm.dcdigital.common.enums.DocumentRelateResourceTypeEnum;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.common.enums.SelectionAttributeEnums;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductBrandMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductBrandTagMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandTagPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductBrandVo;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductBrandCommandServiceImplTest {

    @InjectMocks
    private ProductBrandCommandServiceImpl productBrandCommandService;

    @Mock
    private ProductBrandMapper brandMapper;

    @Mock
    private ProductBrandTagMapper tagMapper;

    @Mock
    private AuthService authService;

    @Mock
    private DocumentService documentService;

    @Mock
    private ProductBrandQueryService queryService;

    @Mock
    private PermissionUtil permissionUtil;

    private ProductBrandAddDto brandDto;

    @Before
    public void setUp() {
        brandDto = new ProductBrandAddDto();
        brandDto.setProductCategoryId("1");
        brandDto.setBrandName("Brand A");
        brandDto.setSelectionScore(new BigDecimal("8.5"));
        brandDto.setSelectionAttribute(SelectionAttributeEnums.PREFERENCE.getId());
        brandDto.setTagName("Tag A,Tag B");
        when(authService.getUserId()).thenReturn("testUser");
    }

    /**
     * 测试新增品牌和标签。
     */
    @Test
    public void given_validBrand_when_addBrandAndTag_then_success() {
        // 模拟查询服务返回值
        when(queryService.judgingConditionBrandName(any(), any(), any())).thenReturn(true);
        when(queryService.getNewSelectScore(any(), anyInt())).thenReturn(BigDecimal.ZERO);

        // 模拟数据库操作返回值
        when(brandMapper.insertProductBrand(any(ProductBrandPo.class))).thenReturn(1);

        int result = productBrandCommandService.addBrandAndTag(brandDto);

        assertEquals(1, result);
        verify(brandMapper, times(1)).insertProductBrand(any(ProductBrandPo.class));
        verify(tagMapper, atLeastOnce()).insertProductBrandTag(any(ProductBrandTagPo.class));
    }

    /**
     * 测试新增品牌时品牌名称已存在的情况。
     */
    @Test(expected = BusinessException.class)
    public void given_existingBrandName_when_addBrandAndTag_then_throwBusinessException() {
        when(queryService.judgingConditionBrandName(any(), any(),any())).thenReturn(false);

        productBrandCommandService.addBrandAndTag(brandDto);
    }

    /**
     * 测试新增品牌时选型评分不符合要求的情况。
     */
    @Test(expected = BusinessException.class)
    public void given_invalidScore_when_addBrandAndTag_then_throwBusinessException() {
        /* Started by AICoder, pid:xae12lc743qb644143bb097b706903078cd412fa */
        when(queryService.judgingConditionBrandName(any(), any(),any())).thenReturn(true);
        when(queryService.getNewSelectScore(any(), anyInt())).thenReturn(new BigDecimal("8.0"));

        productBrandCommandService.addBrandAndTag(brandDto);
        /* Ended by AICoder, pid:xae12lc743qb644143bb097b706903078cd412fa */
    }

    /**
     * 测试更新品牌和标签。
     */
    @Test
    public void given_validBrand_when_updateBrandAndTag_then_success() {
        // Given: 设置 Mock 行为
        when(brandMapper.updateProductBrand(any(ProductBrandPo.class))).thenReturn(1);

        // When: 调用更新方法
        int result = productBrandCommandService.updateBrandAndTag(brandDto);

        // Then: 验证结果和 Mock 调用
        assertEquals(1, result);
        verify(tagMapper).deleteProductBrandTagByBrandId(brandDto.getId());
    }

    /**
     * 测试删除品牌和标签。
     */
    @Test
    public void given_brandId_when_deleteBrandAndTag_then_success() {
        when(brandMapper.selectProductBrandById(anyString())).thenReturn(new ProductBrandVo());
        when(brandMapper.deleteProductBrandById(anyString())).thenReturn(1);
        int result = productBrandCommandService.deleteBrandAndTag("1");

        assertEquals(1, result);
        verify(tagMapper, times(1)).deleteProductBrandTagByBrandId("1");
        verify(documentService, times(1)).deleteByResourceId("1");
    }

    /**
     * 测试添加品牌关联文档。
     */
    @Test
    public void given_brandIdAndDocumentIds_when_addBrandAssociatedDocument_then_success() {
        String ids = "1,2";
        productBrandCommandService.addBrandAssociatedDocument("1", ids);
        verify(documentService, times(1)).relateDocuments(
                eq("1"),
                eq(Arrays.asList("1", "2")),
                eq(DocumentRelateResourceTypeEnum.BRAND.getCode())
        );
    }

    /**
     * 测试删除品牌关联文档。
     */
    @Test
    public void given_brandId_when_deleteBrandAssociatedDocument_then_success() {
        productBrandCommandService.deleteBrandAssociatedDocument("1");

        verify(documentService, times(1)).deleteByResourceId("1");
    }
}

/* Ended by AICoder, pid:sc844xa3cetd0e314d420b3ea1543740951511d5 */